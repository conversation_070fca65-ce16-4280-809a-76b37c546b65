-- Author: <PERSON>

--Pending clusters, working version
-- Modified on 7/8/2022 to align it with the new clustering proces.
-- A cluster could be associated with more than one wave
-- 2/20/2025
-- Adding the SLA Date for better filtering on the PBI dashboard
-- Changing to one line the Daylight savings calculation
-- Changing query format(leading commas)
-- 3/7/2025
-- Adding literals for the partition and dataarea on the WHERE clause
-- 3/12/2025
-- Adding the VoicePickUser and VoicePutUser to the query
-- Adding the VoiceStatus to the query

DECLARE @ReportDays INT = 20;

SELECT 
	PendCL.CLUSTERID		AS [ClusterId]
	, PendCL.CreatedBy      AS [CreatedBy]
	-- PendCL.CreatedOn AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS [CreatedOn]
	, FORMAT(PendCL.CreatedOn AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 'MMM dd, yyyy hh:mmtt')  AS [CreatedOn]
	, FORMAT(PendCL.ReleasedToWH, 'MMM dd, yyyy')   AS [SLA_Date]
    , CASE 
        WHEN PutStatus IS NULL THEN CASE WHEN VoicePickUser IS NULL THEN 'N/A' ELSE 'In Progress' END 
        ELSE 
            CASE 
                WHEN PutStatus = 0 THEN 'Pending'
                WHEN PutStatus = 2 THEN 'Completed'
                WHEN PutStatus = 3 THEN 'Error' -- Not sure if this is the correct status
                WHEN PutStatus = 4 THEN 'Reset'
                WHEN PutStatus = 5 THEN 'Manually Picked'
                WHEN PutStatus = 6 THEN 'Canceled'
                ELSE 'N/A'
            END END AS [VoiceStatus]
    , COALESCE(VoicePutUser, VoicePickUser,'N/A')           AS [VoiceUser]
	, SUM(PendCL.WkPending)  		                AS WkPending
	, WKC.WkCount  			                        AS ClusterTotal
	, PendCL.CLUSTERPROFILEID                       AS ClusterProfile
    --, 
 FROM (
	SELECT 
		CT.CLUSTERID            AS ClusterID
		, CT.CLUSTERPROFILEID   AS ClusterProfileID
		, CT.CREATEDBY          AS CreatedBy
		, CT.CREATEDDATETIME    AS CreatedOn
        , MIN( vput.[STATUS] )  AS PutStatus
        , wkusr.USERNAME         AS VoicePutUser
        , (SELECT TOP 1 -- TOP 1 because there could be more than one line
        wkuser.USERNAME
        FROM HAVOICEINTEGRATIONQUEUEPICKS vip 
        LEFT JOIN WHSWORKUSER wkuser WITH (NOLOCK) ON wkuser.USERID = vip.WORKUSER AND wkuser.DATAAREAID = 'ha' AND wkuser.[PARTITION] = vip.[PARTITION] 
        WHERE 
            vip.WORKID = wt.WORKID AND vip.STATUS < 5 -- 0-Pending, 2-Completed, 4-Resets
            AND vip.DATAAREAID = 'ha' AND vip.[PARTITION] = wt.[PARTITION]
        ORDER BY vip.[STATUS]    -- 0 - Pending first
        ) AS [VoicePickUser]
		, COUNT( CL.WORKID )    AS WkPending
       /* , (
            SELECT 
                STUFF((
                    -- Subquery to concatenate 
                        SELECT ',' + vput.[STATUS]  -- Concatenate the statuses with a comma separator
                        FROM HAVOICEINTEGRATIONQUEUEPUTS vput
                        WHERE vput.WORKID = wt.WORKID  -- Filter to 
                        FOR XML PATH('')  -- Use FOR XML PATH to concatenate values into a single string
                       ), 1, 1, '') 
        )
            AS VoiceStatus  -- Use STUFF to remove the leading comma and space*/
		, CAST
			(
				(
					(
						SELECT 
							MIN(ll.CREATEDDATETIME) -- There are multiple lines per order(usually), we need to get the earliest one
						FROM 
							WHSLOADLINE ll 
						WHERE 
							ll.SHIPMENTID = wt.SHIPMENTID 
							AND ll.ORDERNUM = wt.ORDERNUM 
							AND ll.LOADID = wt.LOADID  
							AND ll.DATAAREAID = 'ha'
					) AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'
				) AS DATE
			) AS [ReleasedToWH]
	FROM 
		WHSWORKCLUSTERLINE CL
	INNER JOIN WHSWORKCLUSTERTABLE CT 	        ON CL.CLUSTERID 		= CT.CLUSTERID 		--AND CT.DATAAREAID 		= CL.DATAAREAID AND CT.[PARTITION] 		= CL.[PARTITION]
	INNER JOIN WHSWORKTABLE WT 			        ON CL.WORKID 			= WT.WORKID 		--AND WT.DATAAREAID 		= CL.DATAAREAID AND WT.[PARTITION] 		= CL.[PARTITION]
	INNER JOIN WHSCONTAINERTABLE CNTTbl         ON CNTTbl.CONTAINERID 	= WT.CONTAINERID 	--AND CNTTbl.DATAAREAID 	= WT.DATAAREAID AND CNTTbl.[PARTITION] 	= WT.[PARTITION]
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput  ON vput.WORKID          = WT.WORKID         AND vput.DATAAREAID         = WT.DATAAREAID AND vput.[PARTITION]    = WT.[PARTITION]
    LEFT JOIN WHSWORKUSER wkusr 		        ON wkusr.USERID 		= vput.WORKUSER 	AND wkusr.DATAAREAID 	    = vput.DATAAREAID AND wkusr.[PARTITION] = vput.[PARTITION]
	WHERE	
		WT.WORKSTATUS		< 2	-- Open, In progress
		AND WT.WORKTRANSTYPE	= 2 -- Sales orders
		AND WT.WORKTEMPLATECODE LIKE '4010%' AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' 
		AND WT.CREATEDDATETIME > ( GETDATE() - 10 /*@ReportDays*/ )
		AND CL.DATAAREAID 		= 'ha' AND CL.[PARTITION] 		= 5637144576
		AND CT.DATAAREAID 		= 'ha' AND CT.[PARTITION] 		= 5637144576
		AND WT.DATAAREAID 		= 'ha' AND WT.[PARTITION] 		= 5637144576
		AND CNTTbl.DATAAREAID 	= 'ha' AND CNTTbl.[PARTITION] 	= 5637144576
		--AND CT.CLUSTERID = 'CL001134674'
	GROUP BY 
		CT.CLUSTERID, CT.CLUSTERPROFILEID, CT.CREATEDBY, CT.CREATEDDATETIME--, CAST( WT.CREATEDDATETIME AS DATE ), CAST( wll.CREATEDDATETIME AS DATE )
		, wt.SHIPMENTID, wt.LOADID, wt.ORDERNUM, vput.[STATUS], wt.WORKID, wt.[PARTITION], wkusr.USERNAME
) AS PendCL -- Total pending works by cluster
INNER JOIN
(
	SELECT 
		CL.CLUSTERID, COUNT( CL.WORKID ) AS WkCount
	FROM 
		WHSWORKCLUSTERLINE CL
	LEFT JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID
	WHERE 
	WT.WORKTRANSTYPE = 2 
	AND WT.WORKTEMPLATECODE LIKE '4010%' AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' 
	AND WT.CREATEDDATETIME > ( GETDATE() - @ReportDays )
	--AND CL.CLUSTERID = 'CL001134674'
	--WT.WORKTEMPLATECODE NOT LIKE 'W%' AND WT.WORKTEMPLATECODE NOT IN ('4010 Wholesale', 'Saks' ) AND WT.CREATEDDATETIME > ( GETDATE() - 20 )
	GROUP BY CL.CLUSTERID
) AS WKC -- Total work count by cluster
ON 
PendCL.ClusterID = WKC.CLUSTERID
GROUP BY 
	PendCL.CLUSTERID,  PendCL.CreatedBy, PendCL.ReleasedToWH,  PendCL.CreatedOn,  PendCL.CLUSTERPROFILEID, PendCL.WkPending, WKC.WkCount
    , PendCL.PutStatus, PendCL.VoicePickUser, PendCL.VoicePutUser
ORDER BY 
	PendCL.CLUSTERID