
SELECT 
    DATEPART( yy, wktbl.CREATEDDATETIME )   AS Year
    , cnttbl.CONTAINERID
    , COUNT( DISTINCT wktbl.WORKID ) AS WkCount
FROM
    WHSCONTAINERTABLE cnttbl
    INNER JOIN WHSWORKTABLE wktbl ON cnttbl.CONTAINERID = wktbl.CONTAINERID AND cnttbl.DATAAREAID = wktbl.DATAAREAID AND cnttbl.[PARTITION] = wktbl.[PARTITION]
WHERE
    1 = 1 
    -- AND wktbl.WORKSTATUS < 5 -- Not necessary because if the work is canceled the container is deleted
GROUP BY
    DATEPART( yy, wktbl.CREATEDDATETIME )
    , cnttbl.CONTAINERID
HAVING
    COUNT( DISTINCT wktbl.WORKID ) > 1
ORDER BY
    DATEPART( yy, wktbl.CREATEDDATETIME )  ASC
    , cnttbl.CONTAINERID
