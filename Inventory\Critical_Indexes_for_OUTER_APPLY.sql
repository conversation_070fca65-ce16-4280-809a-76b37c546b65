-- CRITICAL INDEXES FOR INVENT<PERSON>ANS AND INVENTTRANSORIGIN
-- These indexes will dramatically improve the OUTER APPLY performance
-- for finding last sales orders for items in stock

-- =============================================================================
-- INDEX 1: INVENTTRANS - Primary lookup index for OUTER APPLY
-- =============================================================================
-- This is THE MOST CRITICAL index for your query performance
-- It covers the exact lookup pattern in your OUTER APPLY

CREATE NONCLUSTERED INDEX [IX_INVENTTRANS_OUTER_APPLY_LOOKUP] 
ON [dbo].[INVENTTRANS] (
    [INVENTDIMID] ASC,
    [ITEMID] ASC,
    [DAT<PERSON>REAID] ASC,
    [PARTITION] ASC,
    [STATUSISSUE] ASC
)
INCLUDE (
    [INVENTTRANSORIGIN],
    [MODIFIEDDATETIME]
)
WITH (
    PAD_INDEX = OFF, 
    STATISTICS_NORECOMPUTE = OFF, 
    SORT_IN_TEMPDB = OFF, 
    DROP_EXISTING = OFF, 
    ONLINE = OFF, 
    ALLOW_ROW_LOCKS = ON, 
    ALLOW_PAGE_LOCKS = ON
);

-- =============================================================================
-- INDEX 2: INVENTTRANSORIGIN - Supporting index for join
-- =============================================================================
-- Supports the join from INVENTTRANS to INVENTTRANSORIGIN in OUTER APPLY

CREATE NONCLUSTERED INDEX [IX_INVENTTRANSORIGIN_RECID_CATEGORY] 
ON [dbo].[INVENTTRANSORIGIN] (
    [RECID] ASC,
    [REFERENCECATEGORY] ASC,
    [PARTITION] ASC,
    [DATAAREAID] ASC
)
INCLUDE (
    [REFERENCEID]
)
WITH (
    PAD_INDEX = OFF, 
    STATISTICS_NORECOMPUTE = OFF, 
    SORT_IN_TEMPDB = OFF, 
    DROP_EXISTING = OFF, 
    ONLINE = OFF, 
    ALLOW_ROW_LOCKS = ON, 
    ALLOW_PAGE_LOCKS = ON
);

-- =============================================================================
-- INDEX 3: INVENTSUM - Main table optimization
-- =============================================================================
-- Optimizes the main query filtering and joins

CREATE NONCLUSTERED INDEX [IX_INVENTSUM_MAIN_QUERY] 
ON [dbo].[INVENTSUM] (
    [DATAAREAID] ASC,
    [PHYSICALINVENT] ASC,
    [ITEMID] ASC
)
INCLUDE (
    [INVENTDIMID],
    [RESERVPHYSICAL],
    [MODIFIEDDATETIME],
    [RECID],
    [PARTITION]
)
WITH (
    PAD_INDEX = OFF, 
    STATISTICS_NORECOMPUTE = OFF, 
    SORT_IN_TEMPDB = OFF, 
    DROP_EXISTING = OFF, 
    ONLINE = OFF, 
    ALLOW_ROW_LOCKS = ON, 
    ALLOW_PAGE_LOCKS = ON
);

-- =============================================================================
-- INDEX 4: INVENTDIM - Dimension filtering
-- =============================================================================
-- Optimizes the inventory dimension filtering

CREATE NONCLUSTERED INDEX [IX_INVENTDIM_LOCATION_SITE] 
ON [dbo].[INVENTDIM] (
    [DATAAREAID] ASC,
    [INVENTLOCATIONID] ASC,
    [INVENTSITEID] ASC,
    [WMSLOCATIONID] ASC
)
INCLUDE (
    [INVENTDIMID],
    [INVENTCOLORID],
    [INVENTSIZEID],
    [INVENTSTATUSID],
    [PARTITION]
)
WITH (
    PAD_INDEX = OFF, 
    STATISTICS_NORECOMPUTE = OFF, 
    SORT_IN_TEMPDB = OFF, 
    DROP_EXISTING = OFF, 
    ONLINE = OFF, 
    ALLOW_ROW_LOCKS = ON, 
    ALLOW_PAGE_LOCKS = ON
);

-- =============================================================================
-- INDEX 5: WMSLOCATION - Location profile filtering
-- =============================================================================
-- Optimizes location profile filtering

CREATE NONCLUSTERED INDEX [IX_WMSLOCATION_PROFILE_FILTER] 
ON [dbo].[WMSLOCATION] (
    [DATAAREAID] ASC,
    [LOCPROFILEID] ASC,
    [WMSLOCATIONID] ASC
)
INCLUDE (
    [ZONEID],
    [PARTITION]
)
WITH (
    PAD_INDEX = OFF, 
    STATISTICS_NORECOMPUTE = OFF, 
    SORT_IN_TEMPDB = OFF, 
    DROP_EXISTING = OFF, 
    ONLINE = OFF, 
    ALLOW_ROW_LOCKS = ON, 
    ALLOW_PAGE_LOCKS = ON
);

-- =============================================================================
-- PRIORITY ORDER FOR INDEX CREATION
-- =============================================================================
/*
CREATE THESE INDEXES IN THIS ORDER FOR MAXIMUM IMPACT:

1. IX_INVENTTRANS_OUTER_APPLY_LOOKUP (HIGHEST PRIORITY)
   - This will have the biggest impact on your OUTER APPLY performance
   - Should reduce OUTER APPLY execution time by 80-90%

2. IX_INVENTTRANSORIGIN_RECID_CATEGORY (HIGH PRIORITY)
   - Supports the join in OUTER APPLY
   - Works together with index #1

3. IX_INVENTSUM_MAIN_QUERY (MEDIUM PRIORITY)
   - Improves main query filtering
   - Reduces initial result set size

4. IX_INVENTDIM_LOCATION_SITE (MEDIUM PRIORITY)
   - Improves dimension filtering
   - Reduces join overhead

5. IX_WMSLOCATION_PROFILE_FILTER (LOW PRIORITY)
   - Smallest table, least impact
   - Create last if maintenance window is limited
*/

-- =============================================================================
-- MONITORING QUERIES
-- =============================================================================

-- Check index usage after running your query
SELECT 
    i.name AS IndexName,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates,
    s.last_user_seek,
    s.last_user_scan
FROM sys.dm_db_index_usage_stats s
JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
WHERE s.database_id = DB_ID()
AND i.object_id IN (
    OBJECT_ID('INVENTTRANS'),
    OBJECT_ID('INVENTTRANSORIGIN'),
    OBJECT_ID('INVENTSUM'),
    OBJECT_ID('INVENTDIM'),
    OBJECT_ID('WMSLOCATION')
)
ORDER BY i.name;

-- Check for missing indexes (run after your query)
SELECT 
    migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) AS improvement_measure,
    'CREATE INDEX [missing_index_' + CONVERT(VARCHAR, mig.index_group_handle) + '_' + CONVERT(VARCHAR, mid.index_handle) + ']'
    + ' ON ' + mid.statement + ' (' + ISNULL(mid.equality_columns,'') 
    + CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ',' ELSE '' END
    + ISNULL(mid.inequality_columns, '') + ')' 
    + ISNULL(' INCLUDE (' + mid.included_columns + ')', '') AS create_index_statement,
    migs.*, mid.database_id, mid.[object_id]
FROM sys.dm_db_missing_index_groups mig
JOIN sys.dm_db_missing_index_group_stats migs ON migs.group_handle = mig.index_group_handle
JOIN sys.dm_db_missing_index_details mid ON mig.index_handle = mid.index_handle
WHERE migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) > 10
AND mid.database_id = DB_ID()
ORDER BY improvement_measure DESC;
