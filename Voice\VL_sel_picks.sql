SELECT TOP (1000) [pickId]
      ,[version]
      ,[assignmentId]
      ,[locationId]
      ,[itemId]
      ,[sequenceNumber]
      ,[caseLabelCheckDigit]
      ,[cartonNumber]
      ,[triggerReplenish]
      ,[unitOfMeasure]
      ,[quantityToPick]
      ,[baseItemOverride]
      ,[targetContainerIndicator]
      ,[promptMessage]
      ,[quantityPicked]
      ,[quantityAdjusted]
      ,[isBaseItem]
      ,[operatorId]
      ,[pickTime]
      ,[pickType]
      ,[status]
      ,[pickDetailCount]
      ,[originalPickId]
      ,[shortedDate]
      ,[groupNumber]
      ,[division]
      ,[store]
      ,[divisionAlphaCode]
      ,[transferNumber]
      ,[exportRetries]
      ,[createdDate]
      ,[hostPickID]
      ,[importID]
  FROM [VoiceLink].[dbo].[sel_picks]
  WHERE 
    createdDate > GETDATE() - 2