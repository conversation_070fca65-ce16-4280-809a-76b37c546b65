-- Waves processing statistics
-- <PERSON><PERSON> Anuzzi - SSNW

-- Number of transactions by date( Pacific time )

SELECT CONVERT(NVARCHAR(10),DATEADD(mi, DATEDIFF(mi, GETUTCDATE(), GETDATE()), transDateTime), 101 ) AS TransDate_PT, COUNT(*) AS TotalTans

  FROM WHSWorkCreateHistory

WHERE dataareaId = 'ha'

   AND partition = 5637144576

GROUP BY CONVERT(NVARCHAR(10),DATEADD( mi, DATEDIFF( mi, GETUTCDATE(), GETDATE()), transDateTime), 101 )

ORDER By CONVERT(NVARCHAR(10),DATEADD( mi, DATEDIFF( mi, GETUTCDATE(), GETDATE()), transDateTime), 101 ) DESC

/*
We can also see that this config change flipped our success to failure ratio. 
We are now getting more hits than misses in the location directives.
*/
 

SELECT CONVERT(NVARCHAR(10),DATEADD(mi, DATEDIFF(mi, GETUTCDATE(), GETDATE()), transDateTime),101), fail, COUNT(*)

  FROM whsWorkCreateHistory

WHERE dataareaId = 'ha'

   AND partition = 5637144576

GROUP BY CONVERT(NVARCHAR(10),DATEADD(mi, DATEDIFF(mi, GETUTCDATE(), GETDATE()), transDateTime),101), fail

ORDER BY COUNT(*) DESC

-- Testing

SELECT GETUTCDATE(), GETDATE(), DATEDIFF( mi, GETUTCDATE(), GETDATE() )

SELECT TOP 20 *
	FROM WHSWORKCREATEHISTORY