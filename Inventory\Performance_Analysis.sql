-- PERFORMANCE ANALYSIS: Let's understand why the queries are so slow
-- Run these queries to diagnose the performance issues

-- =============================================================================
-- 1. Check table sizes
-- =============================================================================

PRINT '=== TABLE SIZE ANALYSIS ===';

SELECT 
    t.name AS TableName,
    p.rows AS RowCount,
    CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS TotalSpaceMB,
    CAST(ROUND(((SUM(a.used_pages) * 8) / 1024.00), 2) AS NUMERIC(36, 2)) AS UsedSpaceMB
FROM sys.tables t
INNER JOIN sys.indexes i ON t.OBJECT_ID = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.OBJECT_ID AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE t.name IN ('INVENTTRANS', 'INVENTSUM', 'INVENTDIM', 'WMSLOCATION', 'INVENTTRANSORIGIN')
GROUP BY t.name, p.rows
ORDER BY p.rows DESC;

-- =============================================================================
-- 2. Check how many SKUs we're dealing with
-- =============================================================================

PRINT '=== SKU COUNT ANALYSIS ===';

-- Current inventory SKUs
SELECT 'Current Inventory SKUs' AS Category, COUNT(*) AS Count
FROM (
    SELECT DISTINCT isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid AS SKU
    FROM inventsum isum WITH (NOLOCK)
    JOIN inventdim idim WITH (NOLOCK) 
        ON isum.inventdimid = idim.inventdimid 
        AND isum.DATAAREAID = idim.DATAAREAID 
        AND isum.[PARTITION] = idim.[PARTITION]
        AND idim.inventlocationid = '4010'
        AND idim.INVENTSITEID = 'HA USA'
        AND idim.DATAAREAID = 'ha'
    JOIN WMSLOCATION loc WITH (NOLOCK) 
        ON loc.WMSLOCATIONID = idim.WMSLOCATIONID 
        AND loc.DATAAREAID = 'ha' 
        AND loc.[PARTITION] = isum.[PARTITION]
        AND loc.LOCPROFILEID IN ('W001', 'Picking', 'Picking A', 'Picking D', 'PalletPicking')
    WHERE 
        isum.physicalinvent <> 0 
        AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997')
        AND isum.DATAAREAID = 'ha'
) x

UNION ALL

-- Total INVENTTRANS records for last 2 years
SELECT 'INVENTTRANS Last 2 Years' AS Category, COUNT(*) AS Count
FROM INVENTTRANS WITH (NOLOCK)
WHERE DATAAREAID = 'ha' 
AND MODIFIEDDATETIME >= DATEADD(YEAR, -2, GETDATE())

UNION ALL

-- Sales transactions for last 2 years
SELECT 'Sales Transactions Last 2 Years' AS Category, COUNT(*) AS Count
FROM INVENTTRANS itrans WITH (NOLOCK)
JOIN INVENTTRANSORIGIN itorigin WITH (NOLOCK) 
    ON itrans.INVENTTRANSORIGIN = itorigin.RECID              
WHERE itrans.DATAAREAID = 'ha' 
AND itrans.STATUSISSUE = 1 -- Sold
AND itorigin.REFERENCECATEGORY = 0 -- Sales Order 
AND itrans.MODIFIEDDATETIME >= DATEADD(YEAR, -2, GETDATE());

-- =============================================================================
-- 3. Check existing indexes on critical tables
-- =============================================================================

PRINT '=== INDEX ANALYSIS ===';

SELECT 
    t.name AS TableName,
    i.name AS IndexName,
    i.type_desc AS IndexType,
    STRING_AGG(c.name, ', ') WITHIN GROUP (ORDER BY ic.key_ordinal) AS KeyColumns
FROM sys.tables t
JOIN sys.indexes i ON t.object_id = i.object_id
JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE t.name IN ('INVENTTRANS', 'INVENTSUM', 'INVENTDIM')
AND i.type > 0 -- Exclude heaps
AND ic.is_included_column = 0 -- Only key columns
GROUP BY t.name, i.name, i.type_desc
ORDER BY t.name, i.name;

-- =============================================================================
-- 4. Test the speed of just the inventory query
-- =============================================================================

PRINT '=== INVENTORY QUERY SPEED TEST ===';

DECLARE @StartTime DATETIME2 = GETDATE();

SELECT COUNT(*) AS InventoryRecordCount
FROM inventsum isum WITH (NOLOCK)
JOIN inventdim idim WITH (NOLOCK) 
    ON isum.inventdimid = idim.inventdimid 
    AND isum.DATAAREAID = idim.DATAAREAID 
    AND isum.[PARTITION] = idim.[PARTITION]
    AND idim.inventlocationid = '4010'
    AND idim.INVENTSITEID = 'HA USA'
    AND idim.DATAAREAID = 'ha'
JOIN WMSLOCATION loc WITH (NOLOCK) 
    ON loc.WMSLOCATIONID = idim.WMSLOCATIONID 
    AND loc.DATAAREAID = 'ha' 
    AND loc.[PARTITION] = isum.[PARTITION]
    AND loc.LOCPROFILEID IN ('W001', 'Picking', 'Picking A', 'Picking D', 'PalletPicking')
WHERE 
    isum.physicalinvent <> 0 
    AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997')
    AND isum.DATAAREAID = 'ha';

DECLARE @EndTime DATETIME2 = GETDATE();
PRINT 'Inventory query took: ' + CAST(DATEDIFF(MILLISECOND, @StartTime, @EndTime) AS VARCHAR(10)) + ' milliseconds';

-- =============================================================================
-- 5. Test a single SKU sales order lookup
-- =============================================================================

PRINT '=== SINGLE SKU SALES ORDER LOOKUP TEST ===';

DECLARE @TestItem NVARCHAR(20) = (SELECT TOP 1 ITEMID FROM INVENTSUM WHERE DATAAREAID = 'ha' AND PHYSICALINVENT > 0);
DECLARE @TestColor NVARCHAR(10) = (SELECT TOP 1 idim.INVENTCOLORID 
    FROM INVENTSUM isum 
    JOIN INVENTDIM idim ON isum.INVENTDIMID = idim.INVENTDIMID 
    WHERE isum.ITEMID = @TestItem AND isum.DATAAREAID = 'ha' AND isum.PHYSICALINVENT > 0);
DECLARE @TestSize NVARCHAR(10) = (SELECT TOP 1 idim.INVENTSIZEID 
    FROM INVENTSUM isum 
    JOIN INVENTDIM idim ON isum.INVENTDIMID = idim.INVENTDIMID 
    WHERE isum.ITEMID = @TestItem AND idim.INVENTCOLORID = @TestColor AND isum.DATAAREAID = 'ha' AND isum.PHYSICALINVENT > 0);

PRINT 'Testing with SKU: ' + @TestItem + '-' + @TestColor + '-' + @TestSize;

SET @StartTime = GETDATE();

SELECT TOP 1
    itorigin.REFERENCEID AS SalesOrder,
    CAST(itrans.MODIFIEDDATETIME AS DATE) AS LastSOTransaction,
    COUNT(*) OVER() AS TotalTransactionsFound
FROM INVENTTRANS itrans WITH (NOLOCK)
JOIN INVENTTRANSORIGIN itorigin WITH (NOLOCK) 
    ON itrans.INVENTTRANSORIGIN = itorigin.RECID              
    AND itorigin.[PARTITION] = itrans.[PARTITION]     
    AND itorigin.DATAAREAID = itrans.DATAAREAID
JOIN INVENTDIM idim WITH (NOLOCK)
    ON itrans.INVENTDIMID = idim.INVENTDIMID
    AND itrans.DATAAREAID = idim.DATAAREAID
    AND itrans.[PARTITION] = idim.[PARTITION]
WHERE 
    itrans.ITEMID = @TestItem
    AND idim.INVENTCOLORID = @TestColor
    AND idim.INVENTSIZEID = @TestSize
    AND itrans.STATUSISSUE = 1 -- Sold
    AND itorigin.REFERENCECATEGORY = 0 -- Sales Order 
    AND itrans.DATAAREAID = 'ha'
    AND itrans.MODIFIEDDATETIME >= DATEADD(YEAR, -2, GETDATE())
ORDER BY itrans.MODIFIEDDATETIME DESC, itrans.RECID DESC;

SET @EndTime = GETDATE();
PRINT 'Single SKU sales order lookup took: ' + CAST(DATEDIFF(MILLISECOND, @StartTime, @EndTime) AS VARCHAR(10)) + ' milliseconds';

-- =============================================================================
-- 6. Recommendations based on results
-- =============================================================================

PRINT '=== RECOMMENDATIONS ===';
PRINT '1. If INVENTTRANS has millions of records, that explains the slowness';
PRINT '2. If single SKU lookup is slow (>1000ms), indexes are the problem';
PRINT '3. If inventory query is slow (>5000ms), base query needs optimization';
PRINT '4. Consider the two-query approach for immediate results';
PRINT '5. Set up nightly refresh of sales order lookup table for production';
