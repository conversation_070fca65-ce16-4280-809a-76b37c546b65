

-- Clusters with multiples open picks from the same location

SELECT
    ct.CLUSTERID
    , ct.CLUSTERPROFILEID
    , wkln.WMSLOCATIONID
    , COUNT( * ) AS TotalPicks
FROM
    WHSWORKCLUSTERTABLE ct
    LEFT JOIN WHSWORKCLUSTERLINE cl ON ct.CLUSTERID = cl.CLUSTERID AND ct.[PARTITION] = cl.[PARTITION] AND ct.DATAAREAID = cl.DATAAREAID
    LEFT JOIN WHSWORKLINE wkln ON wkln.WORKID = cl.WORKID AND wkln.[PARTITION] = cl.[PARTITION] AND wkln.DATAAREAID = cl.DATAAREAID
    LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wktbl.PARTITION = wkln.PARTITION AND wktbl.DATAAREAID = wkln.DATAAREAID
    LEFT JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.[PARTITION] = wkln.[PARTITION] AND loc.DATAAREAID = wkln.DATAAREAID
WHERE
    wkln.WORKSTATUS = 0 -- Open
    AND ISNULL( ct.CLUSTERID, '' ) <> ''
    AND loc.LOCPROFILEID LIKE 'PalletPicking'
    AND wktbl.WORKSTATUS = 0
GROUP BY
    ct.CLUSTERID, ct.CLUSTERPROFILEID, wkln.WMSLOCATIONID
HAVING
    COUNT( * ) > 1
ORDER BY
    ct.CLUSTERID, wkln.WMSLOCATIONID

/*
SELECT top 2 *
FROM WHSWORKCLUSTERTABLE
*/