
SELECT 
    TOP 10 
     it.ITEMID               AS [Item]
     , it.ITEMBUY<PERSON>GROUPID
    , dep.NAME           AS [Department]
    , div.NAME            AS [Division]
    , erpt.DESCRIPTION 
    , comp.Name             AS [Company]
    , CAST(it.HAMSRP AS DECIMAL(10,2))  AS [MSRP]
    , comp1.*
FROM 
    inventtable it --ON vt.itemid=it.itemid
       --JOIN ecoresproduct ep ON it.itemid=ep.displayproductnumber
    JOIN ECORESPRODUCT                  erp     ON erp.RECID                = it.PRODUCT
    JOIN ecoresproductcategory          erpc    ON erpc.product             = erp.RECID -- OR it.product
    JOIN ECORESCATEGORYHIERARCHY        erch    ON erpc.CATEGORYHIERARCHY   = erch.recid
    JOIN ecorescategory                 erc     ON erpc.CATEGORY            = erc.recid
    JOIN ecorescategory                 dep     ON erc.PARENTCATEGORY       = dep.recid
    JOIN ECORESCATEGORY                 div     ON dep.PARENTCATEGORY       = div.recid
    LEFT JOIN ECORESPRODUCTTRANSLATION  erpt    ON erpt.PRODUCT             = erp.RECID
    JOIN ECORESCATEGORY             comp    ON div.PARENTCATEGORY       = comp.recid -- Name: Hanna
    JOIN ECORESCATEGORY             comp1    ON comp.PARENTCATEGORY       = comp1.recid -- Name: Hanna Andersson
WHERE
    it.itemid IN ('30070', '36754', '42067', '43205', '15233')

--sp_columns inventtable
--sp_columns ecoresproduct

/*
SELECT 
    it.ITEMID
    --, it.ITEMBUYERGROUPID
    , ercclass.NAME         AS [Class]
    --, ercsubclass.NAME      AS [SubClass]
    , ercdept.Name          AS [Department]
    , ercdiv.Name           AS [Division]
   -- , ercdivgrp.Name        AS [DivisionGroup]
    --, ercbrand.NAME         AS [Brand]
    , erpt.DESCRIPTION 
FROM 
    INVENTTABLE                         it
    JOIN ECORESPRODUCT             erp         ON it.PRODUCT               = erp.RECID                     AND erp.PARTITION = it.PARTITION
    --JOIN ECORESCATEGORY            ercbrand    ON ercbrand.RECID           = erp.HAECORESCATEGORYBRAND -- This join goes to the top category(Brand)
    JOIN ECORESCATEGORY            ercclass    ON ercclass.RECID           = erp.HAECORESCATEGORYCLASS     AND ercclass.PARTITION = erp.PARTITION
    --LEFT JOIN ECORESCATEGORY            ercsubclass ON ercsubclass.RECID        = erp.HAECORESCATEGORYSUBCLASS -- SubClass
    JOIN ECORESCATEGORY            ercdept     ON ercdept.RECID            = erp.HAECORESCATEGORYDEPARTMENT AND ercdept.[PARTITION] = erp.[PARTITION]
    JOIN ECORESCATEGORY            ercdiv      ON ercdiv.RECID             = erp.HAECORESCATEGORYDIVISION AND ercdiv.[PARTITION] = erp.[PARTITION]
    --LEFT JOIN ECORESCATEGORY            ercdivgrp   ON ercdivgrp.RECID          = erp.HAECORESCATEGORYDIVISIONGROUP -- DivisionGroup
    JOIN ECORESPRODUCTTRANSLATION  erpt        ON erpt.PRODUCT             = erp.RECID AND erpt.[PARTITION] = erp.[PARTITION]
WHERE
    it.ITEMID IN ('30070', '36754', '42067', '43205', '15233')

*/
--SELECT TOP 5 * FROM EcoresStyle

--sp_columns ECORESPRODUCT