
-- Shipments information
-- Finding shipments withing the same order that were shipped using different methods
-- The shipments are inheriting the ship method from the order

SELECT 
    o.OrderNum, s.ShipmentId, s.ModeCode
FROM 
    whsshipmenttable s
INNER JOIN 
(
  SELECT 
    OrderNum, COUNT(*) AS shipment_count
  FROM 
    whsshipmenttable
  GROUP BY 
    OrderNum
  HAVING 
    COUNT(*) > 1
) o ON o.OrderNum = s.OrderNum
WHERE EXISTS (
  SELECT 1
  FROM whsshipmenttable s2
  -- Filter within the subquery using WHERE
  WHERE 
    s2.OrderNum = s.OrderNum AND s2.ModeCode <> s.ModeCode
  GROUP BY 
    s2.OrderNum, s2.ModeCode
  HAVING COUNT(*) > 1
);

--sp_columns WHSCONTAINERTABLE
