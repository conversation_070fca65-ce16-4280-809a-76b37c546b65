{"cells": [{"cell_type": "code", "execution_count": 3, "id": "3ccdae14-be0d-4326-9298-632e030c4be1", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["x = [1, 2, 3, 4]\n", "x"]}, {"cell_type": "code", "execution_count": null, "id": "600e79ed-5f5b-4af3-aa9d-28044674f7bf", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "b8cf9731-7538-4d4f-a168-554b29e968de", "metadata": {}, "outputs": [], "source": ["z = 100\n", "z"]}, {"cell_type": "code", "execution_count": 1, "id": "ba4509ec-a1b9-4394-8f08-726d8255c65b", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'degew' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mdegew\u001b[49m\n", "\u001b[1;31mNameError\u001b[0m: name 'degew' is not defined"]}], "source": ["degew"]}, {"cell_type": "code", "execution_count": null, "id": "9fc94bbe-928f-4cfa-a4ae-c0cff1f8a9ca", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 5}