
USE DAX_PROD

-- Getting last know price of a given item


SELECT TOP 1
	price.ITEMID	AS Item
	, dim.Color
	, dim.Size
	, price.VersionID 	AS Season
	, MAX( price.ActivationDate )  AS LastActDate

FROM 
	inventitemprice price
	CROSS APPLY( 
			SELECT 
				idim.INVENTCOLORID AS Color
				, idim.INVENTSIZEID	AS Size
			FROM 
				inventdim idim 
			WHERE 
				idim.inventdimid = price.inventdimid AND idim.DATAAREAID = price.DATAAREAID AND idim.PARTITION = price.PARTITION 
				AND idim.inventsiteid = 'HA USA'
			) dim
WHERE 
	price.ITEMID  = '59631' 
	AND dim.Color = 'RH9' 
	AND dim.Size = '7'

GROUP BY
	price.ITEMID, dim.Color, dim.SIZE, price.VersionID
ORDER BY
	MAX( price.ActivationDate ) DESC

