-- With LP

SELECT  wmslocation.wmslocationid as Location, OH.LP, wmslocation.zoneid as ZoneId, wmslocation.locprofileid as LocP<PERSON><PERSON>le, OH.itemid as Item, OH.inventcolorid as Color, OH.inventsizeid as <PERSON>ze, OH.physicalinvent as <PERSON>ty, OH.reservphysical AS Reserved 
FROM wmslocation
LEFT OUTER JOIN 
( SELECT inventsum.itemid, inventsum.physicalinvent, inventsum.reservphysical, inventdim.inventcolorid, inventdim.inventsizeid, inventdim.wmslocationid, inventdim.licenseplateid as LP
  FROM inventsum
  LEFT OUTER JOIN inventdim
  ON inventsum.inventdimid = inventdim.inventdimid
  WHERE inventsum.physicalinvent > 0 AND inventdim.inventlocationid = '4010'
) as OH
ON wmslocation.wmslocationID = OH.wmslocationid
WHERE wmslocation.inventlocationid = '4010' AND wmslocation.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', 'W001', 'Bulk') AND wmslocation.zoneid NOT IN ( 'Current Pick')
ORDER BY wmslocation.wmslocationid