-- PERFORMANCE TESTING SCRIPT
-- Use this to test your query performance before and after index creation

-- =============================================================================
-- STEP 1: Enable performance monitoring
-- =============================================================================
SET STATISTICS IO ON;
SET STATISTICS TIME ON;

-- Clear the plan cache to ensure fresh execution plans
-- DBCC FREEPROCCACHE; -- Uncomment if you want to clear cache (use carefully in production)

-- =============================================================================
-- STEP 2: Test with a small subset first (for safety)
-- =============================================================================
PRINT '=== TESTING WITH SMALL SUBSET (Single Item) ===';

WITH OH AS(
SELECT 
    loc.zoneid              AS [ZoneId],
    loc.locprofileid        AS [LocProfile],
    isum.itemid             AS [Item],
    idim.inventcolorid      AS [Color],
    idim.inventsizeid       AS [Size],
    loc.wmslocationid       AS [Location],
    idim.INVENTSTATUSID     AS [Status],
    CONVERT(DECIMAL(20,0), isum.physicalinvent) AS [Qty],
    CONVERT(DECIMAL(20,0), isum.reservphysical) AS [Reserved],
    isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid AS [SKU],
    CASE WHEN (hagpft.AVAILABILITY = 'in stock' OR isum.RESERVPHYSICAL > 0) THEN 'Yes' ELSE 'No' END AS [Online],
    MAX(isum.RECID) AS 'RecId',
    CAST(isum.MODIFIEDDATETIME AS DATE) AS LastInventTransaction,
    DATEDIFF(DD, isum.MODIFIEDDATETIME, GETUTCDATE()) AS [SittingDays],
    IIF(LastSO.LastSOTransaction IS NOT NULL, CAST(LastSO.LastSOTransaction AS NVARCHAR(10)), 'N/A') AS LastSOTransaction,
    COALESCE(LastSO.SALESORDER, 'N/A') AS LastSalesOrder
FROM inventsum isum WITH (NOLOCK)
JOIN inventdim idim WITH (NOLOCK) 
    ON isum.inventdimid = idim.inventdimid 
    AND isum.DATAAREAID = idim.DATAAREAID 
    AND isum.[PARTITION] = idim.[PARTITION]
    AND idim.inventlocationid = '4010'
    AND idim.INVENTSITEID = 'HA USA'
    AND idim.DATAAREAID = 'ha'
JOIN WMSLOCATION loc WITH (NOLOCK) 
    ON loc.WMSLOCATIONID = idim.WMSLOCATIONID 
    AND loc.DATAAREAID = 'ha' 
    AND loc.[PARTITION] = isum.[PARTITION]
    AND loc.LOCPROFILEID IN ('W001', 'Picking', 'Picking A', 'Picking D', 'PalletPicking')
LEFT OUTER JOIN hagoogleproductfeedtable hagpft 
    ON hagpft.MPN = isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid
OUTER APPLY
(
    SELECT TOP 1
        itorigin.REFERENCEID AS SALESORDER,
        CAST(itrans.MODIFIEDDATETIME AS DATE) AS LastSOTransaction
    FROM INVENTTRANS itrans WITH (NOLOCK)
    JOIN INVENTTRANSORIGIN itorigin WITH (NOLOCK)
        ON itrans.INVENTTRANSORIGIN = itorigin.RECID
        AND itorigin.[PARTITION] = itrans.[PARTITION]
        AND itorigin.DATAAREAID = itrans.DATAAREAID
    WHERE
        itrans.INVENTDIMID = idim.INVENTDIMID
        AND itrans.ITEMID = isum.ITEMID
        AND itrans.DATAAREAID = idim.DATAAREAID
        AND itrans.[PARTITION] = idim.[PARTITION]
        AND itrans.STATUSISSUE = 1 -- Sold
        AND itorigin.REFERENCECATEGORY = 0 -- Sales Order
    ORDER BY itrans.MODIFIEDDATETIME DESC, itrans.RECID DESC
) AS LastSO
WHERE 
    isum.physicalinvent <> 0 
    AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997')
    AND isum.DATAAREAID = 'ha'
    -- TESTING WITH SINGLE ITEM FIRST
    AND isum.ITEMID = '80222' -- Change this to a real item ID from your data
GROUP BY
    loc.WMSLOCATIONID, isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, 
    isum.PHYSICALINVENT, isum.RESERVPHYSICAL, idim.INVENTSTATUSID, 
    loc.ZONEID, loc.LOCPROFILEID, hagpft.AVAILABILITY, 
    CAST(isum.MODIFIEDDATETIME AS DATE), isum.MODIFIEDDATETIME,
    LastSO.LastSOTransaction, LastSO.SALESORDER
)
SELECT COUNT(*) AS RecordCount FROM OH;

PRINT '=== END SMALL SUBSET TEST ===';
PRINT '';

-- =============================================================================
-- STEP 3: Check execution plan for the OUTER APPLY
-- =============================================================================
PRINT '=== CHECKING OUTER APPLY EXECUTION PLAN ===';

-- This query isolates just the OUTER APPLY logic for analysis
DECLARE @TestInventDimId BIGINT = (SELECT TOP 1 INVENTDIMID FROM INVENTDIM WHERE DATAAREAID = 'ha');
DECLARE @TestItemId NVARCHAR(20) = (SELECT TOP 1 ITEMID FROM INVENTSUM WHERE DATAAREAID = 'ha' AND PHYSICALINVENT > 0);

SELECT  
    MAX(itorigin.REFERENCEID) AS SALESORDER, 
    CAST(MAX(itrans.MODIFIEDDATETIME) AS DATE) AS LastSOTransaction,
    COUNT(*) AS TransactionCount
FROM INVENTTRANS itrans WITH (NOLOCK)
JOIN INVENTTRANSORIGIN itorigin WITH (NOLOCK) 
    ON itrans.INVENTTRANSORIGIN = itorigin.RECID              
    AND itorigin.[PARTITION] = itrans.[PARTITION]     
    AND itorigin.DATAAREAID = itrans.DATAAREAID
WHERE 
    itrans.INVENTDIMID = @TestInventDimId
    AND itrans.ITEMID = @TestItemId
    AND itrans.DATAAREAID = 'ha'
    AND itrans.STATUSISSUE = 1 -- Sold
    AND itorigin.REFERENCECATEGORY = 0 -- Sales Order 
GROUP BY itorigin.REFERENCEID;

PRINT '=== END OUTER APPLY TEST ===';
PRINT '';

-- =============================================================================
-- STEP 4: Performance metrics to capture
-- =============================================================================
PRINT '=== PERFORMANCE METRICS TO MONITOR ===';
PRINT 'Look for these metrics in the output above:';
PRINT '1. Logical reads on INVENTTRANS (should be low with proper index)';
PRINT '2. Logical reads on INVENTTRANSORIGIN (should be low with proper index)';
PRINT '3. CPU time (should decrease significantly with indexes)';
PRINT '4. Elapsed time (overall query time)';
PRINT '';
PRINT 'BEFORE INDEXES: Expect high logical reads on INVENTTRANS/INVENTTRANSORIGIN';
PRINT 'AFTER INDEXES: Should see dramatic reduction in logical reads';
PRINT '';

-- =============================================================================
-- STEP 5: Index effectiveness check
-- =============================================================================
PRINT '=== INDEX USAGE CHECK (Run after creating indexes) ===';

-- Check if our new indexes are being used
SELECT 
    OBJECT_NAME(s.object_id) AS TableName,
    i.name AS IndexName,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.last_user_seek,
    s.last_user_scan
FROM sys.dm_db_index_usage_stats s
JOIN sys.indexes i ON s.object_id = i.object_id AND s.index_id = i.index_id
WHERE s.database_id = DB_ID()
AND i.name IN (
    'IX_INVENTTRANS_OUTER_APPLY_LOOKUP',
    'IX_INVENTTRANSORIGIN_RECID_CATEGORY',
    'IX_INVENTSUM_MAIN_QUERY',
    'IX_INVENTDIM_LOCATION_SITE',
    'IX_WMSLOCATION_PROFILE_FILTER'
)
ORDER BY s.user_seeks DESC;

-- =============================================================================
-- STEP 6: Disable statistics (cleanup)
-- =============================================================================
SET STATISTICS IO OFF;
SET STATISTICS TIME OFF;

PRINT '=== TESTING COMPLETE ===';
PRINT 'Compare the metrics before and after creating the recommended indexes.';
PRINT 'The IX_INVENTTRANS_OUTER_APPLY_LOOKUP index should show the biggest improvement.';
