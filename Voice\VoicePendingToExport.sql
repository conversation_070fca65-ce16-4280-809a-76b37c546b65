
/*
SELECT 'PendingVoicePicks' AS QueueType, COUNT(*) AS PendingCount
FROM HAVOICEINTEGRATIONQUEUEPICKS
WHERE STATUS = 0
UNION ALL
SELECT 'PendingVoicePuts' AS QueueType, COUNT(*) AS PendingCount
FROM HAVOICEINTEGRATIONQUEUEPUTS
WHERE STATUS = 0;
*/
SELECT
    (SELECT COUNT(*) 
    FROM 
        HAVOICEINTEGRATIONQUEUEPUTS  vputs
        JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = vputs.WORKID AND wktbl.DATAAREAID = 'ha' AND wktbl.[PARTITION] = vputs.[PARTITION] AND wktbl.WORKTEMPLATECODE = '4010 Direct'
    WHERE 
        vputs.[STATUS] = 0
        AND wktbl.WORKSTATUS < 5) AS PendingDirect
       ,(SELECT COUNT(*) 
        FROM 
            HAVOICEINTEGRATIONQUEUEPUTS  vputs
            JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = vputs.WORKID AND wktbl.DATAAREAID = 'ha' AND wktbl.[PARTITION] = vputs.[PARTITION] AND wktbl.WORKTEMPLATECODE <> '4010 Direct'
        WHERE 
            vputs.[STATUS] = 0
            AND wktbl.WORKSTATUS < 5) AS PendingAB
    
    --, (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPUTS WHERE [STATUS] = 0 ) AS OrdersCnt
    , (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPICKS WHERE STATUS = 0) AS PendingPicks
    --, CAST(GETUTCDATE() AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME) AS KYDT;

--sp_columns HAVOICEINTEGRATIONQUEUEPUTS

--sp_running