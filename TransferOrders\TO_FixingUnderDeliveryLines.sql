
/*
<PERSON><PERSON><PERSON> to fix the under delivery lines in the transfer orders
Make sure that there is nothing pending to release on the Transfer Order
Deliver the remainder of the lines first
*/

DECLARE @TransferId NVARCHAR(20) = 'TO-00074496'; -- Transfer Order Id
SELECT
    invtl.LINENUM            AS LineNum,
    invtl.ItemId            AS ItemId,
    idim.INVENTCOLORID      AS Color,
    idim.INVENTSIZEID       AS Size,
    --invtl.InventTransId     AS InventTransId,
    --invtl.TransferId        AS TransferId,
    invtl.QTYREMAINSHIP     AS QTYREMAINSHIP,
    invtl.QTYTRANSFER       AS QTYTRANSFER,
    invtl.QTYSHIPPED        AS QTYSHIPPED,
    invtl.UNDERDELIVERYPCT  AS UNDERDELIVERYPCT,
    (invtl.QTYTRANSFER - invtl.QTYREMAINSHIP - invtl.QTYSHIPPED) / invtl.QTYTRANSFER * 100 AS ExpectedUnderDeliveryPct
FROM
    InventTransferLine AS invtl 
    JOIN INVENTDIM idim ON idim.INVENTDIMID = invtl.INVENTDIMID AND idim.DATAAREAID = invtl.DATAAREAID AND idim.PARTITION = invtl.PARTITION
WHERE
    1 = 1 
    --AND invtl.ItemId = '1001'
    AND invtl.TransferId = @TransferId
    --AND invtl.QTYREMAINSHIP <> invtl.QTYTRANSFER 
    AND invtl.UNDERDELIVERYPCT < (invtl.QTYTRANSFER - invtl.QTYREMAINSHIP - invtl.QTYSHIPPED) / invtl.QTYTRANSFER * 100 -- Not corrected yet
    --AND invtl.LINENUM = 288.00

/*

-- To be run on the production environment
-- Make sure that there is nothing pending to release on the Transfer Order
-- Use prodsql01 for the production environment
-- Use DAX_PROD for the production environment  
-- Adding 0.01 to the UNDERDELIVERYPCT to avoid the round in AX 2012
-- This script will update the UNDERDELIVERYPCT for the lines that are not corrected yet


BEGIN TRANSACTION

UPDATE [prodsql01].[DAX_PROD].[dbo].InventTransferLine
SET UNDERDELIVERYPCT = (QTYTRANSFER - QTYREMAINSHIP - QTYSHIPPED) / QTYTRANSFER * 100 + 0.01
WHERE
    1 = 1 
    AND TransferId = 'TO-00075006'
    AND QTYREMAINSHIP <> QTYTRANSFER 
    AND UNDERDELIVERYPCT <= (QTYTRANSFER - QTYREMAINSHIP - QTYSHIPPED) / QTYTRANSFER * 100 -- Not corrected yet
COMMIT TRANSACTION

*/