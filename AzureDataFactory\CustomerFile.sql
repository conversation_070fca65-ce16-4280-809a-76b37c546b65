
DECLARE @startDateTimePST AS DATETIME = '@{variables('start_date')}', 

    @endDateTimePST AS DATETIME = '@{variables('end_date')}'; 

 

DECLARE @startDateTimeUTC AS DATETIME = @startDateTimePST AT TIME ZONE 'Pacific Standard Time' AT TIME ZONE 'UTC' 

  ,@endDateTimeUTC AS DATETIME = @endDateTimePST AT TIME ZONE 'Pacific Standard Time' AT TIME ZONE 'UTC'; 

 

SELECT ct.ACCOUNTNUM AS AXAccountNum 

  ,ct.CUSTGROUP AS CustGroup 

  ,ct.HACUSTORIGIN AS CustOrigin 

  ,ct.RECID AS AXCustRecID 

  ,ct.HADWCUSTID AS WebCustID 

  ,dpt.NAME AS PrimaryName 

  ,dpn.FIRSTNAME AS FirstName 

  ,dpn.MIDDLENAME AS MiddleName 

  ,dpn.LASTNAME AS LastName 

  ,postaladdr.STREET AS PrimaryStreet 

  ,postaladdr.CITY AS PrimaryCity 

  ,postaladdr.STATE AS PrimaryState 

  ,postaladdr.ZIPCODE AS PrimaryZip 

  ,postaladdr.COUNTRYREGIONID AS PrimaryCountry 

  ,phone.LOCATOR AS PrimaryPhone 

  ,email.LOCATOR AS PrimaryEmail 

  ,(SELECT MAX(mod_date) FROM (VALUES (ct.MODIFIEDDATETIME), (postaladdr.MODIFIEDDATETIME), (dpt.MODIFIEDDATETIME), (email.MODIFIEDDATETIME), (phone.MODIFIEDDATETIME)) AS value(mod_date)) AS CustModifiedDateTimeUTC 

  ,ct.CREATEDDATETIME AS CustCreatedDateTimeUTC 

FROM [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.CUSTTABLE AS ct WITH (NOLOCK) 

INNER JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.DIRPARTYTABLE AS dpt WITH (NOLOCK) ON  

  dpt.RECID = ct.PARTY 

  AND dpt.PARTITION = ct.PARTITION 

INNER JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.DIRPERSONNAME AS dpn WITH (NOLOCK) ON  

  dpn.PERSON = ct.PARTY 

  AND dpn.VALIDTO >= '2154-12-31 23:59:59.000' 

  AND dpn.PARTITION = ct.PARTITION 

LEFT OUTER JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.LOGISTICSPOSTALADDRESS AS postaladdr WITH (NOLOCK)  

  ON postaladdr.LOCATION = dpt.PRIMARYADDRESSLOCATION 

  AND postaladdr.VALIDTO = '2154-12-31 23:59:59.000' 

  AND postaladdr.PARTITION = dpt.PARTITION 

LEFT OUTER JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.LOGISTICSELECTRONICADDRESS AS phone WITH (NOLOCK)  

  ON phone.RECID = dpt.PRIMARYCONTACTPHONE 

  AND phone.PARTITION = dpt.PARTITION 

LEFT OUTER JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.LOGISTICSELECTRONICADDRESS AS email WITH (NOLOCK)  

  ON email.RECID = dpt.PRIMARYCONTACTEMAIL 

  AND email.PARTITION = dpt.PARTITION 

WHERE (ct.PARTITION = **********) 

  AND (ct.ACCOUNTNUM NOT LIKE '%*%') 

  AND (dpn.FIRSTNAME <> 'WALK-IN CUST') 

  AND (ct.MODIFIEDDATETIME BETWEEN @startDateTimeUTC AND @endDateTimeUTC OR 

    postaladdr.MODIFIEDDATETIME BETWEEN @startDateTimeUTC AND @endDateTimeUTC OR 

    dpt.MODIFIEDDATETIME BETWEEN @startDateTimeUTC AND @endDateTimeUTC OR 

    email.MODIFIEDDATETIME BETWEEN @startDateTimeUTC AND @endDateTimeUTC OR 

    phone.MODIFIEDDATETIME BETWEEN @startDateTimeUTC AND @endDateTimeUTC) 