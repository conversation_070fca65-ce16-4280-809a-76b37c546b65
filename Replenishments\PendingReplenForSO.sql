-- Find replenishments needed for Rush
-- Grouping them by WorkId because there are duplicates in the table, 12/26/2024
-- Extending the query to include all replenisments, not just the ones for <PERSON>, 3/17/2025


SELECT 
    Link.ReplenWorkID           AS [WorkId]
    , CASE WHEN wavetd.wavetemplatename LIKE '%rush%' THEN 'Yes' ELSE 'No' END AS [ForRush?]
    , wlr.WMSLOCATIONID         AS [FromLoc]
    , wld.WMSLOCATIONID         AS [ToLoc]
    , wld.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID       AS [SKU]
   /*, wld.ITEMID                AS [Item]
    , idim.INVENTCOLORID        AS [Color]
    , idim.INVENTSIZEID         AS [Size]*/
    , CAST(wlr.QTYWORK AS INT)  AS [Qty]
    , RIGHT(wlr.LOCATEDLPID, 5) AS [LP]
    --, COUNT( Link.REPLENWORKID)                                         AS [WaitingWorkLines]
    , CASE WHEN wtr.FROZEN = 1 THEN 'Yes' ELSE 'No' END                 AS [Blocked]
    , wtr.WORKPRIORITY                                                  AS [Priority]
    , CASE WHEN wtr.WORKSTATUS = 0 THEN 'Open' ELSE 'In Process' END    AS [Status]
     , CASE WHEN wtr.WORKSTATUS = 1 THEN FORMAT(wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 'MMM dd, yyyy hh:mmtt') 
        ELSE 'N/A' END AS [PulledAt]
    --, CONVERT(VARCHAR(8), wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 108) AS [TimePulled2]
    , COALESCE(wur.USERNAME, 'N/A')              AS [LockedBy]
    , COALESCE(wul.USERNAME, 'N/A')              AS [PulledBy]
    , COUNT( DISTINCT Link.DEMANDWORKID)                                AS [WaitingShipments]
    --, wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS ModDt
    --, CAST(wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS VARCHAR(22)) AS ModDTSTR
    --, CONVERT(VARCHAR(10), wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 101) + ' ' + 
    --CONVERT(VARCHAR(8), wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 108) AS FormattedDateTime
    --CONVERT( VARCHAR(10), CAST(wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS TIME)
   
    
FROM 
    WHSREPLENWORKLINK link
    JOIN WHSWORKLINE wld        ON wld.workid = link.DEMANDWORKID       AND wld.LINENUM = link.DEMANDLINENUM	AND wld.DATAAREAID = link.DATAAREAID    AND wld.PARTITION = link.PARTITION
    JOIN INVENTDIM idim         ON idim.INVENTDIMID = wld.INVENTDIMID   AND idim.DATAAREAID = wld.DATAAREAID    AND idim.[PARTITION] = wld.[PARTITION]
    JOIN WHSWORKTABLE wtd       ON link.DEMANDWORKID = wtd.WORKID	    AND link.DATAAREAID	= wtd.DATAAREAID    AND link.PARTITION	= wtd.PARTITION
    JOIN WHSWAVETABLE wavetd    ON wtd.WAVEID = wavetd.WAVEID           AND wtd.DATAAREAID	= wavetd.DATAAREAID	AND wtd.PARTITION	= wavetd.PARTITION
	JOIN WHSWORKLINE wlr        ON wlr.workid = link.REPLENWORKID       AND wlr.LINENUM = link.REPLENLINENUM    AND wlr.DATAAREAID = link.DATAAREAID    AND wlr.PARTITION = link.PARTITION 
    LEFT JOIN WHSWORKUSER wul        ON wlr.USERID = wul.USERID              AND wlr.DATAAREAID = wul.DATAAREAID     AND wlr.PARTITION = wul.PARTITION
    JOIN WHSWORKTABLE wtr       ON link.REPLENWORKID = wtr.WORKID       AND link.DATAAREAID	= wtr.DATAAREAID    AND link.PARTITION	= wtr.PARTITION
    LEFT JOIN WHSWORKUSER wur        ON wtr.LOCKEDUSER = wur.USERID          AND wtr.DATAAREAID = wur.DATAAREAID     AND wtr.PARTITION = wur.PARTITION
    
	--JOIN WHSWAVETABLE wavetr ON wtr.WAVEID = wavetr.WAVEID         AND wtr.DATAAREAID = wavetr.DATAAREAID      AND wtr.PARTITION	= wavetr.PARTITION
WHERE 
    wtr.WORKSTATUS < 4 -- Replen not completed or canceled
	--AND wavetd.wavetemplatename LIKE '%rush%'
    AND wtd.CREATEDDATETIME > GETUTCDATE() - 10
GROUP BY
    Link.REPLENWORKID, wtr.FROZEN, wtr.WORKPRIORITY, wtr.WORKSTATUS, wlr.MODIFIEDDATETIME, wtr.LOCKEDUSER, wlr.USERID, wlr.WMSLOCATIONID
    , wld.WMSLOCATIONID, wld.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, wlr.QTYWORK,wlr.LOCATEDLPID, wavetd.wavetemplatename , wul.USERNAME , wur.USERNAME


/*

SELECT TOP 20 *
FROM WHSREPLENWORKLINKREPLEN
WHERE REPLENWORKID IN( 'WK0028935631', 'WK0028935630'  )


SELECT TOP 20 *
FROM WHSREPLENWORKLINK
WHERE 
     --REPLENWORKID = 'WK0012244570'
     DEMANDWORKID = 'WK0028935717'


SELECT 
    GETDATE() AS UnconvertedServerDateTime,
    GETUTCDATE() AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS ConvertedEastDateTime,
    CAST(GETDATE() AS NVARCHAR(30)) AS UsingCastServerDateTime,
    CAST(GETUTCDATE() AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS NVARCHAR(30)) AS UsingCastEastDateTime,
    CONVERT(VARCHAR(20), GETUTCDATE() AT TIME ZONE 'Eastern Standard Time', 107) + ' ' +
    CONVERT(VARCHAR(8), GETUTCDATE() AT TIME ZONE 'Eastern Standard Time', 108) AS FormattedDateTime1,
    FORMAT(GETUTCDATE() AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 'MMM dd, yyyy hh:mmtt') AS FormattedDateTime2,
    CONVERT(NVARCHAR(30), GETDATE(), 126) AS UsingConvertTo_ISO8601;
GO

*/