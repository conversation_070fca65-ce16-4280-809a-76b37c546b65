
WITH shp AS (
    SELECT 
        SUM(CASE WHEN shptbl.HASINGLESKU = 1 THEN 1 ELSE 0 END) AS Single
        , SUM(CASE WHEN shptbl.HASINGLESKU = 0 THEN 1 ELSE 0 END) AS Multiple
        , COUNT(*) AS Total
    FROM 
        WHSSHIPMENTTABLE shptbl
    WHERE 
        1 = 1
        AND shptbl.LOADDIRECTION = 2
        AND shptbl.INVENTLOCATIONID = '4010'
        AND shptbl.WORKTRANSTYPE = 2
        AND shptbl.DATAAREAID = 'ha'
        AND shptbl.[PARTITION] = 5637144576
        AND shptbl.HAGIFT = 1
        --AND shptbl.HASINGLESKU = 0 AND shptbl.HASINGLELINEUNITSHIPMENT = 0
)
SELECT 
    Single
    , Multiple
    , Total
    , CAST((Single * 100.0 / NULLIF(Total, 0)) AS DECIMAL(10,2)) AS SinglePercentage
    , CAST((Multiple * 100.0 / NULLIF(Total, 0)) AS DECIMAL(10,2)) AS MultiplePercentage
FROM 
    shp;