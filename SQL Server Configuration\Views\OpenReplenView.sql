USE [DAX_PROD]
GO

/****** Object:  View [dbo].[W<PERSON>OPENREPLENWORK]    Script Date: 7/26/2022 8:28:21 AM ******/
/*
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[W<PERSON><PERSON><PERSON>REPLENWORK] AS 
*/
SELECT	
	wklnrep2.WORKID				AS WorkId,
	wklnrep1.WMSLOCATIONID		AS 'Location',
	wklnrep2.ITEMID				AS Item,
	idim.INVENTCOLORID			AS Color,
	idim.INVENTSIZEID			AS Size,
	--wklnrep2.INVENTDIMID		AS INVENTDIMID,
	CONVERT( DECIMAL( 10, 0 ), wklnrep2.LINENUM )			AS LINENUM,
	CONVERT( DECIMAL( 10, 0 ), wklnrep2.INVENTQTYWORK	)	AS Qty,
	CONVERT( DECIMAL( 10, 0 ), <PERSON>UM(replink.INVENTQTY) )	AS SUMOFAllocatedReplenQty,
	CAST( ((wklnrep2.INVENTQTYWORK) - (ISNULL(SUM(replink.INVENTQTY), 0))) AS NUMERIC(32,0)) AS UnAllocatedQty
		/*wklnrep1.DATAAREAID		AS DATAAREAID,
		wklnrep1.PARTITION AS PARTITION,
		1010 AS RECID,
		wktblrep.DATAAREAID AS DATAAREAID#2,
		wktblrep.PARTITION AS PARTITION#2,
		wktblrep.IMMEDIATEREPLENISHMENTUNITID AS IMMEDIATEREPLENISHMENTUNITID,
		wklnrep2.DATAAREAID AS DATAAREAID#3,
		wklnrep2.PARTITION AS PARTITION#3,
		replink.DATAAREAID AS DATAAREAID#4,
		replink.PARTITION AS PARTITION#4,*/

	FROM WHSWORKLINE wklnrep1 -- Taking the puts from here T1
		CROSS JOIN WHSWORKTABLE wktblrep -- Replen Work T2
		CROSS JOIN WHSWORKLINE wklnrep2 -- Picks T3
		LEFT OUTER JOIN WHSREPLENWORKLINKREPLEN replink  -- T4
		ON ( ( wklnrep2.LINENUM = replink.REPLENLINENUM AND ( wklnrep2.DATAAREAID = replink.DATAAREAID) AND ( wklnrep2.PARTITION = replink.PARTITION) ) AND 
		 ( wklnrep2.WORKID = replink.REPLENWORKID  AND ( wklnrep2.DATAAREAID = replink.DATAAREAID) AND ( wklnrep2.PARTITION = replink.PARTITION))) 
		LEFT OUTER JOIN INVENTDIM idim ON idim.INVENTDIMID = wklnrep2.INVENTDIMID AND idim.PARTITION = wklnrep2.PARTITION AND idim.DATAAREAID = wklnrep2.DATAAREAID
	WHERE	( wklnrep1.WORKTYPE = 2 AND wklnrep1.WORKSTATUS < 2 ) AND   -- Put line;  Open, In process: status
			(
				( wktblrep.WORKTRANSTYPE = 11 AND wktblrep.USEWORKFORWAVEREPLEN = 1 ) AND 
				( wklnrep1.WORKID = wktblrep.WORKID	AND wklnrep1.DATAAREAID = wktblrep.DATAAREAID AND wklnrep1.PARTITION = wktblrep.PARTITION )
			) AND 
			( 
				( wklnrep2.WORKTYPE = 1 ) AND NOT( wklnrep2.WORKSTATUS = 5 OR wktblrep.WORKSTATUS = 5 ) AND ( wktblrep.WORKID = wklnrep2.WORKID AND ( wktblrep.DATAAREAID = wklnrep2.DATAAREAID ) AND ( wktblrep.PARTITION = wklnrep2.PARTITION ) )
			) AND 
			 NOT EXISTS( 
							SELECT 'x' 
							FROM WHSWORKLINE wklnrep15 
							WHERE ( ( wklnrep15.WORKTYPE = 2 AND wklnrep15.LINENUM > wklnrep1.LINENUM AND wklnrep15.PARTITION = wklnrep1.PARTITION ) AND 
									( wklnrep1.WORKID = wklnrep15.WORKID AND wklnrep1.DATAAREAID = wklnrep15.DATAAREAID AND wklnrep1.PARTITION = wklnrep15.PARTITION 	) 
								 )
						) 
			 AND 
			( NOT (EXISTS (
				SELECT 'x' 
				FROM WHSWORKLINE wklnrep16 
				WHERE (((wklnrep16.WORKTYPE=2) AND 
					(wklnrep16.LINENUM<wklnrep2.LINENUM AND 
					(wklnrep16.PARTITION = wklnrep2.PARTITION))) AND 
					(wklnrep2.WORKID=wklnrep16.WORKID AND 
					(wklnrep2.DATAAREAID = wklnrep16.DATAAREAID) AND 
					(wklnrep2.PARTITION = wklnrep16.PARTITION)))))
			) 
	GROUP BY wklnrep1.WMSLOCATIONID, 
			/*wklnrep1.DATAAREAID,wklnrep1.PARTITION,wktblrep.DATAAREAID,wktblrep.PARTITION,wktblrep.IMMEDIATEREPLENISHMENTUNITID,*/
			wklnrep2.WORKID,wklnrep2.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, 
			--wklnrep2.INVENTDIMID,
			wklnrep2.LINENUM, /*wklnrep2.DATAAREAID,wklnrep2.PARTITION,*/
			wklnrep2.INVENTQTYWORK --,replink.DATAAREAID,replink.PARTITION
GO

/*
SELECT TOP 20 * 
FROM WHSWORKTABLE
WHERE 
	WORKTRANSTYPE = 11
	AND WAVEID = ''
	AND WORKSTATUS < 5
	AND USEWORKFORWAVEREPLEN = 0
	AND CREATEDDATETIME > '01/01/2021'
	--WORKID = 'WK0024379232'


SELECT wkreplk.*
FROM WHSREPLENWORKLINKREPLEN wkreplk
	LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wkreplk.REPLENWORKID AND wktbl.DATAAREAID = wkreplk.DATAAREAID AND wktbl.PARTITION = wkreplk.PARTITION
WHERE
	wktbl.WORKSTATUS < 2

SELECT TOP 20 *
FROM WHSREPLENWORKLINK

SELECT TOP 20 *
FROm WHSWORKLINE
WHERE WORKID = 'WK0024382613'

exec sp_columns WHSREPLENWORKLINKREPLEN

*/