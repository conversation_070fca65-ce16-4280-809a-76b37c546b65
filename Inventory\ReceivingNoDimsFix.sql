
-- Troubleshooting items with no dimensions in picking
-- Appears to be related to an issue when new colors are added
-- looking for items with zero dimensions on pending loads and marked as measured

WITH icb AS(
SELECT 
   icomb.ITEMID
   , idim.INVENTCOLORID AS [Color]
   , idim. INVENTSIZEID AS [Size]
   , idim.INVENTDIMID   AS [icb_InventDimId]
   , icomb.HAMEASUREMENTWORKCREATED
   , icomb.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'  AS [CreatedDateTime]
FROM
    INVENTDIMCOMBINATION icomb
    JOIN INVENTDIM idim ON idim.INVENTDIMID = icomb.INVENTDIMID AND icomb.DATAAREAID = 'ha' AND icomb.[PARTITION] = idim.[PARTITION]
),
itl AS(
SELECT 
    uoms.SHIPMENTID                     AS [ShipmentId]
    , uoms.LOADID                       AS [LoadId]
    , uoms.HAPURCHASEORDER              AS [PO]
    , uoms.LICENSEPLATEID               AS [LicensePlate]
    --, icb.icb_InventDimId
    , uoms.ITEMID                       AS [ItemId]
    , idim.INVENTCOLORID                AS [Color]
    , idim.INVENTSIZEID                 AS [Size]
    --, asni.INVENTDIMID
    , CAST(uoms.QTY AS INT)             AS [Qty]
    --, CAST(asni.WORKCREATEDQTY AS INT)  AS [WkQty]
    --, uoms.CREATEDWORKID                AS [CREATEDWORKID]
   -- , icb.HAMEASUREMENTWORKCREATED
   , icb.CreatedDateTime                AS [ItemCombCreatedDT]
FROM 
    WHSUOMSTRUCTURE uoms
    JOIN WHSASNITEM asni 
        ON uoms.LICENSEPLATEID = asni.LICENSEPLATEID AND uoms.SHIPMENTID = asni.SHIPMENTID AND uoms.[MODULE] = asni.[MODULE] AND uoms.DATAAREAID = 'ha' AND uoms.[PARTITION] = **********
    JOIN INVENTDIM idim ON idim.INVENTDIMID = asni.INVENTDIMID AND idim.DATAAREAID = 'ha' AND idim.[PARTITION] = asni.[PARTITION]
    JOIN WHSPHYSDIMUOM physd    ON physd.ITEMID = uoms.ITEMID AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID
        AND idim.DATAAREAID = 'ha'  AND idim.[PARTITION] = physd.[PARTITION]
    JOIN icb ON icb.ITEMID = uoms.ITEMID AND icb.Color = idim.INVENTCOLORID AND icb.[Size] = idim.INVENTSIZEID
WHERE
    physd.HEIGHT = 0 -- No dimensions
    AND icb.HAMEASUREMENTWORKCREATED =  1  -- Marked as measured
    --AND uoms.LicensePlateId IN (
    AND NOT EXISTS ( -- Discarding the SKU if at least one box has been received
        SELECT  s.LicensePlateId 
        FROM    WHSUOMSTRUCTURE s
        JOIN    WHSASNITEM i ON  s.LICENSEPLATEID = i.LICENSEPLATEID AND s.SHIPMENTID = i.SHIPMENTID AND s.[MODULE] = i.[MODULE] AND s.DATAAREAID = 'ha' AND s.[PARTITION] = **********
        WHERE   s.ITemId = uoms.ItemId  AND asni.InventDimId = i.InventDimId AND ISNULL(s.CREATEDWORKID, '') <> '' -- At least one box within the SKU was measured 
        )
    AND ISNULL(uoms.CREATEDWORKID, '') <> ''   -- Not received(Only gets one row: other box might have been measured)
    --AND uoms.LOADID IN ('**********', '**********')
)
SELECT 
    * 
FROM itl
WHERE
    1 = 1 
    --AND LoadId IN ('**********', '**********', '**********', '**********', '**********')


/*
--BEGIN TRANS;
UPDATE idc
SET idc.HAMEASUREMENTWORKCREATED = 0
FROM INVENTDIMCOMBINATION as idc
INNER JOIN itl ON idc.ITEMID = itl.ItemId AND idc.INVENTDIMID = itl.icb_InventDimId;
--COMMIT TRANS;
*/

--sp_columns WHSUOMSTRUCTURE
--sp_columns WHSASNITEM