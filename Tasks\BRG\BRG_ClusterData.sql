

--BRG Cluster data, 3/9/2022
-- Wave 169172, 3/15/2022

SELECT 
	wktb.WORKTEMPLATECODE												AS WorkTemplate, 
	wkln.WORKID															AS WorkId, 
	wkcl.CLUSTERID														AS Cluster, 
	wktb.WAVEID															AS Wave, 
	wavtb.WA<PERSON><PERSON><PERSON>LATENAME												AS WaveTemplate,
	wavtt.HAVOICEREGION													AS VoiceRegion,
	wkln.WMSLOCATIONID													AS 'Location',
	CASE 
		WHEN ISNULL( idim.INVENTCOLORID, '' ) = '' THEN wkln.ITEMID
		WHEN ISNULL( idim.INVENTSIZEID, '' ) = ''  THEN wkln.ItemId + '-' + idim.INVENTCOLORID
		ELSE wkln.ItemId + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID	
	END AS SKU,
	wkln.ZONEID															AS Zone,
	CONVERT( DECIMAL, wkln.INVENTQTYWORK, 10 )							AS Qty,
	wkln.USERID															AS UserId,
	wkln.WORKCLASSID													AS WorkClass,
	wkln.ORDERNUM														AS 'Order',
	wkln.SHIPMENTID														AS Shipment,
	wkln.CONTAINERID													AS Container,
	--wkln.MODIFIEDDATETIME												AS UTC_ModifDateTime,
	DATEADD( hh, -4, wkln.ModifiedDateTime )							AS KY_ModifDateTime,
	CASE WHEN wktb.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' )	THEN 'AB' ELSE 'No_AB' END AS CNT_Type
FROM WHSWorkline wkln
INNER JOIN WHSWorkTable wktb			ON	wkln.WORKID			= wktb.WORKID
LEFT JOIN WHSWorkClusterLine wkcl		ON	wkln.WORKID			= wkcl.WORKID
INNER JOIN InventDim idim				ON	wkln.INVENTDIMID	= idim.INVENTDIMID
INNER JOIN WHSWaveTable wavtb			ON wavtb.WAVEID		= wktb.WAVEID
INNER JOIN WHSWAVETEMPLATETABLE wavtt	ON wavtt.WAVETEMPLATENAME = wavtb.WAVETEMPLATENAME AND wavtt.DATAAREAID = 'ha'
WHERE 
	wkln.MODIFIEDDATETIME	BETWEEN  '03/25/2022 05:00:00 AM' AND '03/26/2022 04:59:59 AM'	AND
	ISNULL( wkcl.CLUSTERID,  '' )  <> '' AND
	--wkln.MODIFIEDDATETIME	BETWEEN  '02/01/2022 05:00:00 AM' AND GETUTCDATE()	AND
	wkln.WORKCLASSID		= 'DirectPick'										AND
	wkln.WORKTYPE = 1															AND
	wkln.WORKSTATUS < 5															AND
	wkln.ITEMID NOT IN ( '3333', '30991' )  -- No returned orders or giftcards
--	AND wktb.WAVEID		= 'WV000169172'
--ORDER BY wkln.SHIPMENTID, wkln.ORDERNUM, wkln.MODIFIEDDATETIME
ORDER BY wkcl.CLUSTERID, wkln.MODIFIEDDATETIME


/*
BRG request, 6/28/2022
 Different format
WORKID, CLUSTERID, WAVEID, WMSLOCATIONID, ITEMID, ZONEID, HAVOICEREGION, INVENTQTYWORK, USERID, WORKCLASSID, ORDERNUM, SHIPMENTID, CONTAINERID, WAVETEMPLATENAME, UTC_MODIFIEDDATETIME, EST_MODIFIEDDATETIME	
*/

SELECT 
	wkln.WORKID, 
	wkcl.CLUSTERID, 
	wktb.WAVEID, 
	wkln.WMSLOCATIONID,
	wkln.ITEMID,
	wkln.ZONEID,
	wavtt.HAVOICEREGION,
	CONVERT( DECIMAL( 4 , 0 ), wkln.INVENTQTYWORK )						AS INVENTQTYWORK,
	wkln.USERID,
	wkln.WORKCLASSID,
	wkln.ORDERNUM,
	wkln.SHIPMENTID,
	wkln.CONTAINERID,
	wavtb.WAVETEMPLATENAME,
	wkln.MODIFIEDDATETIME												AS UTC_ModifDateTime,
	DATEADD( hh, -4, wkln.ModifiedDateTime )							AS KY_ModifDateTime
FROM WHSWorkline wkln
INNER JOIN WHSWorkTable wktb			ON	wkln.WORKID			= wktb.WORKID 		AND wkln.PARTITION = wktb.PARTITION AND wkln.DATAAREAID = wktb.DATAAREAID
LEFT JOIN WHSWorkClusterLine wkcl		ON	wkln.WORKID			= wkcl.WORKID 		AND wkln.PARTITION = wkcl.PARTITION AND wkln.DATAAREAID = wkcl.DATAAREAID
INNER JOIN InventDim idim				ON	wkln.INVENTDIMID	= idim.INVENTDIMID 	AND wkln.PARTITION = idim.PARTITION AND wkln.DATAAREAID = idim.DATAAREAID
INNER JOIN WHSWaveTable wavtb			ON wavtb.WAVEID			= wktb.WAVEID 		AND wavtb.PARTITION = wktb.PARTITION AND wavtb.DATAAREAID = wktb.DATAAREAID
INNER JOIN WHSWAVETEMPLATETABLE wavtt	ON wavtt.WAVETEMPLATENAME = wavtb.WAVETEMPLATENAME AND wavtt.DATAAREAID = 'ha'
WHERE 
	wkln.MODIFIEDDATETIME	BETWEEN  '07/01/2022 04:00:00 AM' AND '08/01/2022 03:59:59 AM'	AND
	ISNULL( wkcl.CLUSTERID,  '' )  <> '' AND
	wkln.WORKCLASSID		= 'DirectPick'										AND 
	wkln.WORKTYPE = 1															AND -- Pick
	wkln.WORKSTATUS < 5															AND -- Not canceled	
	wkln.ITEMID NOT IN ( '3333', '30991' )  -- No returned orders or giftcards
ORDER BY wkcl.CLUSTERID, wkln.MODIFIEDDATETIME


/*
BRG request, 7/1/2022
 Adding Cluster creation date
From June 1st thru June 23rd
From June 28th thru June 30th
 
*/

SELECT 
	wkln.WORKID, 
	wkcl.CLUSTERID, 
	DATEADD( hh, -4, wkct.CREATEDDATETIME )								AS ClusterCreated,
	wktb.WAVEID, 
	wkln.WMSLOCATIONID,
	wkln.ITEMID,
	wkln.ZONEID,
	wavtt.HAVOICEREGION,
	CONVERT( DECIMAL( 4 , 0 ), wkln.INVENTQTYWORK )						AS INVENTQTYWORK,
	wkln.USERID,
	wkln.WORKCLASSID,
	wkln.ORDERNUM,
	wkln.SHIPMENTID,
	wkln.CONTAINERID,
	wavtb.WAVETEMPLATENAME,
	wkln.MODIFIEDDATETIME												AS UTC_ModifDateTime,
	DATEADD( hh, -4, wkln.ModifiedDateTime )							AS KY_ModifDateTime
FROM WHSWorkline wkln
INNER JOIN WHSWorkTable wktb			ON	wkln.WORKID			= wktb.WORKID
LEFT JOIN WHSWorkClusterLine wkcl		ON	wkln.WORKID			= wkcl.WORKID
INNER JOIN WHSWORKCLUSTERTABLE wkct 	ON wkcl.CLUSTERID		= wkct.CLUSTERID
INNER JOIN InventDim idim				ON	wkln.INVENTDIMID	= idim.INVENTDIMID
INNER JOIN WHSWaveTable wavtb			ON wavtb.WAVEID		= wktb.WAVEID
INNER JOIN WHSWAVETEMPLATETABLE wavtt	ON wavtt.WAVETEMPLATENAME = wavtb.WAVETEMPLATENAME AND wavtt.DATAAREAID = 'ha'
WHERE 
	wkct.CREATEDDATETIME	BETWEEN  '06/28/2022 04:00:00 AM' AND '07/1/2022 03:59:59 AM'	AND
	ISNULL( wkcl.CLUSTERID,  '' )  <> '' AND
	wkln.WORKCLASSID		= 'DirectPick'										AND 
	wkln.WORKTYPE = 1															AND -- Pick
	wkln.WORKSTATUS < 5															AND -- Not canceled	
	wkln.ITEMID NOT IN ( '3333', '30991' )  -- No returned orders or giftcards
ORDER BY wkcl.CLUSTERID, wkln.MODIFIEDDATETIME


-- 8/11/2022


SELECT 
    cl.CLUSTERID
    ,wktbl.WORKID
    ,wktbl.HAWORKSORTORDER
    ,wktbl.WAVEID
    ,wktbl.ORDERNUM
    ,wktbl.CONTAINERID
    ,wktbl.HACONTAINERTYPECODE
    ,wktbl.WORKTEMPLATECODE
    --,DATEADD( hh, -4, wktbl.CREATEDDATETIME ) AS CREATEDDATETIME
FROM WHSWORKTABLE wktbl
LEFT JOIN WHSWORKCLUSTERLINE cl ON cl.WORKID = wktbl.WORKID AND cl.DATAAREAID = wktbl.DATAAREAID AND cl.[PARTITION] = wktbl.[PARTITION]
WHERE 
    wktbl.WORKSTATUS < 5
    AND wktbl.WORKTRANSTYPE = 2
    AND wktbl.INVENTLOCATIONID = '4010'
    AND wktbl.DATAAREAID = 'ha'
    AND wktbl.CREATEDDATETIME BETWEEN '07/01/2022 04:00:00 AM' AND '8/1/2022 03:59:59 AM'
    AND ISNULL( cl.CLUSTERID, '' ) <> ''
ORDER BY    
    cl.CLUSTERID

SELECT TOP 20 *
FROM WHSWORKCLUSTERLINE cl 
--WHERE cl. > '7/1/2022 00:00:00'