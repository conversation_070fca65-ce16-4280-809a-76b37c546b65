-- Using this one

SELECT  
	CASE
		WHEN idim.INVENTCOLORID IS NULL THEN isum.ITEMID 
		ELSE
			CASE 
				WHEN idim.INVENTSIZEID IS NULL THEN isum.ITEMID + '-' + idim.INVENTCOLORID
				ELSE isum.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.inventsizeid
			END
	END 												AS SKU
	, price.Season
	, loc.zoneid 										AS ZoneId
	, loc.wmslocationid									AS 'Location'
	,  CONVERT( DECIMAL( 20,0), isum.physicalinvent )	AS Qty
	--,  OH.LastPickWC AT TIME ZONE 'UTC' AT TIME Zone 'Eastern Standard Time' AS LastMvmt
	, MAX( isum.MODIFIEDDATETIME ) 						AS LastMvmt
	--, DATEDIFF(DAY, OH.LastPickWC , GETUTCDATE() ) 		AS SittingDays
 
	/*
	, CASE WHEN OH.LastPickWC < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, OH.LastPickWC ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
			DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, OH.LastPickWC ) AS nvarchar(4)) AS datetime) ) ) 
			THEN    DATEADD(hh, - 5, OH.LastPickWC ) 
 		ELSE	
			CASE    WHEN OH.LastPickWC < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, OH.LastPickWC ) AS nvarchar(4)) AS datetime ) + 1 -
			DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, OH.LastPickWC ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, OH.LastPickWC ) 
				ELSE dateadd(hh, - 5, OH.LastPickWC ) 
			END 
		END 
	AS LastMvmt
	*/
--INTO #LastPick
FROM wmslocation loc -- Warehouse locations
LEFT JOIN inventdim idim ON idim.WMSLOCATIONID = loc.WMSLOCATIONID AND loc.DATAAREAID = idim.DATAAREAID AND loc.[PARTITION] = idim.[PARTITION] AND idim.inventlocationid = '4010' 
LEFT JOIN inventsum isum ON isum.INVENTDIMID = idim.INVENTDIMID AND isum.DATAAREAID = idim.DATAAREAID AND isum.PARTITION = idim.[PARTITION]
-- Testing the CROSS APPLY
/*
CROSS APPLY
	( SELECT idim.wmslocationid, MAX( isum.MODIFIEDDATETIME ) AS LastPickWC
  		FROM inventsum isum  -- Transactions
 		LEFT OUTER JOIN inventdim idim  ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = idim.DATAAREAID AND isum.PARTITION = idim.[PARTITION]
		WHERE 
			isum.physicalinvent > 0 AND idim.inventlocationid = '4010' AND idim.DATAAREAID = 'ha' AND loc.wmslocationID = idim.wmslocationid
		GROUP BY idim.WMSLOCATIONID
	) AS OH  
*/
/*
INNER JOIN -- Locations with stock
	( SELECT idim.wmslocationid, MAX( isum.MODIFIEDDATETIME ) AS LastPickWC
  		FROM inventsum isum  -- Transactions
 		LEFT OUTER JOIN inventdim idim  ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = idim.DATAAREAID AND isum.PARTITION = idim.[PARTITION]
		 -- This includes work created, even without being picked. 
 		--LEFT JOIN WHSWORKTRANS wktrans ON isum.INVENTDIMID = wktrans.INVENTDIMID AND isum.ITEMID = wktrans.ITEMID  AND isum.DATAAREAID = wktrans.DATAAREAID AND isum.[PARTITION] = wktrans.[PARTITION]-- SKU
		--LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wktrans.WORKID AND wktbl.[PARTITION] = wktrans.[PARTITION] AND wktbl.DATAAREAID = wktrans.DATAAREAID
		WHERE isum.physicalinvent > 0 AND idim.inventlocationid = '4010' AND idim.DATAAREAID = 'ha'-- KY DC only, non empty locations only
			--AND	idim.INVENTSTATUSID IN ('Available', 'Inbound' )  -- Excluding returns
			--AND ISNULL( wktbl.WORKID, '' ) <> '' -- Only work( includes cycle counts ), not inventory adjustments
			--AND wktbl.WORKTRANSTYPE = 2 -- Only sales orders
			--AND idim.WMSLOCATIONID IN ( '16-124W', '22-062X', '16-119B', '32-079D', '14-049Y' )
		GROUP BY idim.WMSLOCATIONID
	) as OH  ON loc.wmslocationID = OH.wmslocationid
*/	
CROSS APPLY 
(
	SELECT TOP 1
		price.VersionID 	AS Season
		, MAX( price.ActivationDate )  AS LastActDate
	FROM 
		inventitemprice price
	WHERE 
		price.ITEMID = isum.ITEMID AND price.inventdimid = price.inventdimid AND idim.DATAAREAID = price.DATAAREAID AND idim.PARTITION = price.PARTITION 
		AND idim.inventsiteid = 'HA USA'
	GROUP BY
		price.VERSIONID	
	ORDER BY
		LastActDate DESC
) price
WHERE 
	loc.inventlocationid = '4010' 
	AND loc.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking' ) 
	AND loc.zoneid NOT IN ( 'Current Pick') -- Exclude GCs, 3333s
	AND isum.PHYSICALINVENT > 0
GROUP BY
	loc.wmslocationid, loc.zoneid, isum.ITEMID, idim.INVENTCOLORID, idim.inventsizeid, price.Season, isum.physicalinvent
ORDER BY 
	LastMvmt ASC
