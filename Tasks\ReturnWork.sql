
-- Return work details

 SELECT 
    wktbl.WORKID                AS [WorkID]
    , wktbl.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'  AS [KYCreatedDT]
    , wkln.WMSLOCATIONID          AS [Location]
    , wkln.ZONEID               As [ZoneID]
    , wkln.ITEMID               AS [Item]
    , idim.INVENTCOLORID        AS [Color]
    , idim.INVENTSIZEID         AS [Size]
    , wkln.ORDERNUM             AS [Order]
    , wktbl.WORKCREATEDBY        AS [CreatedBy]
    , CAST(wkln.QTYWORK AS INT) AS [Qty]
 FROM 
    [DAX_Archive].[arc].WHSWORKLINE wkln
    INNER JOIN [DAX_Archive].[arc].WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wkln.DATAAREAID = wktbl.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION]
    INNER JOIN [DAX_Archive].[arc].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND wkln.[PARTITION] = idim.[PARTITION]
WHERE
 --WORKID = 'WK0029058121'
    --wkln.WORKCLASSID = 'Returns'
    wktbl.WORKTRANSTYPE  =   12
    AND wkln.WORKTYPE = 2
    AND wktbl.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 05:00:00 AM'
    AND wktbl.WORKSTATUS = 4
    AND wktbl.INVENTSITEID = 'HA USA'
    AND wktbl.INVENTLOCATIONID = '4010'
ORDER BY
    wktbl.CREATEDDATETIME ASC