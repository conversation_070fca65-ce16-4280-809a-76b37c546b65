
-- Finding reset replenishments going to Zone D
-- Hotfix went in on 11/2/2023

SELECT 
    wkln.WORKID
    , CASE 
        WHEN wktbl.WORKSTATUS = 0 
            THEN 'Open' 
        WHEN wktbl.WORKSTATUS = 1 
            THEN 'In process' 
        WHEN wktbl.WORKSTATUS = 4
            THEN 'Closed' 
        ELSE 'Unknown' 
    END                         AS [WorkStatus]
    , wktbl.CREATEDDATETIME     AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'     AS [CreatedDateTime]
    , wkln.WMSLOCATIONID        AS [Location]
    , wkln.ITEMID               AS [Item]
    , idim.INVENTCOLORID        AS [Color]
    , idim.INVENTSIZEID         AS [Size]
    , CAST(wkln.QTYWORK AS INT) AS [Qty]
FROM
    WHSWORKLINE wkln
    JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wkln.DATAAREAID = wktbl.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION]
    JOIN INVENTDIM idim ON wkln.INVENTDIMID = idim.INVENTDIMID AND wkln.DATAAREAID = idim.DATAAREAID AND wkln.[PARTITION] = idim.[PARTITION]
    JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.DATAAREAID = wkln.DATAAREAID AND loc.[PARTITION] = wkln.[PARTITION]
WHERE
    wktbl.WORKTEMPLATECODE = 'Reset Replenishment'
    AND wkln.LINENUM = 5
    AND loc.ZONEID = 'Zone D'
    AND wktbl.WORKSTATUS < 5
    AND wktbl.CREATEDDATETIME > '11/2/2023'
ORDER BY
    wktbl.CREATEDDATETIME DESC
