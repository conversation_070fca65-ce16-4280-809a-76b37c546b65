-- <PERSON><PERSON>

select itemId, sum(records) AS records
from (
              select itemId, count(*) as records
                from InventSum
                where dataareaId = 'ha'
                     and partition = 5637144576
                     and itemId in ('67155','41378','33081','43205','36754','33081','33175','33081')
                group by itemId

              union all

              select itemId, count(*)
                from WhsInventReserve
                where dataareaId = 'ha'
                     and partition = 5637144576
                     and itemId in ('67155','41378','33081','43205','36754','33081','33175','33081')
                group by itemId
     ) sub
group by itemId
order by records desc

--33081-S74-80
SELECT TOP 20 *
    FROM InventSum isum
    LEFT JOIN INVENTDIM idim ON isum.INVENTDIMID = idim.INVENTDIMID AND isum.[PARTITION] = idim.[PARTITION] AND isum.DATAAREAID = idim.DATAAREAID
    WHERE isum.ITEMID = '33081'
        AND idim.INVENTCOLORID = 'S74'
        AND idim.INVENTSIZEID ='80'
        AND idim.INVENTLOCATIONID ='4010'
        AND isum.LASTUPDDATEPHYSICAL >'6/5/2022'
        AND isum.POSTEDVALUE % 7.15 <> 0

--33175-S74-120
SELECT TOP 20 *
    FROM InventSum isum
    LEFT JOIN INVENTDIM idim ON isum.INVENTDIMID = idim.INVENTDIMID AND isum.[PARTITION] = idim.[PARTITION] AND isum.DATAAREAID = idim.DATAAREAID
    WHERE isum.ITEMID = '33175'
        AND idim.INVENTCOLORID = 'S74'
        AND idim.INVENTSIZEID ='120'
        AND idim.INVENTLOCATIONID ='4010'
        AND isum.LASTUPDDATEPHYSICAL >'6/2/2022'
        AND isum.POSTEDVALUE % 9.87 <> 0
