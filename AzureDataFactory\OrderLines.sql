
DECLARE @startDateTimePST AS DATETIME = '@{variables('start_date')}', 

    @endDateTimePST AS DATETIME = '@{variables('end_date')}'; 

 

DECLARE @startDateTimeUTC AS DATETIME = @startDateTimePST 

      AT TIME ZONE 'Pacific Standard Time' 

      AT TIME ZONE 'UTC', 

    @endDateTimeUTC AS DATETIME = @endDateTimePST 

      AT TIME ZONE 'Pacific Standard Time' 

      AT TIME ZONE 'UTC'; 

 

WITH SalesTable AS ( 

  SELECT  

    SALESID AS SalesID 

    ,HADWSALESID AS SFCCOrderID 

    ,SALESORIGINID AS SalesOrigin 

    ,C<PERSON><PERSON><PERSON>OUNT AS CustomerAccount 

    ,EMAIL AS OrderEmail 

    ,<PERSON><PERSON>IVER<PERSON>NA<PERSON> AS DeliveryName 

    ,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>LADDRESS AS DeliveryPostalAddressID 

    ,M<PERSON><PERSON>IEDDATETIME AS ModifiedDateTimeUTC 

    ,CREATEDDATETIME AS CreatedDateTimeUTC 

    ,HAORDERDATETIME AS OrderDateTimeUTC 

    ,CASE WHEN SALESSTATUS = 1 THEN 'BACKORDER' 

       WHEN SALESSTATUS = 2 THEN 'DELIVERED' 

       WHEN SALESSTATUS = 3 THEN 'INVOICED' 

       WHEN SALESSTATUS = 4 THEN 'CANCELLED' 

       ELSE 'NONE' END AS SALESSTATUS 

    ,PARTITION 

    ,DATAAREAID 

    ,RECID 

  FROM [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.SALESTABLE 

  WHERE MODIFIEDDATETIME >= @startDateTimeUTC  

    AND MODIFIEDDATETIME < @endDateTimeUTC 

    AND SALESORIGINID IN ('WEB','CALLCENTER')) 

SELECT 

  st.SalesID 

  --,st.SalesOrigin 

  ,sl.RECID AS AXSalesLineRecID 

  ,CONVERT(int, sl.LINENUM) AS AXOrderDetailLineNumber 

  ,CONCAT(sl.ITEMID, '-', inv.INVENTCOLORID, '-',inv.INVENTSIZEID) AS SKU 

  ,sl.HAMERCHPLANNINGCHANNEL AS MerchPlanningChannel 

  ,st.SalesStatus 

  ,sl.HARETURNREASONCODE AS ReturnReasonCode 

  ,rrc.DESCRIPTION AS ReturnReasonDescription 

  ,sl.QTYORDERED AS QuantityOrdered 

  ,sl.HAMSRP AS MSRP 

  ,sl.SALESPRICE AS SalesPriceAmt 

  ,sl.HACOSTAMOUNT AS CostPriceAmt 

  ,sl.LINEDISC AS DiscAmt 

  ,sl.LINEAMOUNT AS LineSalesPriceAmtExt 

  ,st.OrderDateTimeUTC 

  ,sl.MODIFIEDDATETIME AS LastModifiedUTC 

FROM [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.SALESLINE sl  

JOIN SalesTable st ON 

  st.SalesID = sl.SALESID 

  AND st.DATAAREAID = sl.DATAAREAID 

  AND st.PARTITION = sl.PARTITION 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.INVENTDIM inv ON 

  inv.INVENTDIMID = sl.INVENTDIMID 

  AND inv.DATAAREAID = sl.DATAAREAID 

  AND inv.PARTITION = sl.PARTITION 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.RETURNREASONCODE rrc ON 

  rrc.REASONCODEID = sl.HARETURNREASONCODE 

  AND rrc.DATAAREAID = sl.DATAAREAID 

  AND rrc.PARTITION = sl.PARTITION 

ORDER BY SALESID, LINENUM 