DECLARE @WorkId NVARCHAR(20)
DECLARE @ItemId NVARCHAR(20)
DECLARE @CutOff DATETIME
SET @CutOff = '7/4/2023';
--SET @WorkId = 'WK0027914005';
--SET @ItemId = '67887';


WITH logging
AS
(
	SELECT pl.CreatedDateTime,
		   SUBSTRING(AdditionalInfo, PATINDEX('%qtyToUpdateOrig%', AdditionalInfo)+17, 4) AS qtyToUpdateOrig,
		   SUBSTRING(AdditionalInfo, PATINDEX('%qtyToUpdateFinal%', AdditionalInfo)+18, 4) AS qtyToUpdateFinal,
		   SUBSTRING(AdditionalInfo, PATINDEX('%direction%', AdditionalInfo)+11, 7) AS direction,
		   SUBSTRING(AdditionalInfo, PATINDEX('%inventTransId%', AdditionalInfo)+15, 12) AS inventTransId,
		   SUBSTRING(AdditionalInfo, PATINDEX('%itemId%', AdditionalInfo)+8, 5) AS itemId,
		   SUBSTRING(AdditionalInfo, PATINDEX('%InventTransOriginId%', AdditionalInfo)+21, 10) AS inventTransOriginId
	  FROM processLog pl
	  WHERE pl.AOSName = 'PRODAIFAOS02'
	   AND pl.RecId >= **********
	   AND pl.InfoLogMessage IS NULL
	   AND pl.CreatedDateTime >= @cutOff
)

SELECT l.CreatedDateTime, l.qtyToUpdateOrig, l.qtyToUpdateFinal, l.inventTransId,
       ito.ReferenceId AS WorkId, ito.ItemId, ito.ReferenceCategory, 
	   it.Qty AS InventTransQty, it.datePhysical,
	   d.wmsLocationId, d.inventLocationId, d.InventSizeId, d.InventColorId, d.inventSiteId, d.LicensePlateId, d.InventStatusId, d.modifiedDateTime, d.modifiedBy, d.RecVersion,
	   wt.WaveId, wt.OrderNum, wt.LoadId, wt.ShipmentId, wt.workCreatedby, wt.workTemplateCode,
	   wa.Description, wa.WaveAttributeCode, wa.WaveTemplateName
  FROM logging l 
 LEFT JOIN inventTransOrigin ito WITH (NOLOCK)
    ON ito.RecId = l.inventTransOriginId
 LEFT JOIN inventTrans it WITH (NOLOCK)
    ON it.InventTransOrigin = ito.RecId
 LEFT JOIN inventDim d WITH (NOLOCK)
    ON d.InventDimId = it.InventDImId
   AND d.partition = it.partition
   AND d.dataareaId = it.dataareaId
 LEFT JOIN whsWorkTable wt WITH (NOLOCK)
    ON wt.WorkId = ito.ReferenceId
   AND wt.dataareaId = ito.dataareaid
   AND wt.partition = ito.partition
 LEFT JOIN whsWaveTable wa WITH (NOLOCK)
    ON wa.dataareaId = wt.dataareaId
   AND wa.partition = wt.partition
   AND wa.WaveId = wt.WaveId
 WHERE (@workId IS NULL OR @workId = '' OR wt.WorkID = @workId);


WITH aifLog
AS
(
	SELECT ml.MessageId, ml.CreatedDateTime, --CASE WHEN ml.Direction = 1 THEN 'OutBound' ELSE 'InBound' END AS Direction, ae.Description, dl.DocumentXML,
		   SUBSTRING(dl.DocumentXML, PATINDEX('%<_workLineId>%', dl.DocumentXML)+13, 10) AS WorkLineRecId,
		   SUBSTRING(dl.DocumentXML, PATINDEX('%<_qty>%', dl.DocumentXML)+6, 1) AS Qty,
		   REPLACE(SUBSTRING(dl.DocumentXML, PATINDEX('%<_userId>%', dl.DocumentXML)+9, 4),'<','') AS UserId
	  FROM AifMessageLog ml WITH (NOLOCK)
	 INNER JOIN AifExceptionsView ae WITH (NOLOCK)
		ON ae.MessageId = ml.MessageId
	 INNER JOIN aifDocumentLog dl WITH (NOLOCK)
		ON ml.messageId = dl.messageId
	 WHERE 1=1 
	   AND ml.actionId LIKE 'HAWarehouseVoiceLinkService.ConfirmPick'
	   AND ae.description = 'Insufficient inventory transactions'
	   --AND ml.CreatedDateTime >= '2023-06-08'
	   AND ml.CreatedDateTime >= @cutoff
)

SELECT al.CreatedDateTime, al.UserId AS requestUserId, al.Qty AS requestQty, cl.ClusterId,
	   wl.workId, wl.LineNum, wl.WorkStatus, wl.itemId, wl.InventQtyWork, wl.InventQtyRemain, wl.userId, wl.InventTransId, wl.workClassId, wl.OrderNum, wl.LoadId, wl.ShipmentId, wl.WorkClosedUtcDateTime, wl.ZoneId, 
	   d.InventSizeId, d.InventColorId, d.InventSiteId, d.InventLocationId, d.InventStatusId
  FROM AifLog al
  LEFT JOIN WhsWorkLine wl WITH (NOLOCK)
    ON wl.RecId = al.WorkLineRecId
  LEFT JOIN inventDim d WITH (NOLOCK)
    ON d.InventDimId = wl.InventDimid
   AND d.dataareaId = wl.dataareaId
   AND d.partition = wl.partition
  LEFT JOIN WhsWorkClusterLine cl WITH (NOLOCK)
    ON cl.workId = wl.WorkId
   AND cl.dataareaId = wl.DataAreaId
   AND cl.partition = wl.partition
 WHERE (@workId IS NULL OR @workId = '' OR wl.WorkID = @workId)
 ORDER BY al.CreatedDateTime


IF(@workId != '' AND NOT (@workId IS NULL))
BEGIN

SELECT wit.LineNum, wit.ItemId, wit.Qty, wit.TransDateTime, wit.InventTransIdFrom, wit.InventTransIdTo, wit.InventTransIdParent,
	   df.WmsLocationId AS FromWmsLocationId, df.InventLocationId AS FromInventLocationId, df.InventSizeId AS FromInventSizeId, df.InventColorId AS FromInventColorId, df.InventSiteId AS FromINventSiteId, df.InventStatusId AS FromInventStatusId,
	   dt.WmsLocationId AS ToWmsLocationId, dt.InventLocationId AS ToInventLocationId, dt.InventSizeId AS ToInventSizeId, dt.InventColorId AS ToInventColorId, dt.InventSiteId AS ToInventSiteId, dt.InventStatusId AS ToInventStatusId
  FROM WhsWorkInventTrans wit WITH (NOLOCK)
 INNER JOIN InventDim df WITH (NOLOCK)
    ON df.dataareaId = wit.dataareaId
   AND df.partition = wit.partition
   AND df.InventDimId = wit.InventDimIdFrom
 INNER JOIN InventDim dt WITH (NOLOCK)
    ON dt.dataareaId = wit.dataareaId
   AND dt.partition = wit.partition
   AND dt.InventDimId = wit.InventDimIdTo   
 WHERE wit.dataareaId = 'HA'
   AND wit.partition = 5637144576
   AND wit.workId = @WorkId
   AND (@ItemId = '' OR @ItemId IS NULL OR wit.ItemId = @Itemid)
 ORDER BY wit.TransDateTime

END;





 
