
/*
 Dividing the list of containers from a wave into batchs.
 This is useful when an AB document generation job fails, and the containers have to be reset

*/

-- Gemini 2.0 flash experimental
-- This query retrieves a list of ContainerIDs, grouped into batches of x, for a specific WAVEID.
-- It is optimized for SQL Server 2016 compatibility, avoiding the use of STRING_AGG.

DECLARE @WaveId nvarchar(11)  = 'WV000260451';
DECLARE @BatchSize INT          = 20; -- 20 is the maximum comma separated containers

WITH ContainerList AS (
    -- This CTE selects the necessary data from WHSCONTAINERTABLE and WHSSHIPMENTTABLE,
    -- joining them on SHIPMENTID and filtering by DATAAREAID, PARTITION, and WAVEID.
    SELECT 
        cnttbl.ContainerID,
        shptbl.SHIPMENTID,
        shptbl.WAVEID,
        -- Assigns a row number to each container within the result set, ordered by ContainerID.
        -- The PARTITION BY clause is removed as the WHERE clause already filters by a single WAVEID.
        ROW_NUMBER() OVER (ORDER BY cnttbl.ContainerID) AS rn
    FROM 
        WHSCONTAINERTABLE cnttbl
        JOIN WHSSHIPMENTTABLE shptbl ON cnttbl.SHIPMENTID = shptbl.SHIPMENTID AND cnttbl.DATAAREAID = 'ha' AND cnttbl.[PARTITION] = shptbl.[PARTITION]
        JOIN WHSWORKTABLE wktbl ON wktbl.CONTAINERID = cnttbl.CONTAINERID AND wktbl.DATAAREAID = 'ha'  AND wktbl.[PARTITION] = cnttbl.[PARTITION]
    WHERE
        shptbl.WAVEID = @WaveId -- Filter by the desired WAVEID.
        AND wktbl.WORKTEMPLATECODE <> '4010 Direct' -- Only AB work
        AND wktbl.WORKSTATUS < 4 -- Not closed
        AND wktbl.WORKTRANSTYPE = 2  -- Sales Orders
        AND cnttbl.CONTAINERTYPECODE = 'AB' -- Only AB containers
),
BatchedContainers AS (
    -- This CTE calculates the batch number for each container based on its row number.
    -- Each batch contains up to x containers.
    SELECT
        ContainerID,
        -- Calculates the batch number: (row number - 1) / @BatchSize + 1
        (rn - 1) / @BatchSize + 1 AS BatchNumber
    FROM ContainerList
)
SELECT
    -- This is the main SELECT statement that concatenates the ContainerIDs for each batch.
    STUFF((
        -- This subquery concatenates the ContainerIDs for a specific batch using FOR XML PATH('').
        SELECT ',' + ContainerID
        FROM BatchedContainers c2
        WHERE c2.BatchNumber = b.BatchNumber -- Correlates with the outer query to get containers for the current batch.
        ORDER BY ContainerID -- Ensures consistent ordering of ContainerIDs within each batch. This is CRUCIAL.
        FOR XML PATH('') -- Concatenates the ContainerIDs into a comma-separated string.
    ), 1, 1, '') AS ContainerIDs -- Removes the leading ', ' from the concatenated string using STUFF.
FROM (SELECT DISTINCT BatchNumber FROM BatchedContainers) b -- Selects distinct batch numbers for the outer query.
ORDER BY b.BatchNumber; -- Orders the results by batch number.

-- Key Improvements over the original query:
-- 1. Reduced scope of FOR XML PATH(''): It now operates on smaller batches, significantly improving performance.
-- 2. Pre-calculated BatchNumber: Avoids redundant calculations.
-- 3. Clearer logic and structure using CTEs.
-- 4. CRUCIAL: ORDER BY within the FOR XML PATH('') subquery to ensure consistent string ordering.

-- For SQL Server 2017 and onwards, use STRING_ADD
/*
WITH ContainerList AS (
    SELECT 
        cnttbl.ContainerID,
        shptbl.SHIPMENTID,
        shptbl.WAVEID,
        ROW_NUMBER() OVER (ORDER BY cnttbl.ContainerID) AS rn
    FROM 
        WHSCONTAINERTABLE cnttbl
        JOIN WHSSHIPMENTTABLE shptbl 
            ON cnttbl.SHIPMENTID = shptbl.SHIPMENTID 
            AND cnttbl.DATAAREAID = 'ha' 
            AND cnttbl.[PARTITION] = shptbl.[PARTITION]
    WHERE
        shptbl.WAVEID = 'WV000249784'
),
BatchedContainers AS (
    SELECT
        ContainerID,
        (rn - 1) / @BatcchSize + 1 AS BatchNumber
    FROM ContainerList
)
SELECT
    STRING_AGG(ContainerID, ',') WITHIN GROUP (ORDER BY ContainerID) AS ContainerIDs
FROM BatchedContainers
GROUP BY BatchNumber
ORDER BY BatchNumber;
*/

--sp_columns WHSCONTAINERTABLE
--sp_columns WHSSHIPMENTTABLE