
SELECT
    ITEMID
    , idim.INVENTCOLORID    AS [Color]
    , idim.INVENTSIZEID     AS [Size]
    , LOADID
    , ll.RECID
    , ll.INVENTTRANSID
    , PICKEDQTY
FROM 
    WHSLOADLINE ll
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = ll.INVENTDIMID AND ll.DATAAREAID = idim.DATAAREAID AND ll.[PARTITION] = idim.[PARTITION]
WHERE 
    loadid = '**********'
    --AND INVENTTRANSID = 'HA-470408651'
    --AND RECID = 5703867967

/*    
BEGIN TRAN;
UPDATE [DAX_PROD].[dbo].[WHSLOADLINE]
    SET PICKEDQTY  = 1
WHERE 
    loadid = '**********'
    --AND INVENTTRANSID IN( 'HA-467599411')
    AND RECID = 5710636053
COMMIT;
*/