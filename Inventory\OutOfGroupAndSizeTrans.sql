
-- Trynig to determine who is moving items out of zone( Group and size)
-- Focusing on SO items through demand replenishment


WITH PutTrans AS (
SELECT --TOP 20 
    wkln.WORKID
    , wktbl.WORKBUILDID                 AS [WorkCreation]
    , CASE 
        WHEN ISNULL(wktbl.WOR<PERSON>TEMPLATECODE, '') <> '' 
            THEN wktbl.WOR<PERSON><PERSON><PERSON><PERSON><PERSON>CODE ELSE 'Inventory Movement' 
    END AS [WorkTemplate]
    , CASE 
        WHEN wktbl.WORKTRANSTYPE = 11 THEN 'Replenishment'
        WHEN wktbl.WORKTRANSTYPE = 1 THEN 'Purchase Orders'
        WHEN wktbl.WORKTRANSTYPE = 7 THEN 'Inventory Movement'
        ELSE 'Unknown'
    END AS [MovmtType]
    , CASE WHEN ISNULL(wkln.WorkClassID, '') <> '' 
        THEN  wkln.WorkClassID ELSE 'Movement'                 
    END AS [WorkClass]
    , wkln.WMSLOCATIONID                AS [Location]
    , wkln.USERID
    /*
    , RIGHT( '0' + LTRIM(loc.AISLEID),2)       AS [Aisle_Table]
    , CASE 
        WHEN LEFT(wkln.WMSLOCATIONID,2) <> '17' 
            THEN LEFT(wkln.WMSLOCATIONID, 2 )
            ELSE
                CASE WHEN RIGHT(wkln.WMSLOCATIONID,1) < 'J' -- A-D, F-I
                    THEN '17A' ELSE '17Z'
                END
      END    AS [AisleId]
    , RIGHT( wkln.WMSLOCATIONID, 1 )    AS [Bin]
    , wkln.ITEMID                       AS [Item]  
    , idim.INVENTCOLORID                AS [Color]  
    , idim.INVENTSIZEID                 AS [Size]
    */
    , wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID AS [SKU]
    , CAST(wkln.QTYWORK AS INT)         AS [Qty]
    , loc.ZONEID                        AS [LocZoneId]
    , CASE WHEN fc.SLOTTIERVALUE  IS NOT NULL THEN fc.SLOTTIERVALUE ELSE 'N/A' END  AS [FcSlotTier]
   -- , COUNT(*)  OVER(PARTITION BY loc.AISLEID, loc.ZONEID) AS [AisleZonePicks]
    --, COUNT(*) OVER(PARTITION BY loc.AISLEID) AS [AislePicks]
    --, loc.LEVEL_
    --, wkln.WORKCLASSID
    --
FROM
    WHSWORKLINE wkln
    INNER JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wktbl.DATAAREAID = N'ha' AND wktbl.[PARTITION] = wkln.[PARTITION]
    INNER JOIN WMSLOCATION loc ON loc.wmslocationid = wkln.wmslocationid AND loc.DATAAREAID = N'ha' AND loc.PARTITION = 5637144576 AND loc.INVENTLOCATIONID = '4010'
    INNER JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
    LEFT JOIN HAFORECASTREPLENISHMENTTABLE fc  ON fc.ITEM = wkln.ITEMID AND idim.INVENTCOLORID =fc.COLOR AND idim.INVENTSIZEID = fc.SIZE_ AND idim.DATAAREAID = fc.DATAAREAID
WHERE
    wkln.WORKSTATUS = 4 -- Closed
    AND wkln.WORKTYPE = 2 -- Put
    AND loc.LOCPROFILEID IN ( 'Picking', 'Picking A', 'PalletPicking' ) 
    AND loc.ZONEID NOT IN ('Current Pick')  -- No GCs
    AND wkln.ITEMID IS NOT NULL
    --AND wkln.MODIFIEDDATETIME > '07/1/2024'
    AND (wkln.WORKCLASSID IN ('Fwd Throw', 'FwdThrow') OR wktbl.WORKTRANSTYPE = 7)
    AND wktbl.INVENTSITEID = 'HA USA'
    AND wktbl.INVENTLOCATIONID = '4010'
)
, PutD2 AS
(
    SELECT 
    /*
        WORKID
    --, loc.AISLEID
        , [Location]
        , [AisleId]
        , [Bin]
        , [Item]
        , [Qty]
        , [LocZoneId]*/
        *
        , CASE WHEN LEFT(LocZoneId,4) = LEFT(FcSlotTier, 4) THEN 'Yes' Else 'No' END AS [GroupSizeMatch]
    FROM
        PutTrans
)
SELECT 
   WORKID
   , WorkCreation
   , WorkTemplate
   , MovmtType
   , WorkClass
   , UserID
   , SKU
   , [Location]
   , LocZoneId
   , FcSlotTier
FROM 
    PutD2
WHERE
    GroupSizeMatch = 'No'
ORDER BY
    WORKID

