-- Query to find missing records on WHSWork<PERSON>lusterLine
-- The clusters identified must be deleted from WHSWorkClusterTable


DECLARE @DateToCheck DATE = GETUTCDATE() - 7

--SELECT clusterID FROM WHSWORKCLUSTERLINE WHERE MODIFIEDDATETIME> @DateToCheck GROUP BY CLUSTERID ORDER BY CLUSTERID

USE DAX_PROD

SELECT 
    wktbl.CLUSTERID
    , wktbl.MODIFIEDBY
    , wktbl.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'     AS [CreatedDateTime]
FROM 
    WHSWORKCLUSTERTABLE wktbl
    LEFT JOIN WHSWORKCLUSTERLINE wkln ON wkln.CLUSTERID = wktbl.CLUSTERID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION]
WHERE
    ISNULL(wkln.CLUSTERID, '') = ''
    AND wktbl.MODIFIEDDATETIME > @DateToCheck
    AND wktbl.MODIFIEDBY != 'wfexc'  -- Not reset clusters
--GROUP BY
  --  wkl.CLUSTERID, wkcl.MODIFIEDBY
ORDER BY
    wktbl.CLUSTERID;