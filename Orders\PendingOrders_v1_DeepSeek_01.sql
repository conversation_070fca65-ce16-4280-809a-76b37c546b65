
-- 3/7/2025
-- Query used on the Power BI report to get the orders that need to be shipped and are not shipped yet
-- The query is used to get the orders that are not shipped yet and are in the following modes: 1st Day, 2nd Day, 3rd Day, Saturday, International Economy
-- Faster than the other versions of the query

--3/14/2025
-- Adding the HoursSinceReleased column to the query
-- Including all orders that are not shipped yet, not only express orders

-- 4/2/2025
-- Adding the WholeSale column to the query

-- 4/9/2025
-- Adding the ShipmentStatus column to the query
-- 5/5/2025
-- Optimized using DeepSeek, with DeepThink(R1)

--5/6/2025: it's squaring the units for "In Process" shipments

-- 3/7/2025
-- Query used on the Power BI report to get the orders that need to be shipped and are not shipped yet
-- The query is used to get the orders that are not shipped yet and are in the following modes: 1st Day, 2nd Day, 3rd Day, Saturday, International Economy
-- Faster than the other versions of the query

--3/14/2025
-- Adding the HoursSinceReleased column to the query
-- Including all orders that are not shipped yet, not only express orders

-- 4/2/2025
-- Adding the WholeSale column to the query

-- 4/9/2025
-- Adding the ShipmentStatus column to the query
-- 5/5/2025
-- Optimized using DeepSeek, with DeepThink(R1)

-- 5/6/2025: it's squaring the units for "In Process" shipments
-- 5/12/2025: fixed the units for "In Process" shipments
-- 5/14/2025: Moved the filter for shipped shipments and open orders to the WHERE clause

DECLARE @CurrentDateTime datetime = GETUTCDATE();

WITH orders AS (
    SELECT
        wst.ORDERNUM,
        st.CUSTGROUP,
        wst.SHIPMENTID,
        wst.SHIPMENTSTATUS, 
        wst.HALOCDIRECTFAIL,    
        cnttbl.CONTAINERID,    
        wktbl.WORKID,
        wktbl.FROZEN,
        wktbl.WORKSTATUS,
        wkusr.USERNAME                                      AS [LockedUser],
        voicepick.USERNAME                                  AS [VoicePickUser],
        vput.[STATUS]                                       AS [PutStatus],
        wkusr1.USERNAME                                     AS [VoicePutUser],
        COALESCE(wkcl.CLUSTERID, 'N/A')                     AS [ClusterId],
        COALESCE(wkct.CLUSTERPROFILEID, 'N/A')              AS [ClusterProfile],
        ISNULL(SUM(cntln.QTY), 0)                           AS [CntUnits],
        wst.MODECODE,
        wst.WAVEID,
        wvt.WAVETEMPLATENAME,
        ll.Units                                            AS [LoadUnits], 
        ll.MinCreated                                       AS [ReleasedToWH],
        replen.DEMANDWORKID
    
    -- Base Tables
    FROM SALESTABLE st
    INNER JOIN WHSSHIPMENTTABLE wst WITH (NOLOCK) 
        ON st.SALESID = wst.ORDERNUM 
        AND st.[PARTITION] = wst.[PARTITION]
        AND wst.DATAAREAID = st.DATAAREAID
        AND wst.SHIPMENTSTATUS < 5 -- Exclude shipped orders
        AND st.SALESSTATUS = 1 -- Open order only
    
    INNER JOIN WHSWAVETABLE wvt WITH (NOLOCK) 
        ON wvt.WAVEID = wst.WAVEID 
        AND wvt.[PARTITION] = wst.[PARTITION]
    
    -- Container Related
    LEFT JOIN WHSCONTAINERTABLE cnttbl WITH (NOLOCK) 
        ON wst.SHIPMENTID = cnttbl.SHIPMENTID 
        AND cnttbl.DATAAREAID = 'ha'
        AND cnttbl.[PARTITION] = wst.[PARTITION]
        -- Trying to reduce the join size
        --AND (cnttbl.SHIPCARRIERTRACKINGNUM = '' OR cnttbl.SHIPCARRIERTRACKINGNUM IS NULL)  -- Exclude shipped orders
    
    LEFT JOIN WHSCONTAINERLINE cntln WITH (NOLOCK) 
        ON cntln.CONTAINERID = cnttbl.CONTAINERID 
        AND cntln.[PARTITION] = cnttbl.[PARTITION]
    
    -- Work Related
    LEFT JOIN WHSWORKTABLE wktbl WITH (NOLOCK) 
        ON cnttbl.CONTAINERID = wktbl.CONTAINERID 
        AND wktbl.DATAAREAID = 'ha'
    -- This is more efficient than an inline query
    LEFT JOIN (
        SELECT 
            vip.WORKID,
            vip.[PARTITION],
            wkuser.USERNAME,
            ROW_NUMBER() OVER (PARTITION BY vip.WORKID ORDER BY vip.[STATUS]) AS rn
        FROM HAVOICEINTEGRATIONQUEUEPICKS vip
        INNER JOIN WHSWORKUSER wkuser WITH (NOLOCK) ON wkuser.USERID = vip.WORKUSER
                AND wkuser.DATAAREAID = 'ha'
        WHERE vip.STATUS < 5
        ) voicepick ON voicepick.WORKID = wktbl.WORKID 
            AND voicepick.[PARTITION] = wktbl.[PARTITION]
            AND voicepick.rn = 1 -- Get the first one
    -- Cluster Related
    LEFT JOIN WHSWORKCLUSTERLINE wkcl WITH (NOLOCK) 
        ON wkcl.WORKID = wktbl.WORKID 
    
    LEFT JOIN WHSWORKCLUSTERTABLE wkct WITH (NOLOCK) 
        ON wkct.CLUSTERID = wkcl.CLUSTERID 
    
    -- Shipping
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs WITH (NOLOCK) 
        ON hacs.CARTONID = cnttbl.CONTAINERID 
        -- Trying to reduce the number of records in this join
        AND hacs.[PARTITION] = cnttbl.[PARTITION]
        --AND hacs.TRACKINGNUMBER IS NULL -- Exclude manually shipped orders
    
    -- Users
    LEFT JOIN WHSWORKUSER wkusr WITH (NOLOCK) 
        ON wkusr.USERID = wktbl.LOCKEDUSER 
    
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput WITH (NOLOCK) 
        ON vput.WORKID = wktbl.WORKID 
    
    LEFT JOIN WHSWORKUSER wkusr1 WITH (NOLOCK) 
        ON wkusr1.USERID = vput.WORKUSER 
    
    -- Replenishment
    LEFT JOIN WHSREPLENWORKLINK replen WITH (NOLOCK) 
        ON replen.DEMANDWORKID = wktbl.WORKID 
    
    -- Load Line Aggregation
    LEFT JOIN (
        SELECT 
            SHIPMENTID, ORDERNUM, LOADID,
            SUM(QTY) AS Units,
            MIN(CREATEDDATETIME) AS MinCreated
        FROM WHSLOADLINE 
        WHERE DATAAREAID = 'ha'
        GROUP BY SHIPMENTID, ORDERNUM, LOADID
    ) ll ON ll.SHIPMENTID = wst.SHIPMENTID 
        AND ll.ORDERNUM = st.SALESID 
        AND ll.LOADID = wst.LOADID
    
    WHERE 
        hacs.TRACKINGNUMBER IS NULL -- Exclude manually shipped orders
        AND (cnttbl.SHIPCARRIERTRACKINGNUM = '' OR cnttbl.SHIPCARRIERTRACKINGNUM IS NULL)  -- Exclude shipped orders
    GROUP BY 
        wst.ORDERNUM, st.CUSTGROUP, wst.SHIPMENTID, wst.SHIPMENTSTATUS, 
        wst.HALOCDIRECTFAIL, cnttbl.CONTAINERID, wktbl.WORKID, wktbl.FROZEN,
        wktbl.WORKSTATUS, wkusr.USERNAME, voicepick.USERNAME, vput.[STATUS],
        wkusr1.USERNAME, wkcl.CLUSTERID, wkct.CLUSTERPROFILEID, wst.MODECODE,
        wst.WAVEID, wvt.WAVETEMPLATENAME, ll.Units, ll.MinCreated, replen.DEMANDWORKID
)
SELECT 
    ORDERNUM                                                AS [OrderNum],
    CASE 
        WHEN CUSTGROUP LIKE 'W%' 
        THEN 'Yes' ELSE 'No' 
    END                                                     AS [WholeSale],
    MODECODE                                                AS [ShipMethod],
    CASE 
        WHEN MODECODE IN ('1D', '2D', '3D', 'SA', 'IE') 
        THEN 'Yes' ELSE 'No' 
    END                                                     AS [IsRush],
    FORMAT(ReleasedToWH 
            AT TIME ZONE 'UTC' 
            AT TIME ZONE 'Eastern Standard Time', 
          'MMM dd yyyy hh:mmtt', 'en-US')                   AS [ReleasedToWH],
    FORMAT(ReleasedToWH, 'MMM dd, yyyy')                    AS [SLA_Date],
    DATEDIFF(HOUR, ReleasedToWH, @CurrentDateTime)          AS [HoursSinceReleased],
    CAST(COALESCE(CntUnits, LoadUnits, 0) AS INT)           AS [Units],
    SHIPMENTID                                              AS [ShipmentId],
    CASE 
        WHEN SHIPMENTSTATUS = 0 THEN 'Open'
        WHEN SHIPMENTSTATUS = 1 THEN 'Waved'
        WHEN SHIPMENTSTATUS = 2 THEN 'In process'
        WHEN SHIPMENTSTATUS = 3 THEN 'In packing'
        WHEN SHIPMENTSTATUS = 4 THEN 'Loaded'
        ELSE 'Received'
    END                                                     AS [ShipmentStatus],
    IIF(HALOCDIRECTFAIL = 1, 'Yes', 'No')                   AS [LDF],
    COALESCE(CONTAINERID, 'N/A')                            AS [ContainerId],
    COALESCE(WORKID, 'N/A')                                 AS [WORKID],
    CASE 
        WHEN FROZEN = 1 THEN 'Yes' 
        WHEN FROZEN = 0 THEN 'No' 
        ELSE 'N/A' 
    END                                                     AS [WorkBlocked],
    CASE 
        WHEN WORKSTATUS = 0 THEN 'Open' 
        WHEN WORKSTATUS = 1 THEN 'In process'
        WHEN WORKSTATUS = 4 THEN 'Closed'
        ELSE 'N/A'
    END                                                     AS [WorkStatus],
    
    CASE 
        WHEN DEMANDWORKID IS NOT NULL 
        THEN 'Yes' ELSE 'No' 
    END                                                     AS [NeedsReplen],
    
    COALESCE(LockedUser, 'N/A')                             AS [LockedBy],
    COALESCE(VoicePutUser, VoicePickUser, 'N/A')            AS [VoiceUser],
    CASE 
        WHEN PutStatus IS NULL THEN 
            CASE 
                WHEN VoicePickUser IS NULL 
                THEN 'N/A' ELSE 'In Progress' 
            END
        ELSE 
            CASE PutStatus
                WHEN 0 THEN 'Pending'
                WHEN 2 THEN 'Completed'
                WHEN 3 THEN 'Error'
                WHEN 4 THEN 'Reset'
                WHEN 5 THEN 'Manually Picked'
                WHEN 6 THEN 'Canceled'
                ELSE 'N/A'
            END
    END                                                     AS [VoiceStatus],
    ClusterId,
    ClusterProfile,
    WAVEID                                                  AS [WaveId],
    WAVETEMPLATENAME                                        AS [WaveTemplate]
FROM orders
WHERE
    1 = 1
    --AND ORDERNUM IN ('48696854', '48697593','48697302', '48698332','48698234','48698311','48698038')
ORDER BY HoursSinceReleased DESC;
