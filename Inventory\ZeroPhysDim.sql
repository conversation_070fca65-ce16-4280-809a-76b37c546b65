-- Zero Physical Dimensions


    SELECT  CASE    WHEN isnull([ECORESITEMCOLORNAME],'' ) = '' THEN ITEMID  
                    WHEN isnull([ECORESITEMSIZENAME], '' ) = '' THEN ITEMID + '-' + [ECORESITEMCOLORNAME]
                    ELSE ITEMID + '-' + [ECORES<PERSON>EMCOLORNAME] + '-' + [ECORESITEMSIZENAME]
            END AS 'SKU',
    CONVERT( DECIMAL( 10, 2 ), [WEIGHT],    0) AS 'Weight',
    CONVERT( DECIMAL( 10, 2 ), [DEPTH],     0 ) AS 'Depth',
    CONVERT( DECIMAL( 10, 2 ), [HEIGHT],    0 ) AS 'Height',
    CONVERT( DECIMAL( 10, 2 ), [WIDTH],     0 ) AS 'Width',
    CONVERT( DECIMAL( 10, 2 ), ISNULL( [DEPTH], 0 ) * ISNULL( [WIDTH], 0) * ISNULL( [HEIGHT], 0 ), 0 ) AS 'Volume'  
    FROM [DAX_PROD].[dbo].[WHSPHYSDIMUOM]  
    --WH<PERSON><PERSON> CONVERT(DECIMAL(10,2),ISNULL([DEPTH],0) * ISNULL([WIDTH],0) * ISNULL([HEIGHT],0),0) = 0.00
    ORDER BY SKU
/*
SELECT *
FROM
  WHSPHYSDIMUOM
WHERE
        ITEMID + '-' + ECORESITEMCOLORNAME + '-' + ECORESITEMSIZENAME = '80985-SR4-S'
*/
