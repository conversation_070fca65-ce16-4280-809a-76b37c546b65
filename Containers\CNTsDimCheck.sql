

-- Checking for containerization issues with SM containers
SELECT
    cnttbl.ContainerID
    , cnttbl.CONTAINERTYPECODE AS CNTType
    , CASE    WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wktbl.CREATEDDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wktbl.CREATEDDATETIME ) -- No DST
        WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, wktbl.CREATEDDATETIME ) < 8 OR DATEPART( dd, wktbl.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, wktbl.CREATEDDATETIME ) -- No DST
                    WHEN DATEPART( dd, wktbl.CREATEDDATETIME ) - DATEPART( w, wktbl.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wktbl.CREATEDDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wktbl.CREATEDDATETIME )
             END
        WHEN DATEPART( dd, wktbl.CREATEDDATETIME ) - DATEPART( w, wktbl.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wktbl.CREATEDDATETIME ) -- EST
        ELSE DATEADD( hh, - 4, wktbl.CREATEDDATETIME ) -- EDT
    END                             AS CreatedDateTime
    , SUM( CONVERT( DECIMAL( 10, 2 ), physdim.DEPTH * physdim.WIDTH * physdim.HEIGHT * cntln.Qty, 1 ) ) AS Volume
    , wavtbl.WaveTemplateName
FROM WHSCONTAINERTABLE cnttbl
LEFT JOIN WHSCONTAINERLINE cntln  ON cnttbl.CONTAINERID = cntln.CONTAINERID AND cntln.[PARTITION] = cnttbl.PARTITION AND cnttbl.DATAAREAID = 'ha'
LEFT JOIN WHSWORKTABLE wktbl ON wktbl.CONTAINERID = cnttbl.CONTAINERID AND cntln.[PARTITION] = wktbl.PARTITION AND wktbl.DATAAREAID = 'ha'
LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = cntln.INVENTDIMID AND cntln.[PARTITION] = idim.PARTITION AND idim.DATAAREAID = 'ha'
LEFT JOIN WHSPHYSDIMUOM physdim ON physdim.ITEMID = cntln.ITEMID AND physdim.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physdim.ECORESITEMSIZENAME = idim.INVENTSIZEID
LEFT JOIN WHSWAVETABLE wavtbl ON wavtbl.WAVEID = wktbl.WAVEID AND wavtbl.[PARTITION] = wktbl.[PARTITION] AND wavtbl.DATAAREAID = 'ha'
--LEFT JOIN WHSWAVETEMPLATELINE wavtl ON wavtbl.WAVETEMPLATENAME = wavtl.WAVETEMPLATENAME AND wavtl.[PARTITION] = wavtbl.[PARTITION] AND wavtl.DATAAREAID = 'ha'
WHERE 
   --cnttbl.CONTAINERID ='CN012769304'
    --wktbl.WAVEID = 'WV000174587' AND
     --wktbl.CREATEDDATETIME > '6/6/2022' AND
     --wktbl.CREATEDDATETIME BETWEEN  '11/1/2022 04:00:00' AND '11/3/2022 20:00:00' --GETUTCDATE() 
     wktbl.CREATEDDATETIME BETWEEN  '11/11/2022 17:25:00' AND '11/14/2022 15:27:35' -- CG volume set to 1200
     AND cnttbl.CONTAINERTYPECODE IN ( 'SM', 'MD', 'LG') 
     --AND cnttbl.CONTAINERID ='CN012951648'
    --AND wavtl.POSTMETHODNAME = 'containerization'
    --AND wavtl.WAVESTEPCODE = 'MainPack'
GROUP BY
    cnttbl.CONTAINERID, cnttbl.CONTAINERTYPECODE, wktbl.CREATEDDATETIME, wavtbl.WAVETEMPLATENAME
HAVING MAX( physdim.HEIGHT ) < 6.25
    AND SUM( CONVERT( DECIMAL( 10, 2 ), physdim.DEPTH * physdim.WIDTH * physdim.HEIGHT * cntln.Qty, 1 ) ) BETWEEN 0.76 AND 1000

UNION ALL

SELECT
    cnttbl.ContainerID
    , cnttbl.CONTAINERTYPECODE AS CNTType
    , CASE    WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wktbl.CREATEDDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wktbl.CREATEDDATETIME ) -- No DST
        WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, wktbl.CREATEDDATETIME ) < 8 OR DATEPART( dd, wktbl.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, wktbl.CREATEDDATETIME ) -- No DST
                    WHEN DATEPART( dd, wktbl.CREATEDDATETIME ) - DATEPART( w, wktbl.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wktbl.CREATEDDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wktbl.CREATEDDATETIME )
             END
        WHEN DATEPART( dd, wktbl.CREATEDDATETIME ) - DATEPART( w, wktbl.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wktbl.CREATEDDATETIME ) -- EST
        ELSE DATEADD( hh, - 4, wktbl.CREATEDDATETIME ) -- EDT
    END                             AS CreatedDateTime
    , SUM( CONVERT( DECIMAL( 10, 2 ), physdim.DEPTH * physdim.WIDTH * physdim.HEIGHT * cntln.Qty, 1 ) ) AS Volume
    , wavtbl.WaveTemplateName
FROM WHSCONTAINERTABLE cnttbl
LEFT JOIN WHSCONTAINERLINE cntln  ON cnttbl.CONTAINERID = cntln.CONTAINERID AND cntln.[PARTITION] = cnttbl.PARTITION AND cnttbl.DATAAREAID = 'ha'
LEFT JOIN WHSWORKTABLE wktbl ON wktbl.CONTAINERID = cnttbl.CONTAINERID AND cntln.[PARTITION] = wktbl.PARTITION AND wktbl.DATAAREAID = 'ha'
LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = cntln.INVENTDIMID AND cntln.[PARTITION] = idim.PARTITION AND idim.DATAAREAID = 'ha'
LEFT JOIN WHSPHYSDIMUOM physdim ON physdim.ITEMID = cntln.ITEMID AND physdim.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physdim.ECORESITEMSIZENAME = idim.INVENTSIZEID
LEFT JOIN WHSWAVETABLE wavtbl ON wavtbl.WAVEID = wktbl.WAVEID AND wavtbl.[PARTITION] = wktbl.[PARTITION] AND wavtbl.DATAAREAID = 'ha'
--LEFT JOIN WHSWAVETEMPLATELINE wavtl ON wavtbl.WAVETEMPLATENAME = wavtl.WAVETEMPLATENAME AND wavtl.[PARTITION] = wavtbl.[PARTITION] AND wavtl.DATAAREAID = 'ha'
WHERE 
     --wktbl.WAVEID = 'WV000174587' AND
     --wktbl.CREATEDDATETIME > '6/6/2022' AND
     --wktbl.CREATEDDATETIME BETWEEN  '11/1/2022 04:00:00' AND '11/3/2022 20:00:00' --GETUTCDATE() 
     wktbl.CREATEDDATETIME BETWEEN  '11/14/2022 15:27:36' AND GETUTCDATE() -- CG volume set to 1200
     AND cnttbl.CONTAINERTYPECODE IN ( 'SM', 'MD', 'LG') 
    --AND cnttbl.CONTAINERID ='CN012951648'
    --AND wavtl.POSTMETHODNAME = 'containerization'
    --AND wavtl.WAVESTEPCODE = 'MainPack'
GROUP BY
    cnttbl.CONTAINERID, cnttbl.CONTAINERTYPECODE, wktbl.CREATEDDATETIME, wavtbl.WAVETEMPLATENAME

HAVING MAX( physdim.HEIGHT ) < 6.25
    AND SUM( CONVERT( DECIMAL( 10, 2 ), physdim.DEPTH * physdim.WIDTH * physdim.HEIGHT * cntln.Qty, 1 ) ) BETWEEN 0.76 AND 1200
ORDER BY cnttbl.CONTAINERID



/*

--SELEwktbl GETUTCDATE() AS UTCDATE, GETUTCDATE() -7 AS SevenDaysAgo
SELEwktbl 
    TOP 10 *
FROM 
    WHSWAVETEMPLATELINE
WHERE
    POSTMETHODNAME = 'containerization'
    AND WAVESTEPCODE = 'MainPack'
*/




