-- DEBUG VERSION: Shows detailed breakdown to verify logic
-- Use this to check if the occupancy calculation is working correctly

WITH 
-- Pre-filter inventory data to reduce dataset size
FilteredInventory AS (
    SELECT 
        idim.WMSLOCATIONID,
        isum.ITEMID,
        idim.INVENTCOLORID,
        idim.INVENTSIZEID,
        isum.PHYSICALINVENT,
        isum.RESERVPHYSICAL
    FROM INVENTSUM isum WITH (NOLOCK)
    INNER JOIN INVENTDIM idim WITH (NOLOCK) 
        ON isum.INVENTDIMID = idim.INVENTDIMID 
        AND isum.DATAAREAID = 'ha' 
        AND isum.[PARTITION] = 5637144576
        AND idim.DATAAREAID = 'ha'
        AND idim.[PARTITION] = 5637144576
        AND idim.INVENTLOCATIONID = '4010' -- Filter early for better performance
    WHERE isum.PHYSICALINVENT > 0 -- Only locations with inventory
),
-- Pre-filter work lines to reduce dataset size
FilteredWorkLines AS (
    SELECT 
        wkln.WMSLOCATIONID,
        wkln.ITEMID,
        idim.INVENTCOLORID,
        idim.INVENTSIZEID,
        wkln.QTYWORK
    FROM WHSWORKLINE wkln WITH (NOLOCK)
    INNER JOIN INVENTDIM idim WITH (NOLOCK) 
        ON idim.INVENTDIMID = wkln.INVENTDIMID 
        AND wkln.DATAAREAID = 'ha' 
        AND wkln.[PARTITION] = 5637144576
        AND idim.DATAAREAID = 'ha'
        AND idim.[PARTITION] = 5637144576
    WHERE wkln.WORKTYPE = 2 -- Put
        AND wkln.WORKSTATUS < 4 -- Not completed
        AND wkln.QTYWORK >= 1 -- Only meaningful work quantities
),
-- Pre-filter locations to reduce dataset size
FilteredLocations AS (
    SELECT 
        loc.WMSLOCATIONID,
        loc.ZONEID,
        loc.LOCPROFILEID
    FROM WMSLOCATION loc WITH (NOLOCK)
    WHERE loc.INVENTLOCATIONID = '4010' 
        AND loc.LOCPROFILEID IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking')
        AND loc.ZONEID NOT IN ('Current Pick') -- Exclude GCs, 3333s
        AND loc.DATAAREAID = 'ha'
        AND loc.[PARTITION] = 5637144576
),
-- Combine inventory and work data efficiently - matching original logic
LocationData AS (
    SELECT  
        loc.WMSLOCATIONID AS [Location],
        loc.ZONEID AS ZoneId,
        loc.LOCPROFILEID AS LocProfile,
        -- Match original SKU logic exactly
        CASE WHEN inv.PHYSICALINVENT > 0 
            THEN inv.ITEMID + '-' + inv.INVENTCOLORID + '-' + inv.INVENTSIZEID
            ELSE CASE WHEN wk.QTYWORK >= 1 
                THEN wk.ITEMID + '-' + wk.INVENTCOLORID + '-' + wk.INVENTSIZEID
                ELSE NULL END
        END AS SKU,
        COALESCE(inv.RESERVPHYSICAL, 0) AS Reserved,
        -- Match original Occupied logic exactly
        CASE WHEN COALESCE(inv.PHYSICALINVENT, 0) >= 1 THEN 1 ELSE 0 END AS Occupied,
        -- Match original IncomingWork logic exactly  
        CASE WHEN COALESCE(wk.QTYWORK, 0) >= 1 THEN 1 ELSE 0 END AS IncomingWork,
        COALESCE(inv.RESERVPHYSICAL, 0) AS RESERVPHYSICAL,
        -- Debug fields
        COALESCE(inv.PHYSICALINVENT, 0) AS PhysicalInvent,
        COALESCE(wk.QTYWORK, 0) AS QtyWork
    FROM FilteredLocations loc
    LEFT JOIN FilteredInventory inv 
        ON loc.WMSLOCATIONID = inv.WMSLOCATIONID
    LEFT JOIN FilteredWorkLines wk 
        ON loc.WMSLOCATIONID = wk.WMSLOCATIONID
    -- Include ALL locations from FilteredLocations, just like the original query
),
-- Calculate final occupancy using optimized formula - include ALL locations
ONH_PWC AS (
    SELECT
        [Location],
        ZoneId,
        SKU,
        PhysicalInvent,
        QtyWork,
        Occupied,
        IncomingWork,
        -- Optimized MAX calculation: 0.5*(a + b + |a - b|) = MAX(a, b)
        CONVERT(DECIMAL(10, 0), 
            0.5 * (Occupied + IncomingWork + ABS(Occupied - IncomingWork))
        ) AS FinalOccupied
    FROM LocationData
    -- Include ALL locations, even those without SKU (empty locations)
)
-- Show summary statistics
SELECT
    COUNT(*) AS TotalLocations,
    SUM(CASE WHEN PhysicalInvent > 0 THEN 1 ELSE 0 END) AS LocationsWithInventory,
    SUM(CASE WHEN QtyWork > 0 THEN 1 ELSE 0 END) AS LocationsWithWork,
    SUM(CASE WHEN SKU IS NOT NULL THEN 1 ELSE 0 END) AS LocationsWithSKU,
    SUM(Occupied) AS OccupiedCount,
    SUM(IncomingWork) AS IncomingWorkCount,
    SUM(FinalOccupied) AS FinalOccupiedCount,
    CAST(SUM(FinalOccupied) AS DECIMAL(10,4)) / COUNT(*) AS PercentFilled
FROM ONH_PWC;

-- Show sample records for verification (need to repeat CTE for separate query)
WITH
-- Pre-filter inventory data to reduce dataset size
FilteredInventory AS (
    SELECT
        idim.WMSLOCATIONID,
        isum.ITEMID,
        idim.INVENTCOLORID,
        idim.INVENTSIZEID,
        isum.PHYSICALINVENT,
        isum.RESERVPHYSICAL
    FROM INVENTSUM isum WITH (NOLOCK)
    INNER JOIN INVENTDIM idim WITH (NOLOCK)
        ON isum.INVENTDIMID = idim.INVENTDIMID
        AND isum.DATAAREAID = 'ha'
        AND isum.[PARTITION] = 5637144576
        AND idim.DATAAREAID = 'ha'
        AND idim.[PARTITION] = 5637144576
        AND idim.INVENTLOCATIONID = '4010'
    WHERE isum.PHYSICALINVENT > 0
),
FilteredWorkLines AS (
    SELECT
        wkln.WMSLOCATIONID,
        wkln.ITEMID,
        idim.INVENTCOLORID,
        idim.INVENTSIZEID,
        wkln.QTYWORK
    FROM WHSWORKLINE wkln WITH (NOLOCK)
    INNER JOIN INVENTDIM idim WITH (NOLOCK)
        ON idim.INVENTDIMID = wkln.INVENTDIMID
        AND wkln.DATAAREAID = 'ha'
        AND wkln.[PARTITION] = 5637144576
        AND idim.DATAAREAID = 'ha'
        AND idim.[PARTITION] = 5637144576
    WHERE wkln.WORKTYPE = 2 AND wkln.WORKSTATUS < 4 AND wkln.QTYWORK >= 1
),
FilteredLocations AS (
    SELECT
        loc.WMSLOCATIONID,
        loc.ZONEID,
        loc.LOCPROFILEID
    FROM WMSLOCATION loc WITH (NOLOCK)
    WHERE loc.INVENTLOCATIONID = '4010'
        AND loc.LOCPROFILEID IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking')
        AND loc.ZONEID NOT IN ('Current Pick')
        AND loc.DATAAREAID = 'ha'
        AND loc.[PARTITION] = 5637144576
),
LocationData AS (
    SELECT
        loc.WMSLOCATIONID AS [Location],
        loc.ZONEID AS ZoneId,
        loc.LOCPROFILEID AS LocProfile,
        CASE WHEN inv.PHYSICALINVENT > 0
            THEN inv.ITEMID + '-' + inv.INVENTCOLORID + '-' + inv.INVENTSIZEID
            ELSE CASE WHEN wk.QTYWORK >= 1
                THEN wk.ITEMID + '-' + wk.INVENTCOLORID + '-' + wk.INVENTSIZEID
                ELSE NULL END
        END AS SKU,
        CASE WHEN COALESCE(inv.PHYSICALINVENT, 0) >= 1 THEN 1 ELSE 0 END AS Occupied,
        CASE WHEN COALESCE(wk.QTYWORK, 0) >= 1 THEN 1 ELSE 0 END AS IncomingWork,
        COALESCE(inv.PHYSICALINVENT, 0) AS PhysicalInvent,
        COALESCE(wk.QTYWORK, 0) AS QtyWork
    FROM FilteredLocations loc
    LEFT JOIN FilteredInventory inv ON loc.WMSLOCATIONID = inv.WMSLOCATIONID
    LEFT JOIN FilteredWorkLines wk ON loc.WMSLOCATIONID = wk.WMSLOCATIONID
),
ONH_PWC AS (
    SELECT
        [Location], ZoneId, SKU, PhysicalInvent, QtyWork, Occupied, IncomingWork,
        CONVERT(DECIMAL(10, 0),
            0.5 * (Occupied + IncomingWork + ABS(Occupied - IncomingWork))
        ) AS FinalOccupied
    FROM LocationData
)
SELECT TOP 20
    Location,
    ZoneId,
    SKU,
    PhysicalInvent,
    QtyWork,
    Occupied,
    IncomingWork,
    FinalOccupied
FROM ONH_PWC
ORDER BY FinalOccupied DESC, Location;
