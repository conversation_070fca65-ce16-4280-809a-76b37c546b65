

-- Shipments by type. 

SELECT
    DATEPART(month, (CAST( shptbl.MOD<PERSON>IE<PERSON><PERSON><PERSON><PERSON>ME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS Date)) )  AS [MonthOrd]
    , DATENAME(month, (CAST( shptbl.MODIFIED<PERSON><PERSON><PERSON><PERSON> AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS Date)))  AS [Month]
    --shptbl.SHIPMENTID       AS [ShipmentID]
    --, wktbl.ORDERNUM        AS [SalesID]
    , SUM( shptbl.HAQUERYGIFTCARD ) AS [TotalGCs]
    , SUM( CASE WHEN shptbl.HASINGLESKU = 1 THEN 
               CASE WHEN shptbl.HAQUERYGIFTCARD = 1 THEN 0 ELSE 1 END
            ELSE 0 END) AS [Singles]
    , SUM( CASE WHEN shptbl.HASINGLESKU = 0 THEN 
                CASE WHEN shptbl.HAQUERYGIFTCARD = 1 THEN 0 ELSE 1 END
                    ELSE 0 END) AS [Multis]
    --, CASE WHEN shptbl.HASINGLESKU = 0 THEN 1 ELSE 0 END AS [Multi]
    
FROM 
    WHSSHIPMENTTABLE shptbl
    LEFT JOIN WHSWORKTABLE wktbl ON shptbl.SHIPMENTID = wktbl.SHIPMENTID AND wktbl.DATAAREAID = shptbl.DATAAREAID AND wktbl.[PARTITION] = shptbl.[PARTITION]
WHERE
    wktbl.WORKSTATUS < 5
    AND shptbl.LOADDIRECTION = 2
    AND wktbl.INVENTSITEID = 'HA USA'
GROUP BY
    DATEPART(month, (CAST( shptbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS Date)) )
    , DATENAME(month, (CAST( shptbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS Date)))
    --, shptbl.MODIFIEDDATETIME
ORDER BY
    MonthOrd
    --DATEPART(month, (CAST( shptbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS Date)))
