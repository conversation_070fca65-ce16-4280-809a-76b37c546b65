
-- Private query. Include the cluster number

SELECT 
	wll.CREATEDDATETIME 		AS ReleasedToWHDate
	, CL.CLUSTERID 							AS ClusterID
	, CT.CLUSTERPROFILEID 					AS ClusterProfile
	, CASE 
		WHEN WT.WORKSTATUS = 0 THEN 'Open' 
		ELSE 'In process' 
	  END 									AS WorkStatus
	, WT.CONTAINERID
	, WT.ORDERNUM
FROM WHSWORKCLUSTERLINE CL
INNER JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID AND CL.DATAAREAID = 'ha'
INNER JOIN WHSLOADLINE wll ON wll.SHIPMENTID = WT.SHIPMENTID AND wll.ORDERNUM = WT.ORDERNUM AND wll.LOADID = WT.LOADID -- 5/30/2022
INNER JOIN WHSWORKCLUSTERTABLE CT ON CT.CLUSTERID = CL.CLUSTERID AND CT.[PARTITION] = CL.[PARTITION] AND CT.DATAAREAID = 'ha'
WHERE	WT.WORKSTATUS		< 2	AND -- Open, In progress
		WT.WORKTRANSTYPE	= 2 AND -- Sales orders
		WT.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>CODE LIKE '4010%' AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' AND -- Excluding Wholesale
		WT.CREATEDDATETIME > ( GETDATE() - 20 ) -- Last 20 days
ORDER BY wll.CREATEDDATETIME, CL.CLUSTERID