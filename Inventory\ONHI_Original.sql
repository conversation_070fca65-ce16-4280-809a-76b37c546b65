-- picking location on hand quantity with current fixed location product variant for the location

-- Original query with a possible slight tweak

SELECT 
    WMSLOCATION.WMSLOCATIONID
    , WMSLOCATION.ZONEID
    , WHSZONE.HAAUTOUPDATEFIXEDLOCATION
    , OH.ITEMID as OH_ItemId
    , OH.INVENTSIZEID as OH_Size
    , OH.INVENTCOLORID as OH_Color
    , OH.PHYSICALINVENT
    , fixedloc.itemid as FL_Item
    , fixedloc.INVENTSIZEID as FL_Size
    , fixedloc.INVENTCOLORID as FL_Color
FROM WMSLOCATION 
LEFT OUTER JOIN
( -- This subquery pulls locations with inventory from Warehouse 4010( KY DC )
    SELECT        
        WMSLOCATION_1.WMSLOCATIONID
        , WMSLOCATION_1.ZONEID as Lo<PERSON>Zone
        , INVENTSUM.ITEMID
        , INVENTDIM.INVENTSIZEID
        ,INVENTDIM.INVENTCOLORID
        , INVENTSUM.PHYSICALINVENT
        , inventsum.RESERVPHYSICAL
    FROM INVENTSUM 
    INNER JOIN INVENTDIM ON INVENTSUM.INVENTDIMID = INVENTDIM.INVENTDIMID 
    INNER JOIN WMSLOCATION AS WMSLOCATION_1 ON WMSLOCATION_1.WMSLOCATIONID = INVENTDIM.WMSLOCATIONID
    WHERE 
        (INVENTSUM.PHYSICALINVENT > 0)  -- Only locations with inventory
        AND (INVENTDIM.INVENTLOCATIONID = '4010') 
        -- I added the following condition
        AND ( WMSLOCATION_1.LOCPROFILEID IN (  'W001', 'Picking', 'Picking A','Picking D', 'PalletPicking' ) ) -- Testing 10/23/2024
        --AND ( WMSLOCATION_1.LOCPROFILEID = 'Picking' or WMSLOCATION_1.LOCPROFILEID = 'Manual Move' or WMSLOCATION_1.LOCPROFILEID = 'Bulk' or WMSLOCATION_1.LOCPROFILEID = 'No LP Track' or  WMSLOCATION_1.LOCPROFILEID = 'W001' or 
        --WMSLOCATION_1.LOCPROFILEID = 'Picking A') 
        --AND (WMSLOCATION_1.LOCPROFILEID = '*')
) AS OH ON WMSLOCATION.WMSLOCATIONID = OH.WMSLOCATIONID
left outer join 
(
    select pvfl.WMSLOCATIONID, wmslocation.ZONEID, pvfl.ITEMID, inventdim.INVENTSIZEID, inventdim.INVENTCOLORID
    from WHSPRODUCTVARIANTFIXEDLOCATION as pvfl
    join inventdim on pvfl.PRODUCTVARIANTINVENTDIMID = inventdim.INVENTDIMID
    join WMSLOCATION on pvfl.WMSLOCATIONID = WMSLOCATION.WMSLOCATIONID
    where pvfl.INVENTLOCATIONID = '4010'    
) as fixedloc on wmslocation.WMSLOCATIONID = fixedloc.WMSLOCATIONID
left outer join 
    WHSZONE on wmslocation.ZONEID = whszone.ZONEID
WHERE (WMSLOCATION.INVENTLOCATIONID = '4010') AND (WMSLOCATION.LOCPROFILEID = 'Picking' or WMSLOCATION.LOCPROFILEID = 'Manual Move' or WMSLOCATION.LOCPROFILEID = 'Bulk' or WMSLOCATION.LOCPROFILEID = 'No LP Track' or  WMSLOCATION.LOCPROFILEID = 'W001' or 
        WMSLOCATION.LOCPROFILEID = 'Picking A')
ORDER BY WMSLOCATION.WMSLOCATIONID