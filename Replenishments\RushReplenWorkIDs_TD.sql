--<PERSON> work IDs v2 - <PERSON>s
SELECT wavetd.waveid, wavetd.<PERSON><PERSON><PERSON>MPLATENAME, wavetd.WAVESTATUS, link.DEMANDWORKID, link.DEMANDLINENUM, wtd.FRO<PERSON><PERSON>, wld.REP<PERSON>NDEMAND,
		wavetr.<PERSON>VE<PERSON>, wavetr.WAVESTATUS, wavetr.<PERSON><PERSON><PERSON><PERSON>LATENAME, link.R<PERSON>LENWORKID, link.<PERSON><PERSON><PERSON>NLINENUM, wtr.<PERSON><PERSON><PERSON><PERSON>, wtr.WOR<PERSON><PERSON>TU<PERSON>, wtr.LOCKEDUSER, wtr.WHSZONEID, wtr.WOR<PERSON>POOLID
FROM WHSREPLENWORKLINK link
     JOIN WHSWORKLINE wld ON wld.workid = link.DEMANDWORKID
                                    AND wld.LINENUM = link.DEMA<PERSON>LINENUM
                                    AND wld.DATAAREAID = link.DATAAREAID
                                    AND wld.PARTITION = link.PARTITION
     JOIN WHSWORKTABLE wtd ON wld.WORKID = wtd.WORKID
                             AND wld.DATAAREAID = wtd.<PERSON><PERSON><PERSON>REAID
                             AND wld.PARTITION = wtd.PARTITION
     JOIN WHSWAVETABLE wavetd ON wtd.WAVEID = wavetd.WAVEID
                                AND wtd.DATAAREAID = wavetd.DATAAREAID
                                AND wtd.PARTITION = wavetd.PARTITION
	 JOIN WHSWORKLINE wlr on wlr.workid = link.REPLENWORKID
                                    AND wlr.LINENUM = link.REPLENLINENUM
                                    AND wlr.DATAAREAID = link.DATAAREAID
                                    AND wlr.PARTITION = link.PARTITION
     JOIN WHSWORKTABLE wtr ON wlr.WORKID = wtr.WORKID
                             AND wlr.DATAAREAID = wtr.DATAAREAID
                             AND wlr.PARTITION = wtr.PARTITION
	 JOIN WHSWAVETABLE wavetr ON wtr.WAVEID = wavetr.WAVEID
                                AND wtr.DATAAREAID = wavetr.DATAAREAID
                                AND wtr.PARTITION = wavetr.PARTITION
WHERE wld.REPLENDEMAND = 1
      AND wld.WORKSTATUS = 0
	  AND wlr.WORKSTATUS < 4
	  and wavetd.wavetemplatename in ('Singles - Rush Order', 
                                             'Rush Orders - Multi',
									'DSL 2/3 DAYS',
									'GIFTS RUSH', 
									'CSL RUSH', 
									'CSL/MISC', 
									'DSL ONE DAY', 
									'SATURDAY DELIVERY',
									'OVERSIZE RUSH', 
									'Baby Registry Rush')



/*

SELECT TOP 20 *
FROM WHSREPLENWORKLINKREPLEN
WHERE REPLENWORKID IN( 'WK0028935631', 'WK0028935630'  )


SELECT TOP 20 *
FROM WHSREPLENWORKLINK
WHERE 
     --REPLENWORKID = 'WK0012244570'
     DEMANDWORKID = 'WK0028935717'

*/