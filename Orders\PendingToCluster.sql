
-- Author: <PERSON>

-- Pending to cluster( Not clustered )
-- Used in PBI

SELECT	CLI.WorkID, CLI.CreatedDate, CLI.WAVEID, CLI.Template, CLI.Frozen, CNT_Type
/*		COUNT( CLI.WORKID ) AS PendWkCount, -- Segregating Work's ready by work template
    SUM( CASE WHEN FROZEN = 0 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 1 ELSE 0 END 
              END  
            ) AS AB_Ready, 
        SUM( CASE WHEN FROZEN = 0 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 0 ELSE 1 END 
              END  
            ) AS Not_AB_Ready,     
		SUM( CASE WHEN FROZEN = 1 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 1 ELSE 0 END 
              END  
            ) AS AB_Blocked, 
        SUM( CASE WHEN FROZEN = 1 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 0 ELSE 1 END 
              END  
            ) AS Not_AB_Blocked
*/
FROM (    
SELECT 
CAST( 
  CASE  WHEN DATEPART( mm, wt.CreatedDateTime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wt.CreatedDateTime ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wt.CreatedDateTime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wt.CreatedDateTime ) -- No DST
        WHEN DATEPART( mm, wt.CreatedDateTime ) = 3 
        THEN CASE   WHEN DATEPART( dd, wt.CreatedDateTime ) < 8 OR DATEPART( dd, wt.CreatedDateTime ) > 14 THEN  DATEADD( hh, - 5, wt.CreatedDateTime ) -- No DST
                    WHEN DATEPART( dd, wt.CreatedDateTime ) - DATEPART( w, wt.CreatedDateTime ) + 1  >= 8 THEN  DATEADD(hh, - 4, wt.CreatedDateTime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wt.CreatedDateTime )
             END
        WHEN DATEPART( dd, wt.CreatedDateTime ) - DATEPART( w, wt.CreatedDateTime ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wt.CreatedDateTime )
        ELSE DATEADD( hh, - 4, wt.CreatedDateTime )
END   AS DATE )  AS CreatedDate,
WT.WAVEID, WVT.WAVETEMPLATENAME AS Template, 
WT.WORKID, WT.FROZEN,
CASE WHEN WT.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' ) THEN 'AB' ELSE 'Not_AB' END AS CNT_Type
FROM WHSWORKTABLE WT
LEFT JOIN WHSWORKCLUSTERLINE CL ON CL.WORKID = WT.WORKID
INNER JOIN WHSWAVETABLE WVT ON WVT.WAVEID = WT.WAVEID
WHERE WT.WORKSTATUS < 2																			AND -- Open, In progresss
    WT.WORKTRANSTYPE    = 2											AND -- Sales Order
    WT.WORKTEMPLATECODE NOT LIKE 'W%'								AND -- Excluding Wholesale
    WT.WORKTEMPLATECODE NOT IN ('4010 Wholesale', 'Saks' )       	AND -- More exclusions
    WT.CREATEDDATETIME > ( GETDATE() - 15 )                         AND -- Last two weeks
    CL.CLUSTERID IS NULL                                                -- Not clustered
GROUP BY WT.WAVEID, WT.WORKTEMPLATECODE, WVT.WAVETEMPLATENAME, WT.CREATEDDATETIME, WT.WORKID, FROZEN
) AS CLI
--GROUP BY WAVEID, TEMPLATE, CreatedDate, Frozen, CNT_Type


-
-- **** Not clustered ****
--Without units, way faster
SELECT	
	WT.WAVEID
	, WVT.WAVETEMPLATENAME AS Template
	, COUNT( WT.WORKID ) AS PendWkCount
	, SUM( CASE WHEN WT.FROZEN = 0 THEN 1 ELSE 0 END) AS Ready
	, SUM( CASE WHEN WT.FROZEN = 1 THEN 1 ELSE 0 END ) AS Blocked
	, CAST( DATEADD( hour, -4, WT.CREATEDDATETIME ) AS DATE ) AS CreatedDate
FROM 
	WHSWORKTABLE WT
LEFT JOIN 
	WHSWORKCLUSTERLINE CL ON CL.WORKID = WT.WORKID
INNER JOIN 
	WHSWAVETABLE WVT ON WVT.WAVEID = WT.WAVEID
WHERE	WT.WORKSTATUS		< 2									AND -- Open, In progresss
		WT.WORKTRANSTYPE	= 2									AND -- Sales Order
		WT.WORKTEMPLATECODE NOT LIKE 'W%'						AND -- Excluding Wholesale
		WT.WORKTEMPLATECODE NOT IN ('4010 Wholesale', 'Saks' )	AND -- More exclusions
		WT.CREATEDDATETIME > ( GETDATE() - 15 )					AND -- Last two weeks
		CL.CLUSTERID IS NULL										-- Not clustered
GROUP BY WT.WAVEID, WVT.WAVETEMPLATENAME, CAST( DATEADD( hour, -4, WT.CREATEDDATETIME ) AS DATE )
ORDER BY WT.WAVEID



--All clusters from selected waves

SELECT CT.CLUSTERID, WT.WAVEID, COUNT( CL.WORKID ) AS WkCount
	FROM WHSWORKCLUSTERTABLE CT
	INNER JOIN WHSWORKCLUSTERLINE CL ON CT.CLUSTERID = CL.CLUSTERID
	LEFT JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID
	INNER JOIN WHSWAVETABLE wvt ON wvt.WAVEID = WT.WAVEID
	WHERE WT.WORKTRANSTYPE = 2 AND 
	WT.WORKTEMPLATECODE LIKE '4010%' AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' AND WT.CREATEDDATETIME > ( GETDATE() - 20 )
	AND WT.WAVEID IN ( 'WV000161677', 'WV000161668','WV000161699' )
	--WT.WORKTEMPLATECODE NOT LIKE 'W%' AND WT.WORKTEMPLATECODE NOT IN ('4010 Wholesale', 'Saks' ) AND WT.CREATEDDATETIME > ( GETDATE() - 15 )
	GROUP BY WT.WAVEID, CT.CLUSTERID

--Pending work ids with cluster information
-- Modified 6/8/2023

SELECT 
	CT.CLUSTERID 			AS ClusterID
	, CT.CLUSTERPROFILEID
	, CT.CREATEDBY 			AS CreatedBy
	, CT.CREATEDDATETIME 	AS CreatedOn
	, CL.HACLUSTERPOSITION
	, CL.WORKID--, COUNT( CL.WORKID ) AS WkPending
FROM WHSWORKCLUSTERTABLE CT
INNER JOIN WHSWORKCLUSTERLINE CL ON CL.CLUSTERID = CT.CLUSTERID AND CT.DATAAREAID = CL.DATAAREAID AND CT.[PARTITION] = CL.[PARTITION]
INNER JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID  AND CL.DATAAREAID = WT.DATAAREAID AND CL.[PARTITION] = WT.[PARTITION]
INNER JOIN WHSCONTAINERTABLE CNTTbl ON CNTTbl.CONTAINERID = WT.CONTAINERID AND CNTTbl.DATAAREAID = WT.DATAAREAID AND CNTTbl.[PARTITION] = WT.[PARTITION]
WHERE	WT.WORKSTATUS		< 2	AND -- Open, In progress
		WT.WORKTRANSTYPE	= 2 AND -- Sales orders
		WT.WORKTEMPLATECODE LIKE '4010%' 
		AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' 
		AND	ISNULL( CNTTbl.SHIPCARRIERTRACKINGNUM, '' )  = ''  -- Avoiding code errors, around 11/15/2021
		--WT.WORKTEMPLATECODE IN ( '4010 ADirect', '4010 AutoBagger Mult', '4010 AutoBagger Sing', '4010 Bander', '4010 CartonShip Mult', '4010 CartonShip Sing', '4010 Direct', '4010 Gift Card Only' ) AND
		AND WT.CREATEDDATETIME > ( GETDATE() - 20 ) 
		--AND CT.CLUSTERID = 'CL001016199'
ORDER BY 
	CL.CLUSTERID, CL.HACLUSTERPOSITION

	--GROUP BY CT.CLUSTERID, CT.CLUSTERPROFILEID, CT.CREATEDBY, CT.CREATEDDATETIME
--Cluster Info
--Total


-- Modified 6/8/2023

SELECT
	PCLs.CLUSTERID
	, PCLs.WkPending
	, PCLs.WkCount
FROM
(
SELECT  
	CT.CLUSTERID
	, SUM( CASE WHEN WT.WORKSTATUS < 2 THEN 1 ELSE 0 END ) AS WkPending
	, COUNT( * ) AS WkCount
	FROM WHSWORKCLUSTERLINE CL
	JOIN WHSWORKCLUSTERTABLE CT ON CT.CLUSTERID = CL.CLUSTERID AND CT.DATAAREAID = CL.DATAAREAID AND CT.[PARTITION] = CL.[PARTITION]
	JOIN WHSWORKTABLE WT ON WT.WORKID = CL.WORKID AND CL.DATAAREAID = WT.DATAAREAID AND CL.[PARTITION] = WT.[PARTITION]
WHERE 
	WT.WORKTRANSTYPE = 2 AND 
	WT.WORKTEMPLATECODE LIKE '4010%' 
	AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' 
	AND WT.CREATEDDATETIME > ( GETDATE() - 20 ) 
	GROUP BY CT.CLUSTERID
) AS PCLs
WHERE	
	PCLs.WkPending > 0