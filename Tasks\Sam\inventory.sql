-- Inventory data for <PERSON>'s request

USE DAX_PROD

SELECT 
    CASE 
        WHEN isnull(idim.inventcolorid, '') = '' THEN isum.itemid
        WHEN isnull(idim.inventsizeid, '') = '' THEN isum.itemid + '-' + idim.inventcolorid
        ELSE isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid    
    END                                                                 AS [SKU]
    , CONVERT( DECIMAL( 20,0), isum.physicalinvent )                    AS [Qty]
    , idim.wmslocationid                                                AS [Location]
FROM inventsum isum
JOIN inventdim idim ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION] AND isum.physicalinvent > 0 
JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = idim.WMSLOCATIONID AND loc.DATAAREAID = idim.DATAAREAID AND idim.[PARTITION] = loc.[PARTITION]
WHERE 
    idim.inventlocationid = '4010'
    AND idim.INVENTSITEID = 'HA USA'
    AND loc.inventlocationid = '4010' 
    AND loc.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', /*'W001','Offsite',*/'Bulk') 
    AND loc.zoneid NOT IN ( 'Current Pick')
ORDER BY [SKU]
