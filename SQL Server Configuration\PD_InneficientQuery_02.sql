-- Inefficient query #2

(@P1 bigint,@P2 nvarchar(5),@P3 nvarchar(21),@P4 bigint,@P5 nvarchar(5))
SELECT 
    T1.ITEMID, T2.INVENTSIZEID, T2.INVENTCOLORID, T2.CONFIGID, T2.INVENTSTYLEID
FROM 
    WHSLOADLINE T1 CROSS JOIN INVENTDIM T2
WHERE 
    (((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (T1.SHIPMENTID=@P3)) AND (((T2.PARTITION=@P4) AND (T2.DATAAREAID=@P5)) AND (T2.INVENTDIMID=T1.INVENTDIMID))
GROUP BY 
    T1.ITEMID,T2.INVENTSIZEID,T2.INVENTCOLORID,T2.CONFIGID,T2.INVENTSTYLEID
ORDER BY 
    T1.ITEMID,T2.INVENTSIZEID,T2.INVENTCOLORID,T2.CONFIGID,T2.INVENTSTYLEID


-- Query rewritten

SELECT T1.ITEMID, T2.INVENTSIZEID, T2.INVENTCOLORID, T2.CONFIGID, T2.INVENTSTYLEID
FROM WHSLOADLINE T1
INNER JOIN INVENTDIM T2
    ON T2.INVENTDIMID = T1.INVENTDIMID
    AND T1.PARTITION = T2.PARTITION
    AND T1.DATAAREAID = T2.DATAAREAID
WHERE
    T1.PARTITION = @P1 AND
    T1.DATAAREAID = @P2 AND
    T1.SHIPMENTID = @P3
GROUP BY T1.ITEMID, T2.INVENTSIZEID, T2.INVENTCOLORID, T2.CONFIGID, T2.INVENTSTYLEID
ORDER BY T1.ITEMID, T2.INVENTSIZEID, T2.INVENTCOLORID, T2.CONFIGID, T2.INVENTSTYLEID
