
-- Process being blocked longer


WITH Blockers AS (
    SELECT 
        blocking_session_id AS LeadBlockerSessionID,
        session_id AS BlockedSessionID
    FROM sys.dm_exec_requests
    WHERE blocking_session_id <> 0
),
RecursiveBlockers AS (
    SELECT 
        LeadBlockerSessionID,
        BlockedSessionID
    FROM Blockers
    UNION ALL
    SELECT 
        r.blocking_session_id,
        r.session_id
    FROM sys.dm_exec_requests r
    INNER JOIN RecursiveBlockers rb ON r.blocking_session_id = rb.BlockedSessionID
    WHERE r.blocking_session_id <> 0
)
SELECT TOP 1
    -- Blocked process information
    COUNT(*) OVER () AS BlkLines,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1))
    END AS Blocked_AXSessionID,
    r.session_id AS Blocked_SQLSessionID,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) - 1)
    END AS Blocked_AXUserName,
    s.host_name AS Blocked_HostName,
    r.wait_time / 1000 AS Max_WaitTimeInSec,
    r.wait_type AS Blocked_WaitType,
    DB_NAME(r.database_id) AS Blocked_DB_Name,
    t.text AS Blocked_SQL_Text,
    s.status AS Blocked_SQL_Status,
    r.command AS Blocked_SQL_Command,
    -- Blocking process information
    CASE 
        WHEN LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1))
    END AS Blocking_AXSessionID,
    r2.session_id AS Blocking_SQLSessionID,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) - 1)
    END AS Blocking_AXUserName,
    s2.host_name AS Blocking_HostName,
    r2.wait_time / 1000 AS Blocking_Max_WaitTimeInSec,
    r2.wait_type AS Blocking_WaitType,
    DB_NAME(r2.database_id) AS Blocking_DB_Name,
    t2.text AS Blocking_SQL_Text,
    s2.status AS Blocking_SQL_Status,
    r2.command AS Blocking_SQL_Command
    --, SYSDATETIMEOFFSET() AT TIME ZONE 'Eastern Standard Time' AS KYDateTime
FROM RecursiveBlockers rb
INNER JOIN sys.dm_exec_requests r ON rb.BlockedSessionID = r.session_id
INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
INNER JOIN sys.dm_exec_requests r2 ON rb.LeadBlockerSessionID = r2.session_id
INNER JOIN sys.dm_exec_sessions s2 ON r2.session_id = s2.session_id
CROSS APPLY sys.dm_exec_sql_text(r2.sql_handle) t2
ORDER BY Max_WaitTimeInSec DESC;

SELECT TOP 1
    -- Blocked process information
    (SELECT COUNT(*)
     FROM sys.dm_exec_requests r_count
     WHERE r_count.blocking_session_id <> 0) AS BlkLines,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1))
    END AS Blocked_AXSessionID,
    r.session_id AS Blocked_SQLSessionID,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) - 1)
    END AS Blocked_AXUserName,
    s.host_name AS Blocked_HostName,
    r.wait_time / 1000 AS Max_WaitTimeInSec,
    r.wait_type AS Blocked_WaitType,
    DB_NAME(r.database_id) AS Blocked_DB_Name,
    t.text AS Blocked_SQL_Text,
    s.status AS Blocked_SQL_Status,
    r.command AS Blocked_SQL_Command,
    -- Blocking process information
    CASE 
        WHEN LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1))
    END AS Blocking_AXSessionID,
    r2.session_id AS Blocking_SQLSessionID,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) - 1)
    END AS Blocking_AXUserName,
    s2.host_name AS Blocking_HostName,
    r2.wait_time / 1000 AS Blocking_Max_WaitTimeInSec,
    r2.wait_type AS Blocking_WaitType,
    DB_NAME(r2.database_id) AS Blocking_DB_Name,
    t2.text AS Blocking_SQL_Text,
    s2.status AS Blocking_SQL_Status,
    r2.command AS Blocking_SQL_Command
    --, SYSDATETIMEOFFSET() AT TIME ZONE 'Eastern Standard Time' AS KYDateTime
FROM (
    SELECT TOP 1
        r1.blocking_session_id AS LeadBlockerSessionID,
        r1.session_id AS BlockedSessionID
    FROM sys.dm_exec_requests r1
    WHERE r1.blocking_session_id <> 0
    ORDER BY r1.wait_time DESC
) rb
INNER JOIN sys.dm_exec_requests r ON rb.BlockedSessionID = r.session_id
INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
INNER JOIN sys.dm_exec_requests r2 ON rb.LeadBlockerSessionID = r2.session_id
INNER JOIN sys.dm_exec_sessions s2 ON r2.session_id = s2.session_id
CROSS APPLY sys.dm_exec_sql_text(r2.sql_handle) t2
ORDER BY Max_WaitTimeInSec DESC;

-- NO CTEs
-- Using this one in Python

SELECT TOP 1
    -- Blocked process information
     (SELECT COUNT(*)
     FROM sys.dm_exec_requests r_count
     WHERE r_count.blocking_session_id <> 0) AS BlkLines,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1))
    END AS Blocked_AXSessionID,
    r.session_id AS Blocked_SQLSessionID,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) - 1)
    END AS Blocked_AXUserName,
    s.host_name AS Blocked_HostName,
    r.wait_time / 1000 AS Max_WaitTimeInSec,
    r.wait_type AS Blocked_WaitType,
    DB_NAME(r.database_id) AS Blocked_DB_Name,
    t.text AS Blocked_SQL_Text,
    s.status AS Blocked_SQL_Status,
    r.command AS Blocked_SQL_Command,
    -- Blocking process information
    CASE 
        WHEN LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1))
    END AS Blocking_AXSessionID,
    r2.session_id AS Blocking_SQLSessionID,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s2.context_info AS VARCHAR(128)))), 1) - 1)
    END AS Blocking_AXUserName,
    s2.host_name AS Blocking_HostName,
    r2.wait_time / 1000 AS Blocking_Max_WaitTimeInSec,
    r2.wait_type AS Blocking_WaitType,
    DB_NAME(r2.database_id) AS Blocking_DB_Name,
    t2.text AS Blocking_SQL_Text,
    s2.status AS Blocking_SQL_Status,
    r2.command AS Blocking_SQL_Command
    --, SYSDATETIMEOFFSET() AT TIME ZONE 'Eastern Standard Time' AS KYDateTime
FROM (
    SELECT TOP 1
        r1.blocking_session_id AS LeadBlockerSessionID,
        r1.session_id AS BlockedSessionID
    FROM sys.dm_exec_requests r1
    WHERE r1.blocking_session_id <> 0
    ORDER BY r1.wait_time DESC
) rb
INNER JOIN sys.dm_exec_requests r ON rb.BlockedSessionID = r.session_id
INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
INNER JOIN sys.dm_exec_requests r2 ON rb.LeadBlockerSessionID = r2.session_id
INNER JOIN sys.dm_exec_sessions s2 ON r2.session_id = s2.session_id
CROSS APPLY sys.dm_exec_sql_text(r2.sql_handle) t2
ORDER BY Max_WaitTimeInSec DESC;

