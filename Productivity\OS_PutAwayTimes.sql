
DECLARE @ReportDays INT = 7;

DECLARE @TimeThreshold INT = 300; -- If there is more than 5 minutes difference between scans, the operator should be doing something else: discard


WITH rpa (WorkID, userid, Location, CreatedDT, WorkTemplate) AS
(
SELECT
    wkln.WORKID
    , wkln.USERID
    , wkln.WMSLOCATIONID    AS 'Location'
    , wkln.<PERSON><PERSON><PERSON>IE<PERSON><PERSON><PERSON><PERSON>ME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS CreatedDT
    , wktbl.WOR<PERSON><PERSON><PERSON>LATECODE AS WorkTemplate
FROM
    WHSWORKLINE wkln
    JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wkln.DATAAREAID = wktbl.DATAAREAID AND wkln.[PARTITION] = wktbl.[PARTITION] 
WHERE
    CAST(DATEADD(hh, -4, wkln.MODIFIEDDATETIME) AS DATE) > GETUTCDATE() - @ReportDays
    AND wkln.WORKCLASSID = 'BulkPut' 
    AND wkln.WORKTYPE = 2 -- Put
    AND wktbl.INVENTLOCATIONID = '4010'
    AND wkln.WORKSTATUS = 4 -- Closed
),
rpac AS
(
SELECT
    CreatedDT
    , LAG(CreatedDT) OVER (PARTITION BY userid ORDER BY CreatedDT) AS previous_timestamp
    , userid
    , [Location]
    , CASE
        WHEN LAG(Location) OVER (PARTITION BY userid ORDER BY CreatedDT) IS NOT NULL
        THEN 
            CASE WHEN LEFT( LAG(Location) OVER (PARTITION BY userid ORDER BY CreatedDT), 3) = LEFT([Location],3) THEN 'No' ELSE 'Yes' END 
        ELSE NULL
    END AS AisleChanged
    , CASE
        WHEN LAG(WorkTemplate) OVER (PARTITION BY userid ORDER BY CreatedDT) IS NOT NULL
        THEN 
            CASE WHEN LAG(WorkTemplate) OVER (PARTITION BY userid ORDER BY CreatedDT) = [WorkTemplate] THEN 'No' ELSE 'Yes' END 
        ELSE NULL
    END AS ZoneChanged
    --, DATEDIFF(SECOND, LAG(CreatedDT) OVER (PARTITION BY userid ORDER BY CreatedDT), CreatedDT) AS tds
    , CASE
        WHEN LAG(CreatedDT) OVER (PARTITION BY userid ORDER BY CreatedDT) IS NOT NULL
        THEN DATEDIFF(SECOND, LAG(CreatedDT) OVER (PARTITION BY userid ORDER BY CreatedDT), CreatedDT)
        ELSE NULL
    END AS time_difference_seconds
    , WorkTemplate
    , CASE WHEN DATEPART( hh, CreatedDT ) IN ( 00, 01, 02, 03, 16, 17, 18, 19, 20, 21, 22, 23 ) THEN '2nd' ELSE '1st' END AS ThrownBy
FROM
    rpa
)
SELECT
    *
FROM
    rpac
WHERE
    time_difference_seconds < @TimeThreshold
ORDER BY
    CreatedDT