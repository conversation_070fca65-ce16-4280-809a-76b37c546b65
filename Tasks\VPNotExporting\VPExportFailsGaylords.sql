
-- Finding how many open lines from gaylords, assuming that no product was shorted

SELECT
    Loc
	, <PERSON><PERSON>t<PERSON>ick.AISLEID
    , CAST( <PERSON><PERSON>t<PERSON>ick.PickedTime AS DATE ) PickDate
	, PickType
    , CASE WHEN PalletPick.ModifiedBy IN( 'svc-vite', 'wfexc' ) THEN 0 ELSE 1 END AS ExportFailed
FROM
(
SELECT
	wkln.WMSLOCATIONID AS Loc
	, loc.AISLEID
    , wkln.WORKID
    , wkln.MODIFIEDBY
    , CASE WHEN wktbl.WORKTEMPLATECODE = '4010 Direct' THEN 'Manual' ELSE 'AB' END AS PickType
	, CASE WHEN DATEPART( mm, wkln.WORKCLOSEDUTCDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wkln.WORKCLOSEDUTCDATETIME ) -- Daylight Savings Months 
       		WHEN DATEPART( mm, wkln.WORKCLOSEDUTCDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME ) -- No DST
       		WHEN DATEPART( mm, wkln.WOR<PERSON>CLOSEDUTCDATETIME ) = 3 
       		THEN CASE   
					WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) < 8 OR DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) > 14 THEN  DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME ) -- No DST
                   	WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wkln.WORKCLOSEDUTCDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wkln.WORKCLOSEDUTCDATETIME ) -- Last Sunday after March 8
                   	ELSE DATEADD(hh, - 5, wkln.WORKCLOSEDUTCDATETIME )
           			END
       		WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wkln.WORKCLOSEDUTCDATETIME ) + 1  >= 1 --  After first Sunday, November
       		THEN DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME )
       		ELSE DATEADD( hh, - 4, wkln.WORKCLOSEDUTCDATETIME )
	END	 AS PickedTime
    , CONVERT( DECIMAL( 10, 0 ), wkln.QTYWORK ) AS Qty
FROM
	WHSWORKLINE wkln 	
    LEFT JOIN WHSWORKTABLE wktbl    ON wktbl.WORKID = wkln.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION]
	LEFT JOIN WMSLOCATION loc	    ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.[PARTITION] = wkln.[PARTITION] AND loc.DATAAREAID = wkln.DATAAREAID
WHERE
	wktbl.CREATEDDATETIME > GETUTCDATE() - 350
	AND wkln.WORKTYPE = 1  -- Pick
	AND wkln.WORKSTATUS = 4  -- Closed
	AND wktbl.WORKTRANSTYPE = 2 -- Sales Orders
	AND loc.LOCPROFILEID = 'PalletPicking' -- Gaylords
	--AND wkln.WORKCLASSID NOT LIKE  'Fwd%'
)  AS PalletPick