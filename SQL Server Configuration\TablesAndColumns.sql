

-- All the tables, not views
/*
SELECT
  *
FROM
  DAX_PROD.INFORMATION_SCHEMA.TABLES
WHERE
  TABLE_TYPE = 'BASE TABLE';
GO
*/

-- All tables and columns
-- Find colums seasoncode, Division, Department, Offer

SELECT schema_name(tab.schema_id) as schema_name,
    tab.name as table_name, 
    col.column_id,
    col.name as column_name, 
    t.name as data_type,    
    col.max_length,
    col.precision
FROM sys.tables as tab
    INNER JOIN sys.columns as col
        on tab.object_id = col.object_id
    LEFT JOIN sys.types as t
    on col.user_type_id = t.user_type_id
WHERE
    COL.NAME LIKE 'PackageId%'
ORDER BY schema_name,
    table_name, 
    column_id;





