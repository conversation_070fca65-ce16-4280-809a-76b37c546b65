
-- Bing AI

SELECT 
    DB_NAME(mid.database_id) AS DatabaseName,
    OBJECT_NAME(mid.[object_id]) AS TableName,
    mid.equality_columns,
    mid.inequality_columns,
    mid.included_columns,
    migs.user_seeks + migs.user_scans AS TotalReads,
    'CREATE INDEX IX_' + OBJECT_NAME(mid.[object_id]) + '_' + REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns,'') + ISNULL(mid.inequality_columns,''), '[', ''), ']', ''), ',', '_') 
    + ' ON ' + mid.statement 
    + ' (' + ISNULL(mid.equality_columns,'') 
    + CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ',' ELSE '' END 
    + ISNULL(mid.inequality_columns, '') 
    + ')' 
    + ISNULL(' INCLUDE (' + mid.included_columns + ');', '') AS IndexCreationScript,
    migs.avg_total_user_cost * migs.avg_user_impact * (migs.user_seeks + migs.user_scans) AS IndexAdvantage
FROM sys.dm_db_missing_index_details AS mid
INNER JOIN sys.dm_db_missing_index_groups AS mig ON mid.index_handle = mig.index_handle
INNER JOIN sys.dm_db_missing_index_group_stats AS migs ON mig.index_group_handle = migs.group_handle
WHERE mid.database_id = DB_ID()
ORDER BY IndexAdvantage DESC;


-- Top 20
/*
This query will provide you with a list of the top 20 missing indexes that, if created, could potentially offer the most significant performance improvements. 
The EstimatedImprovement column is a calculated value that represents the potential impact of each missing index. The query also generates a script to create each
recommended index1.

Please review these recommendations carefully, as they are based on statistical analysis and may not always be perfect. It’s important to test any new indexes in a
development or staging environment before applying them to your production database.
*/
SELECT TOP 20 
    DB_NAME(mid.database_id) AS DatabaseName,
    OBJECT_NAME(mid.[object_id]) AS TableName,
    mid.equality_columns,
    mid.inequality_columns,
    mid.included_columns,
    migs.user_seeks + migs.user_scans AS TotalReads,
    'CREATE INDEX IX_' + OBJECT_NAME(mid.[object_id]) + '_' + REPLACE(REPLACE(REPLACE(ISNULL(mid.equality_columns,'') + ISNULL(mid.inequality_columns,''), '[', ''), ']', ''), ',', '_') 
    + ' ON ' + mid.statement 
    + ' (' + ISNULL(mid.equality_columns,'') 
    + CASE WHEN mid.equality_columns IS NOT NULL AND mid.inequality_columns IS NOT NULL THEN ',' ELSE '' END 
    + ISNULL(mid.inequality_columns, '') 
    + ')' 
    + ISNULL(' INCLUDE (' + mid.included_columns + ');', '') AS IndexCreationScript,
    CONVERT(decimal(28, 1), migs.avg_total_user_cost * migs.avg_user_impact * (migs.user_seeks + migs.user_scans)) AS EstimatedImprovement
FROM sys.dm_db_missing_index_groups mig
INNER JOIN sys.dm_db_missing_index_group_stats migs ON mig.index_group_handle = migs.group_handle
INNER JOIN sys.dm_db_missing_index_details mid ON mig.index_handle = mid.index_handle
ORDER BY EstimatedImprovement DESC;
