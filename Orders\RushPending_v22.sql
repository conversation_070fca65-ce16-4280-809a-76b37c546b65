
-- Variation of v2, created with <PERSON>(3/4/2025)

WITH rush AS (
    SELECT
        wst.ORDERNUM,
        wst.SHIPMENTID,
        IIF(wst.HALOCDIRECTFAIL = 1, 'Yes', 'No') AS [LDF],
        COALESCE(cnttbl.CONTAINERID, 'N/A') AS [ContainerId],
        COALESCE(wktbl.WORKID, 'N/A') AS [WORKID],
        IIF(wktbl.FROZEN = 1, 'Yes', 'No') AS [WkBlocked],
        CASE wktbl.WORKSTATUS
            WHEN 0 THEN 'Open'
            WHEN 1 THEN 'In process'
            WHEN 4 THEN 'Closed'
            ELSE 'N/A'
        END AS WkStatus,
        wkusr.USERNAME AS [LockedUser],
        IIF(wkcl.CLUSTERID IS NULL, 'N/A', wkcl.CLUSTERID) AS ClusterId,
        IIF(wkct.CLUSTERID IS NULL, 'N/A', wkct.CLUSTERPROFILEID) AS ClusterProfile,
        COALESCE(SUM(cntln.QTY), 0) AS CntUnits,
        wst.MODECODE,
        wst.WAVEID,
        wvt.WAVETEMPLATENAME,
        ll.TotalUnits AS Units,
        ll.MinCreatedDateTime AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS [ReleasedToWH],
        IIF(wktbl.WORKID IS NULL, 'N/A', IIF(COUNT(rw.DEMANDWORKID) > 0, 'Yes', 'No')) AS [NeedsReplen]
    FROM SALESTABLE st
    JOIN WHSSHIPMENTTABLE wst WITH (NOLOCK) ON st.SALESID = wst.ORDERNUM AND st.[PARTITION] = wst.[PARTITION] AND wst.DATAAREAID = 'ha'
    JOIN WHSWAVETABLE wvt WITH (NOLOCK) ON wvt.WAVEID = wst.WAVEID AND wvt.[PARTITION] = wst.[PARTITION] AND wvt.DATAAREAID = 'ha'
    LEFT JOIN WHSCONTAINERTABLE cnttbl WITH (NOLOCK) ON wst.SHIPMENTID = cnttbl.SHIPMENTID AND cnttbl.[PARTITION] = wst.[PARTITION] AND cnttbl.DATAAREAID = 'ha'
    LEFT JOIN WHSCONTAINERLINE cntln WITH (NOLOCK) ON cntln.CONTAINERID = cnttbl.CONTAINERID AND cnttbl.[PARTITION] = cntln.[PARTITION] AND cnttbl.DATAAREAID = 'ha'
    LEFT JOIN WHSWORKTABLE wktbl WITH (NOLOCK) ON wktbl.CONTAINERID = cnttbl.CONTAINERID AND wktbl.[PARTITION] = cnttbl.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN WHSWORKCLUSTERLINE wkcl WITH (NOLOCK) ON wkcl.WORKID = wktbl.WORKID AND wkcl.[PARTITION] = wktbl.[PARTITION] AND wkcl.DATAAREAID = 'ha'
    LEFT JOIN WHSWORKCLUSTERTABLE wkct WITH (NOLOCK) ON wkcl.CLUSTERID = wkct.CLUSTERID AND wkcl.[PARTITION] = wkct.[PARTITION] AND wkct.DATAAREAID = 'ha'
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs WITH (NOLOCK) ON hacs.CARTONID = cnttbl.CONTAINERID AND hacs.[PARTITION] = cnttbl.[PARTITION] AND cnttbl.DATAAREAID = 'ha'
    LEFT JOIN WHSWORKUSER wkusr WITH (NOLOCK) ON wkusr.USERID = wktbl.LOCKEDUSER AND wkusr.[PARTITION] = wktbl.[PARTITION] AND wkusr.DATAAREAID = 'ha'
    LEFT JOIN (
        SELECT SHIPMENTID, ORDERNUM, LOADID, SUM(QTY) AS TotalUnits, MIN(CREATEDDATETIME) as MinCreatedDateTime
        FROM WHSLOADLINE WITH (NOLOCK)
        WHERE DATAAREAID = 'ha'
        GROUP BY SHIPMENTID, ORDERNUM, LOADID
    ) ll ON wst.SHIPMENTID = ll.SHIPMENTID AND st.SALESID = ll.ORDERNUM AND wst.LOADID = ll.LOADID
    LEFT JOIN WHSREPLENWORKLINK rw WITH (NOLOCK) ON wktbl.WORKID = rw.DEMANDWORKID AND rw.DATAAREAID = 'ha'
    WHERE
        wst.SHIPMENTSTATUS < 5
        AND st.SALESSTATUS = 1
        AND (cnttbl.SHIPCARRIERTRACKINGNUM = '' OR cnttbl.SHIPCARRIERTRACKINGNUM IS NULL) -- Not shipped
        AND hacs.TRACKINGNUMBER IS NULL -- Not shipped yet
        AND wst.MODECODE IN ('1D', '2D', '3D', 'SA', 'IE')
    GROUP BY
        cnttbl.CONTAINERID, wst.SHIPMENTID, wst.LOADID, wst.ORDERNUM, wst.MODECODE, wst.HALOCDIRECTFAIL,
        hacs.CREATEDDATETIME, wst.WAVEID, wktbl.FROZEN, wkcl.CLUSTERID, wktbl.WORKSTATUS, wkusr.USERNAME, wktbl.WORKID,
        wkct.CLUSTERID, wkct.CLUSTERPROFILEID, wvt.WAVETEMPLATENAME, st.SALESID, hacs.TRACKINGNUMBER, ll.TotalUnits, ll.MinCreatedDateTime
)
SELECT
    ORDERNUM AS [OrderNum],
    MODECODE AS [ShipMethod],
    FORMAT(ReleasedToWH, 'MMM dd yyyy h:mmtt', 'en-US') AS ReleasedToWH,
    CAST(CASE WHEN CntUnits > 0 THEN CntUnits ELSE Units END AS INT) AS Units,
    SHIPMENTID AS [ShipmentId],
    LDF AS [LDF],
    CONTAINERID AS [ContainerId],
    WORKID AS [WorkId],
    NeedsReplen AS [NeedsReplen],
    WkBlocked AS [WorkBlocked],
    WkStatus AS [WorkStatus],
    COALESCE(LockedUser, 'N/A') AS [LockedBy],
    CLUSTERID AS [ClusterId],
    ClusterProfile AS [ClusterProfile],
    WAVEID AS [WaveId],
    [WAVETEMPLATENAME] AS [WaveTemplate]
FROM
    rush
ORDER BY
    ReleasedToWH, OrderNum;