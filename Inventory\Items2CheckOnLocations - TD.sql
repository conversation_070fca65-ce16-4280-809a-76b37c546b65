
/*
<PERSON>s
Here is the query that will identify the items that have transactions that are preventing updates to the Location Profile ID:
*/
 

SELECT
    isum.itemid
    , loc.WMSLOCATIONID
    , loc.LOCPROFILEID
    , id.*
    , isum.*
FROM
    inventsum isum
       join inventdim id on isum.INVENTDIMID = id.INVENTDIMID and isum.DATAAREAID = id.DATAAREAID and isum.PARTITION = id.PARTITION
       join wmslocation loc on id.WMSLOCATIONID = loc.WMSLOCATIONID and isum.DATAAREAID = loc.DATAAREAID and isum.PARTITION = loc.PARTITION
WHERE
    ( ( isum.CLOSED = 0 or isum.CLOSEDQTY = 0 ) ) 
    AND ( id.WMSLOCATIONID = '31-094D' and id.INVENTLOCATIONID = '4010' )
    AND isum.ITEMID + '-' + id.INVENTCOLORID + '-' + id.INVENTSIZEID = '30070-S74-XL'

/*
SELECT 
    *
FROM 
    inventsum isum
WHERE
    isum.itemid = '60599'
    AND ( isum.CLOSED = 0 or isum.CLOSEDQTY = 0 )

SELECT
    isum.ItemId
    , SUM( isum.PostedValue ) AS PostedValues
FROM
    inventsum isum
WHERE
    isum.itemid = '60599'
GROUP BY
    isum.Itemid
*/