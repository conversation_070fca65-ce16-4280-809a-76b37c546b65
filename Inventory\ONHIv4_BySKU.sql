
-- You.com, GPT-4o
SELECT  
    wmslocation.zoneid AS [ZoneId],
    wmslocation.locprofileid AS [LocProfile],
    OH.itemid AS [Item],
    OH.inventcolorid AS [Color],
    OH.inventsizeid AS [Size],
    wmslocation.wmslocationid AS [Location],
    CONVERT(DECIMAL(20,0), OH.physicalinvent) AS [Qty],
    CONVERT(DECIMAL(20,0), OH.reservphysical) AS [Reserved],
    OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid AS [SKU],
    CASE WHEN (hagpft.AVAILABILITY = 'in stock' OR OH.RESERVPHYSICAL > 0) THEN 'Yes' ELSE 'No' END AS [Online],
    CAST(OH.LastPickWC AS DATE) AS [LastMovement],
    DATEDIFF(DD, OH.LastPickWC, GETDATE()) AS [SittingDays]
FROM wmslocation
LEFT OUTER JOIN 
    ( 
        SELECT 
            isum.itemid,
            idim.inventcolorid,
            idim.inventsizeid,
            isum.physicalinvent,
            isum.reservphysical,
            idim.wmslocationid,
            MAX(isum.MODIFIEDDATETIME) AS LastPickWC
        FROM inventdim idim
        JOIN inventsum isum WITH (NOLOCK) ON isum.inventdimid = idim.inventdimid 
            AND isum.DATAAREAID = idim.DATAAREAID 
            AND isum.[PARTITION] = idim.[PARTITION]
        JOIN WMSLOCATION loc WITH (NOLOCK) ON loc.WMSLOCATIONID = idim.WMSLOCATIONID 
            AND loc.DATAAREAID = 'ha' 
            AND loc.[PARTITION] = isum.[PARTITION]
        WHERE 
            idim.inventlocationid = '4010'
            AND isum.physicalinvent <> 0 
            AND idim.INVENTSITEID = 'HA USA' 
            AND loc.LOCPROFILEID IN ('W001', 'Picking', 'Picking A', 'Picking D', 'PalletPicking') 
            AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997') 
        GROUP BY
            idim.WMSLOCATIONID, isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, isum.PHYSICALINVENT, isum.RESERVPHYSICAL
    ) AS OH
ON wmslocation.wmslocationID = OH.wmslocationid
LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid
WHERE 
    wmslocation.inventlocationid = '4010' 
    AND wmslocation.locprofileid LIKE '%Picking%'
    AND wmslocation.zoneid NOT IN ('Current Pick')