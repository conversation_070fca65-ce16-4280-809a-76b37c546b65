
-- Finding who picked the cluster

SELECT 
    PickCLData.CLUSTERID
    , PickCLData.DatePicked
    , PickCLData.PickedBy
    , PickCLData.ClusterType
    , SUM( PickCLData.Qty ) AS QtyPicked
FROM
(
SELECT 
	CLPD.CLUSTERID
    , CAST( CASE WHEN DATEPART( hh, CLPD.PickedTime ) IN ( 00, 01, 02, 03 ) THEN CLPD.PickedTime - 1  ELSE CLPD.PickedTime END AS DATE ) AS DatePicked
	, CASE WHEN DATEPART( hh, CLPD.PickedTime ) IN ( 00, 01, 02, 03, 16, 17, 18, 19, 20, 21, 22, 23 ) THEN '2nd' ELSE '1st' END AS PickedBy
    , CLPD.ClusterType
	, CLPD.Qty 
FROM
(
SELECT
	cltbl.CLUSTERID
	, CASE WHEN DATEPART( mm, wkln.WORKCLOSEDUTCDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wkln.WORKCLOSEDUTCDATETIME ) -- Daylight Savings Months 
       		WHEN DATEPART( mm, wkln.WORKCLOSEDUTCDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME ) -- No DST
       		WHEN DATEPART( mm, wkln.WORKCLOSEDUTCDATETIME ) = 3 
       		THEN CASE   
					WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) < 8 OR DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) > 14 THEN  DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME ) -- No DST
                   	WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wkln.WORKCLOSEDUTCDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wkln.WORKCLOSEDUTCDATETIME ) -- Last Sunday after March 8
                   	ELSE DATEADD(hh, - 5, wkln.WORKCLOSEDUTCDATETIME )
           			END
       		WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wkln.WORKCLOSEDUTCDATETIME ) + 1  >= 1 --  After first Sunday, November
       		THEN DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME )
       		ELSE DATEADD( hh, - 4, wkln.WORKCLOSEDUTCDATETIME )
	END	 AS PickedTime
    , CASE WHEN cltbl.CLUSTERPROFILEID LIKE 'AutoBagger%' THEN 'AB' ELSE 'Manual' END AS ClusterType
    , CONVERT( DECIMAL( 10,  0), wkln.QTYWORK ) AS Qty
	--, wkln.WORKID, wkln.USERID, wkln.MODIFIEDBY, wkln.WMSLOCATIONID
FROM
	WHSWORKCLUSTERTABLE cltbl
	LEFT JOIN WHSWORKCLUSTERLINE clln 	ON clln.CLUSTERID = cltbl.CLUSTERID AND clln.[PARTITION] = cltbl.[PARTITION] 	AND clln.DATAAREAID = cltbl.DATAAREAID
	LEFT JOIN WHSWORKLINE wkln 			ON wkln.WORKID = clln.WORKID 		AND wkln.[PARTITION] = clln.[PARTITION] 	AND wkln.[DATAAREAID] = clln.DATAAREAID
	LEFT JOIN WMSLOCATION loc			ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.[PARTITION] = wkln.[PARTITION] AND loc.DATAAREAID = wkln.DATAAREAID
WHERE
	cltbl.CREATEDDATETIME > GETUTCDATE() - 7
	AND wkln.WORKTYPE = 1  -- Pick
	AND wkln.WORKSTATUS = 4  -- Closed
	AND loc.LOCPROFILEID LIKE '%Picking%' -- Picking, Picking A, Pallet Picking
	AND wkln.MODIFIEDBY = 'axbatch'
	--AND cltbl.CLUSTERID IN ( 'CL001168929')
) AS CLPD
) AS PickCLData
GROUP BY
	CLUSTERID, DatePicked, PickedBy, ClusterType
ORDER BY 
	DatePicked, PickedBy, CLUSTERID, ClusterType

