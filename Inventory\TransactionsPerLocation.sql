

-- Transactions by locations

-- Inventory adjustments per day
SELECT 
     -- *,
     
     idim.WMSLOCATIONID       AS [Location]
     --, idim.RECID             AS [IdimRecId]
    -- , itrans.RECID           AS [ItransRecId]
     --, itorigin.RECID         AS [ItOriginRecId]
     , itrans.ITEMID          As [Item]
     , idim.INVENTCOLORID     AS 'Color'
     , idim.INVENTSIZEID      AS 'Size'
     , idim.INVENTSTATUSID    AS 'Status'
     , CONVERT( varchar, CAST(itrans.MODIFIEDDATETIME AT TIME Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME), 100) AS trans_MofifDT
     , CASE WHEN itrans.STATUSISSUE = 0 THEN 'Purchased'
          WHEN itrans.STATUSISSUE = 1 THEN 'Sold'
          WHEN itrans.STATUSISSUE = 2 THEN 'Deducted'
          WHEN itrans.STATUSISSUE = 3 THEN 'Picked'
          WHEN itrans.STATUSISSUE = 4 THEN 'Reserved Physical'
          WHEN itrans.STATUSISSUE = 6 THEN 'On Order'
          ELSE 'Unknown'
          END AS [StatusIssue]
     , CASE 
          WHEN itorigin.REFERENCECATEGORY = 0 THEN 'Sales Order'
          WHEN itorigin.REFERENCECATEGORY = 2 THEN 'Production Order'
          WHEN itorigin.REFERENCECATEGORY = 3 THEN 'Purchase Order'
          WHEN itorigin.REFERENCECATEGORY = 4 THEN 'Inventory Transaction'
          WHEN itorigin.REFERENCECATEGORY = 5 THEN 'Inventory Adjustment'
          WHEN itorigin.REFERENCECATEGORY = 6 THEN 'Transfer'
          WHEN itorigin.REFERENCECATEGORY = 13 THEN 'Inventory Counting'
          WHEN itorigin.REFERENCECATEGORY = 15 THEN 'Quarantine Order'
          WHEN itorigin.REFERENCECATEGORY = 21 THEN 'Transfer order shipment'
          WHEN itorigin.REFERENCECATEGORY = 22 THEN 'Transfer order receive'
          WHEN itorigin.REFERENCECATEGORY = 201 THEN 'Work'
          WHEN itorigin.REFERENCECATEGORY = 202 THEN 'Quarantine'
          ELSE 'Unknown'
     END AS [ReferenceCategory]
     , itorigin.REFERENCECATEGORY AS [ReferenceCategoryId]
      , itorigin.REFERENCEID AS [ReferenceId]
     , idim.LICENSEPLATEID
     , idim.MODIFIEDBY                                 AS [PerformedBy]
     --, tl.CREATEDBY                                    AS [JournalCreatedBy]
     , CAST(itrans.QTY AS DECIMAL(20,0))               AS Qty
     , CAST(itrans.COSTAMOUNTPOSTED AS DECIMAL(20,2))  AS [AmountPosted]
    
     , itrans.VOUCHER
     , COALESCE(CAST(gje.ACCOUNTINGDATE AS NVARCHAR(35)), 'N/A')             AS [AccountingDate]
     , COALESCE(gje.JOURNALNUMBER, 'N/A')              AS [JournalNumber]
     , CASE 
          WHEN gje.JOURNALCATEGORY IS NULL   THEN 'Work' -- This should be N/A. Just filling up this field
          WHEN gje.JOURNALCATEGORY = 0       THEN 'Huamei3?' -- Not really sure what is this(modifiedby)
          WHEN gje.JOURNALCATEGORY = 2       THEN 'Sales Order'
          WHEN gje.JOURNALCATEGORY = 3       THEN 'Purchase Order'
          WHEN gje.JOURNALCATEGORY = 4       THEN 'Inventory'
          WHEN gje.JOURNALCATEGORY = 8       THEN 'Customer'
          WHEN gje.JOURNALCATEGORY = 12      THEN 'Fixed assets'
          WHEN gje.JOURNALCATEGORY = 14      THEN 'Vendor'
          WHEN gje.JOURNALCATEGORY = 15      THEN 'Payment'
          WHEN gje.JOURNALCATEGORY = 17      THEN 'Bank'
          WHEN gje.JOURNALCATEGORY = 35      THEN 'Write Off'
          WHEN gje.JOURNALCATEGORY = 36      THEN 'General Journal'
          ELSE 'N/A'  END                                        AS [JournalCategory]
     , COALESCE(tl.TXT, 'N/A')                                   AS 'TransLogText'
     , COALESCE(CAST(tl.[TYPE] AS NVARCHAR(10)), 'N/A')          AS 'TransLogType' 
     --, idim.INVENTDIMID
     --, itrans.INVENTDIMID
     --, itorigin.ITEMINVENTDIMID
 FROM 
     INVENTTRANS itrans
     JOIN INVENTTRANSORIGIN itorigin    ON itrans.INVENTTRANSORIGIN   = itorigin.RECID              AND itorigin.[PARTITION] = itrans.[PARTITION]     AND itorigin.DATAAREAID  = itrans.DATAAREAID
     JOIN INVENTDIM idim                ON idim.INVENTDIMID           = itrans.INVENTDIMID          AND idim.[PARTITION]     = itrans.[PARTITION]     AND idim.DATAAREAID = itrans.DATAAREAID
     --Apparently there is no voucher when the ReferenceId is Work related
     LEFT JOIN GENERALJOURNALENTRY gje  ON itrans.VOUCHER <> '' AND gje.SUBLEDGERVOUCHER       = itrans.VOUCHER              AND gje.[PARTITION]      = itrans.[PARTITION]     AND gje.SUBLEDGERVOUCHERDATAAREAID = itrans.DATAAREAID
     LEFT JOIN TRANSACTIONLOG tl        ON gje.CREATEDTRANSACTIONID   = tl.CREATEDTRANSACTIONID     AND gje.[PARTITION]      = tl.[PARTITION]         AND tl.DATAAREAID = gje.SUBLEDGERVOUCHERDATAAREAID
     --LEFT JOIN INVENTTRANSPOSTING itp        ON itp.INVENTTRANSORIGIN      = itorigin.RECID              AND itorigin.[PARTITION] = itp.[PARTITION]        AND itorigin.DATAAREAID = 'ha' 
WHERE
     1 = 1 
     --AND idim.WMSLOCATIONID = '09-060W' 
     --AND itorigin.REFERENCECATEGORY IN ('13') -- Quantity adjustment
     --AND itorigin.REFERENCEID = 'WK0011185898'
     --AND idim.INVENTLOCATIONID = '4010'
     --AND itrans.MODIFIEDDATETIME > '11/18/2023 00:00:00'
     --AND itorigin.REFERENCECATEGORY IN (5, 13)
    -- AND itrans.ITEMID IS NOT NULL -- Warning
    
     AND itrans.ITEMID = '67392' --AND idim.INVENTCOLORID = '78M' --AND idim.INVENTSIZEID = 'M'
     --AND itrans.STATUSISSUE = 1 -- Sold
     --AND itorigin.REFERENCECATEGORY = 0 -- Sales Order
     --AND QTY > 1
     --AND idim.LICENSEPLATEID ='00008281301833780367'
ORDER BY 
     --itorigin.REFERENCEID
     itrans.MODIFIEDDATETIME

--sp_columns GENERALJOURNALENTRY
/*
SELECT 
     JOURNALCATEGORY
     , COUNT( * )
FROM
    GENERALJOURNALENTRY
GROUP BY 
     JOURNALCATEGORY

*/