USE DAX_PROD

-- Finding specific shipments
SELECT *
FROM WHSSHIPMENTTABLE
WHERE 
	--DELIVERYNAME = 'WINDSOR REDICK'
	DELIVERYNAME = 'ANGELA GIBSON'
	AND MODIFIEDDATETIME > '08/01/2022 04:00:00 AM'

-- Counting Shipments per customer( more than xx  )
SELECT 
	wstcnt.DELIVERYNAME, wstcnt.TotalCnt--, wstcnt.[Address], [AccountNum]
FROM
(
SELECT DeliveryName, Count( * ) AS TotalCnt--, [ADDRESS], AccountNum
FROM WHSShipmentTable wst
WHERE 
	wst.MODIFIEDDATETIME > '08/01/2022 04:00:00 AM'
	AND ISNULL( wst.DELIVERYNAME, '' ) <> ''
GROUP BY DELIVERYNAME--, [ADDRESS], AccountNum
) AS wstcnt
WHERE
	wstcnt.TotalCnt > 20

-- Different version, using Having( faster )
SELECT DeliveryName, Count( * ) AS TotalCnt--, [ADDRESS], AccountNum
FROM WHSShipmentTable wst
WHERE 
	wst.MODIFIEDDATETIME > '08/01/2022 04:00:00 AM'
	AND ISNULL( wst.DELIVERYNAME, '' ) <> ''
GROUP BY DELIVERYNAME--, [ADDRESS], AccountNum
HAVING Count( * ) > 20

/*
SELECT TOP 2 *
FROM [DAX_PROD].[dbo].[SALESTABLE]

SELECT TOP 2 *
FROM [DAX_PROD].[dbo].[WHSSHIPMENTTABLE]
*/

-- All the shipments heading to a customer with a different name that the one is on the order
SELECT 
	shptbl.SHIPMENTID, 
	shptbl.ORDERNUM, 
	shptbl.MODIFIEDBY,
	shptbl.MODIFIEDDATETIME, 
	shptbl.DELIVERYPOSTALADDRESS, 
	shptbl.DELIVERYNAME, 
	shptbl.SHIPMENTSTATUS,
	st.SALESID, 
	st.DELIVERYPOSTALADDRESS, 
	st.DELIVERYNAME,
	ll.LOADID, 
	ll.SHIPMENTID, 
	ll.ORDERNUM,
	ll.RELEASETOWAREHOUSEID, 
	ll.MODIFIEDBY, 
	ll.MODIFIEDDATETIME,
	ll.CREATEDBY, 
	ll.CREATEDDATETIME, 
	ll.ORDERNUM
FROM 
	[DAX_PROD].[dbo].[WHSSHIPMENTTABLE] AS shptbl
	INNER JOIN [DAX_PROD].[dbo].[SALESTABLE] st ON st.SALESID = shptbl.ORDERNUM AND st.DATAAREAID = shptbl.DATAAREAID AND st.[PARTITION] = shptbl.[PARTITION]
	INNER JOIN WHSLOADLINE ll on shptbl.SHIPMENTID = ll.SHIPMENTID and shptbl.DATAAREAID = ll.DATAAREAID and shptbl.PARTITION = ll.PARTITION
/*
WHERE 
	ISNULL( shptbl.SHIPMENTID, '' ) <> ''
	AND shptbl.MODIFIEDDATETIME	> GETUTCDATE() - 15
	AND shptbl.INVENTLOCATIONID	= '4010' 
	AND shptbl.INVENTSITEID		= 'HA USA'
	AND shptbl.DATAAREAID			= 'ha'
*/
GROUP BY 
	shptbl.SHIPMENTID, 
	shptbl.ORDERNUM, 
	shptbl.MODIFIEDBY,
	shptbl.MODIFIEDDATETIME, 
	shptbl.DELIVERYPOSTALADDRESS, 
	shptbl.DELIVERYNAME, 
	shptbl.SHIPMENTSTATUS,
	st.SALESID, 
	st.DELIVERYPOSTALADDRESS, 
	st.DELIVERYNAME,
	ll.LOADID, 
	ll.SHIPMENTID, 
	ll.ORDERNUM,
	ll.RELEASETOWAREHOUSEID, 
	ll.MODIFIEDBY, 
	ll.MODIFIEDDATETIME,
	ll.CREATEDBY, 
	ll.CREATEDDATETIME, 
	ll.ORDERNUM
HAVING shptbl.DELIVERYNAME <> st.DELIVERYNAME AND shptbl.SHIPMENTSTATUS < 5
ORDER BY ll.RELEASETOWAREHOUSEID asc

-- Todd Dewars
select 
	shipt.SHIPMENTID, shipt.ORDERNUM, shipt.MODIFIEDBY,	shipt.MODIFIEDDATETIME, shipt.DELIVERYPOSTALADDRESS, shipt.DELIVERYNAME, shipt.SHIPMENTSTATUS,
	st.SALESID, st.DELIVERYPOSTALADDRESS, st.DELIVERYNAME,
	ll.LOADID, ll.SHIPMENTID,ll.ORDERNUM, ll.RELEASETOWAREHOUSEID, ll.MODIFIEDBY, ll.MODIFIEDDATETIME, ll.CREATEDBY, ll.CREATEDDATETIME, ll.ORDERNUM
from WHSSHIPMENTTABLE shipt
     join SALESTABLE st on shipt.ORDERNUM = st.SALESID and shipt.DATAAREAID = st.DATAAREAID and shipt.PARTITION = st.PARTITION
     join WHSLOADLINE ll on shipt.SHIPMENTID = ll.SHIPMENTID and shipt.DATAAREAID = ll.DATAAREAID and shipt.PARTITION = ll.PARTITION
group by 
	shipt.SHIPMENTID, shipt.ORDERNUM, shipt.MODIFIEDBY, shipt.MODIFIEDDATETIME, shipt.DELIVERYPOSTALADDRESS, shipt.DELIVERYNAME, shipt.SHIPMENTSTATUS
	, st.SALESID, st.DELIVERYPOSTALADDRESS, st.DELIVERYNAME
	, ll.LOADID, ll.SHIPMENTID, ll.ORDERNUM, ll.RELEASETOWAREHOUSEID, ll.MODIFIEDBY, ll.MODIFIEDDATETIME, ll.CREATEDBY, ll.CREATEDDATETIME, ll.ORDERNUM
having shipt.SHIPMENTSTATUS < 5 and shipt.DELIVERYPOSTALADDRESS <> st.DELIVERYPOSTALADDRESS --shipt.DELIVERYPOSTALADDRESS = 5655453585 --shipt.DELIVERYNAME like 'lydia snapp'
order by ll.RELEASETOWAREHOUSEID asc

 