

SELECT 
    cntln.ContainerID
  --  , wktbl.CREATEDDATETIME
    , cntln.ITEMID
    , idim.INVENTCOLORID AS Color
    , idim.INVENTSIZEID AS Size
    , CONVERT( DECIMAL( 10, 2 ), physdim.WEIGHT, 1 ) AS Weight
    , CONVERT( DECIMAL( 10, 2 ), CASE WHEN physdim.DEPTH >= physdim.WIDTH THEN physdim.DEPTH ELSE physdim.WIDTH END, 1 )   AS "Length"
    , CONVERT( DECIMAL( 10, 2 ), CASE WHEN physdim.DEPTH < physdim.WIDTH THEN physdim.DEPTH ELSE physdim.WIDTH END, 1 )    AS "Depth"
    , CONVERT( DECIMAL( 10, 2 ), physdim.HEIGHT, 1 ) AS Height
    , CONVERT( DECIMAL( 10, 0 ), cntln.QTY, 1 ) AS Qty
    , CONVERT( DECIMAL( 10, 2 ), physdim.DEPTH * physdim.WIDTH * physdim.HEIGHT * cntln.Qty, 1 ) AS Volume
    , physdim.HA_MEASUREDBY       AS MeasuredBy
    , physdim.HA_MEASUREDDATE     AS MeasuredDate
    , physdim.MODIFIEDBY
    , physdim.Modifieddatetime  At Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS 'ModifiedDateTime'
FROM WHSCONTAINERLINE cntln 
LEFT JOIN WHSCONTAINERTABLE cnttbl ON cnttbl.CONTAINERID = cntln.CONTAINERID AND cntln.[PARTITION] = cnttbl.PARTITION AND cnttbl.DATAAREAID = 'ha'
--LEFT JOIN WHSWORKTABLE wktbl ON wktbl.CONTAINERID = cnttbl.CONTAINERID AND cntln.[PARTITION] = wktbl.PARTITION AND wktbl.DATAAREAID = 'ha'
LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = cntln.INVENTDIMID AND cntln.[PARTITION] = idim.PARTITION AND idim.DATAAREAID = 'ha'
LEFT JOIN WHSPHYSDIMUOM physdim ON physdim.ITEMID = cntln.ITEMID AND physdim.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physdim.ECORESITEMSIZENAME = idim.INVENTSIZEID
WHERE 
  1 = 1
  
  AND cnttbl.CONTAINERID ='CN018235997'
  --AND physdim.WEIGHT < 0
    --wktbl.WAVEID = 'WV000174587' AND
     --wktbl.CREATEDDATETIME > '6/6/2022' AND
     --wktbl.CREATEDDATETIME BETWEEN  GETUTCDATE() - 2 AND GETUTCDATE() 
     --AND cnttbl.CONTAINERTYPECODE = 'SM'
/*GROUP BY
  cntln.ContainerID, cntln.ITEMID, idim.INVENTSIZEID,  idim.INVENTCOLORID, physdim.WEIGHT,physdim.DEPTH, physdim.HEIGHT, physdim.WIDTH, cntln.QTY, physdim.HA_MEASUREDBY
    , physdim.HA_MEASUREDDATE, physdim.MODIFIEDBY, physdim.Modifieddatetime*/
     
--Order By Volume DESC    

--sp_columns WHSCONTAINERTABLE

