
--What pieces are sitting at the bander

SELECT  
  idim.licenseplateid       AS LP
  , wktbl.WORKSTATUS
  , isum.itemid             AS Item
  , idim.inventcolorid      AS Color
  , idim.inventsizeid       AS Size
  , isum.physicalinvent     AS Qty
  , isum.reservphysical     AS Reserved 
  , isum.AVAILPHYSICAL      AS AVAILPHYSICAL
  --, loc.zoneid        AS ZoneId
  --, loc.locprofileid  AS LocProfile
FROM inventsum isum
LEFT OUTER JOIN inventdim idim  ON isum.inventdimid = idim.inventdimid AND isum.DataAreaId = idim.DataAreaId AND isum.PARTITION = idim.PARTITION
LEFT OUTER JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = idim.LICENSEPLATEID AND wktbl.DATAAREAID = idim.DATAAREAID AND wktbl.[PARTITION] = idim.[PARTITION]
WHERE 
isum.physicalinvent > 0 
    AND idim.inventlocationid = '4010'
    AND idim.wmsLocationId = 'Bander' 
  --AND OH.AVAILPHYSICAL > 0
  --AND wktbl.WORKSTATUS = 1 -- Closed work
ORDER BY idim.wmslocationid