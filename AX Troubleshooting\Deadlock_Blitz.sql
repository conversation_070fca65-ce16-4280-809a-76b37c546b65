

-- 

EXEC sp_BlitzCache  @OnlySqlHandles = '0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000,0x020000004179cd0b9b9a0499e020f5de1f0cabb11f08914f0000000000000000000000000000000000000000';



--Filter by session ID:
EXEC sp_blitzlock @session_id = 1234;

--Filter by wait type:
EXEC sp_blitzlock @waittype = 'PAGEIOLATCH_WAIT';

--Filter by deadlock victim:
EXEC sp_blitzlock @victim_id = 'processabe601d088';

--Filter by blocking process:
EXEC sp_blitzlock @blocking_id = 'processabe4028ca8';

--Limit the number of results:
EXEC sp_blitzlock @top = 1000;

--Include additional information:
EXEC sp_blitzlock @include_waitstats = 1, @include_blocking_tree = 1;

--Specify the database to analyze:
EXEC sp_blitzlock @database_name = 'MyDatabase';

--Analyze blocking chains:
EX<PERSON> sp_blitzlock @blocking_chain = 1;

--Identify long-running transactions:
EXEC sp_blitzlock @long_running = 1;

--To filter results by wait type, include additional information, and limit the output to 10 rows:
EXEC sp_blitzlock @waittype = 'PAGEIOLATCH_WAIT', @include_waitstats = 1, @include_blocking_tree = 1, @top = 10;


