
-- 3/7/2025
-- Query used on the Power BI report to get the orders that need to be shipped and are not shipped yet
-- The query is used to get the orders that are not shipped yet and are in the following modes: 1st Day, 2nd Day, 3rd Day, Saturday, International Economy
-- Faster than the other versions of the query

--3-14-2025
-- Adding the HoursSinceReleased column to the query
-- Including all orders that are not shipped yet, not only express orders

-- 4/2/2025
-- Adding the WholeSale column to the query

-- 4/9/2025
-- Adding the ShipmentStatus column to the query

DECLARE @CurrentDateTime datetime = GETUTCDATE();

WITH orders AS
(
SELECT
    wst.ORDERNUM
  --, wst.ACCOUNTNUM
    , CASE WHEN st.CUSTGROUP LIKE 'W%' THEN 'Yes' ELSE 'No' END AS [WholeSale] 
    , wst.SHIPMENTID
    ,  CASE 
		--WHEN wst.SHIPMENTSTATUS IS NULL THEN 'Not released'
		WHEN wst.SHIPMENTSTATUS = 0 THEN 	'Open'
		WHEN wst.SHIPMENTSTATUS = 1 THEN 	'Waved'
		WHEN wst.SHIPMENTSTATUS = 2 THEN 	'In process'
		WHEN wst.SHIPMENTSTATUS = 3 THEN 	'In packing'
		WHEN wst.SHIPMENTSTATUS = 4 THEN 	'Loaded'
		--WHEN wst.SHIPMENTSTATUS = 5 THEN 	'Shipped'
		ELSE 									'Received'
	END AS ShipmentStatus -- Adding it for cross-reference with the other report(Outbound)
    , IIF(wst.HALOCDIRECTFAIL = 1, 'Yes', 'No')     AS [LDF]
     , COALESCE(cnttbl.CONTAINERID, 'N/A')          AS [ContainerId]
     , COALESCE(wktbl.WORKID, 'N/A')                AS [WORKID]
    --, wktbl.WORKID
    , CASE 
            WHEN wktbl.FROZEN = 1 THEN 'Yes' 
            WHEN wktbl.FROZEN = 0 THEN 'No' 
            ELSE 'N/A' 
        END AS [WkBlocked]
    , CASE 
        WHEN wktbl.WORKSTATUS = 0 THEN 'Open' 
        WHEN wktbl.WORKSTATUS = 1 THEN 'In process'
        WHEN wktbl.WORKSTATUS = 4 THEN 'Closed'
        ELSE 'N/A'
    END AS WkStatus
    , wkusr.USERNAME            AS [LockedUser]
    , (SELECT TOP 1 -- TOP 1 because there could be more than one line
        wkuser.USERNAME
        FROM HAVOICEINTEGRATIONQUEUEPICKS vip 
        LEFT JOIN WHSWORKUSER wkuser WITH (NOLOCK) ON wkuser.USERID = vip.WORKUSER AND wkuser.DATAAREAID = 'ha' AND wkuser.[PARTITION] = vip.[PARTITION] 
        WHERE 
            vip.WORKID = wktbl.WORKID AND vip.STATUS < 5 -- 0-Pending, 2-Completed, 4-Resets
            AND vip.DATAAREAID = 'ha' AND vip.[PARTITION] = wktbl.[PARTITION]
        ORDER BY vip.[STATUS]    -- 0 - Pending first
        ) AS [VoicePickUser]
    , vput.[STATUS]             AS [PutStatus]
    , wkusr1.USERNAME           AS [VoicePutUser]
    , IIF(ISNULL(wkcl.CLUSTERID, '') = '','N/A', wkcl.CLUSTERID)            AS 'ClusterId'
    , IIF(ISNULL(wkct.CLUSTERID, '') = '', 'N/A', wkct.CLUSTERPROFILEID)    AS 'ClusterProfile'
    , IIF(ISNULL(cnttbl.CONTAINERID, '') = '', 0, CONVERT( DECIMAL( 10, 0), SUM( cntln.QTY ) ) )                                         AS CntUnits
    , wst.MODECODE
    --, st.DLVMODE
    , wst.WAVEID
    , wvt.WAVETEMPLATENAME
    , (SELECT CAST(SUM(ll.QTY) AS INT) FROM WHSLOADLINE ll WHERE ll.SHIPMENTID = wst.SHIPMENTID AND ll.ORDERNUM = st.SALESID AND ll.LOADID = wst.LOADID AND ll.DATAAREAID = 'ha') AS Units
    , (SELECT MIN(ll.CREATEDDATETIME) FROM WHSLOADLINE ll WHERE ll.SHIPMENTID = wst.SHIPMENTID AND ll.ORDERNUM = st.SALESID AND ll.LOADID = wst.LOADID  AND ll.DATAAREAID = 'ha')  AS [ReleasedToWH]

    --, wst.DELIVERYNAME                                                                      AS ShipTo
    --, hacs.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS ActualShippedTime
    , IIF(wktbl.WORKID IS NULL, 'N/A', IIF(EXISTS (SELECT '1' FROM WHSREPLENWORKLINK WHERE DEMANDWORKID= wktbl.WORKID AND DATAAREAID= 'ha'), 'Yes', 'No')) AS [NeedsReplen]
    --, hacs.TRACKINGNUMBER
FROM
    SALESTABLE st
    -- Taking into account orders with shipments created
    JOIN WHSSHIPMENTTABLE wst                   WITH (NOLOCK) ON st.SALESID = wst.ORDERNUM                  AND st.[PARTITION] = wst.[PARTITION]        AND wst.DATAAREAID = st.DATAAREAID 
    JOIN WHSWAVETABLE wvt                       WITH (NOLOCK) ON wvt.WAVEID = wst.WAVEID                    AND wvt.[PARTITION] = wst.[PARTITION]       AND wvt.DATAAREAID = wst.DATAAREAID
    LEFT JOIN WHSCONTAINERTABLE cnttbl          WITH (NOLOCK) ON wst.SHIPMENTID = cnttbl.SHIPMENTID         AND cnttbl.[PARTITION] = wst.[PARTITION]    AND cnttbl.DATAAREAID = wst.DATAAREAID
    LEFT JOIN WHSCONTAINERLINE cntln            WITH (NOLOCK) ON cntln.CONTAINERID = cnttbl.CONTAINERID     AND cnttbl.[PARTITION] = cntln.[PARTITION]  AND cnttbl.DATAAREAID = cntln.DATAAREAID
    LEFT JOIN WHSWORKTABLE wktbl                WITH (NOLOCK) ON wktbl.CONTAINERID = cnttbl.CONTAINERID     AND wktbl.[PARTITION] = cnttbl.[PARTITION]  AND wktbl.DATAAREAID = cnttbl.DATAAREAID
    LEFT JOIN WHSWORKCLUSTERLINE wkcl           WITH (NOLOCK) ON wkcl.WORKID = wktbl.WORKID                 AND wkcl.[PARTITION] = wktbl.[PARTITION]    AND wkcl.DATAAREAID = wktbl.DATAAREAID
    LEFT JOIN WHSWORKCLUSTERTABLE wkct          WITH (NOLOCK) ON wkcl.CLUSTERID = wkct.CLUSTERID            AND wkcl.[PARTITION] = wkct.[PARTITION]     AND wkct.DATAAREAID = wkcl.DATAAREAID
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs       WITH (NOLOCK) ON hacs.CARTONID = cnttbl.CONTAINERID         AND hacs.[PARTITION] = cnttbl.[PARTITION]   AND cnttbl.DATAAREAID = hacs.DATAAREAID
    LEFT JOIN WHSWORKUSER wkusr                 WITH (NOLOCK) ON wkusr.USERID = wktbl.LOCKEDUSER            AND wkusr.[PARTITION] = wktbl.[PARTITION]   AND wkusr.DATAAREAID = wktbl.DATAAREAID
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput  WITH (NOLOCK) ON wktbl.WORKID = vput.WORKID                 AND wktbl.[PARTITION] = vput.[PARTITION]    AND wktbl.DATAAREAID = vput.DATAAREAID
    LEFT JOIN WHSWORKUSER wkusr1                WITH (NOLOCK) ON wkusr1.USERID = vput.WORKUSER              AND wkusr1.[PARTITION] = vput.[PARTITION]   AND wkusr1.DATAAREAID = vput.DATAAREAID
        --AND wktbl.LOCKEDUSER <> ''
WHERE
    --wll.CREATEDDATETIME > GETUTCDATE() - @DaysToCheck
    wst.SHIPMENTSTATUS < 5 -- Not Shipped
    AND st.SALESSTATUS = 1 -- Open order

    AND (cnttbl.SHIPCARRIERTRACKINGNUM = '' OR cnttbl.SHIPCARRIERTRACKINGNUM IS NULL) -- Not shipped(including the tracking number added to the table) or tracking number not available
    AND hacs.TRACKINGNUMBER IS NULL -- Not shipped yet
    --AND wst.MODECODE IN ( '1D', '2D', '3D', 'SA', 'IE' ) -- 1st Day, 2nd Day, 3rd Day, Saturday, International Economy
    --AND wst.WAVEID IS NOT NULL -- Wave created
    
    --AND hacs.CREATEDDATETIME IS NULL -- Not shipped yet
    --AND wst.SHIPCONFIRMUTCDATETIME > CONCAT( @DayToCheck, '07:30:00 PM' )
    --AND wst.ORDERNUM = '44426351'
GROUP BY
    cnttbl.CONTAINERID, wst.SHIPMENTID, wst.LOADID, wst.ORDERNUM, wst.MODECODE, wst.HALOCDIRECTFAIL --, st.DLVMODE, st.CREATEDDATETIME
    , hacs.CREATEDDATETIME,  wst.WAVEID, wktbl.FROZEN, wkcl.CLUSTERID, wktbl.WORKSTATUS, wkusr.USERNAME, wktbl.WORKID
    , wkct.CLUSTERID, wkct.CLUSTERPROFILEID, wvt.WAVETEMPLATENAME, st.SALESID, hacs.TRACKINGNUMBER, wktbl.[PARTITION], vput.[STATUS], wkusr1.USERNAME
    , st.CUSTGROUP, wst.SHIPMENTSTATUS
)
SELECT 
    ORDERNUM                                                AS [OrderNum]
    , WHoleSale                                             AS [WholeSale]
    , MODECODE                                              AS [ShipMethod]
    , CASE 
        WHEN 
            MODECODE IN ( '1D', '2D', '3D', 'SA', 'IE' ) 
            THEN 'Yes' ELSE 'No' 
    END                                                     AS [IsRush]
    , FORMAT(ReleasedToWH AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 'MMM dd yyyy hh:mmtt', 'en-US')  AS [ReleasedToWH]
    , FORMAT(ReleasedToWH, 'MMM dd, yyyy')                  AS [SLA_Date] -- Formatting as String, for consistency on PBI
    , DATEDIFF(HH, ReleasedToWH, @CurrentDateTime)          AS [HoursSinceReleased]
    -- Use the container units if available
    , CASE WHEN CntUnits > 0 THEN CntUnits ELSE Units END   AS [Units]
    , SHIPMENTID                                            AS [ShipmentId]
    , ShipmentStatus                                        AS [ShipmentStatus]
    , LDF                                                   AS [LDF]
    , CONTAINERID                                           AS [ContainerId]
    , WORKID                                                AS [WorkId]
    , NeedsReplen                                           AS [NeedsReplen]
    , WkBlocked                                             AS [WorkBlocked]
    , WkStatus                                              AS [WorkStatus]
    , COALESCE(LockedUser,'N/A')                            AS [LockedBy]
    , COALESCE(VoicePutUser, VoicePickUser,'N/A')           AS [VoiceUser]
    , CASE 
        WHEN PutStatus IS NULL THEN CASE WHEN VoicePickUser IS NULL THEN 'N/A' ELSE 'In Progress' END --
        ELSE 
            CASE 
                WHEN PutStatus = 0 THEN 'Pending'
                WHEN PutStatus = 2 THEN 'Completed'
                WHEN PutStatus = 3 THEN 'Error' -- Not sure if this is the correct status
                WHEN PutStatus = 4 THEN 'Reset'
                WHEN PutStatus = 5 THEN 'Manually Picked'
                WHEN PutStatus = 6 THEN 'Canceled'
                ELSE 'N/A'
            END END AS [VoiceStatus]
    , CLUSTERID                                             AS [ClusterId]
    , ClusterProfile                                        AS [ClusterProfile]
    , WAVEID                                                AS [WaveId]
    , [WAVETEMPLATENAME]                                    AS [WaveTemplate]
FROM
    orders
ORDER BY
    HoursSinceReleased DESC
    --ReleasedToWH, OrderNum

--