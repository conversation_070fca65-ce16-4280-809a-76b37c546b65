--Empty bins

SELECT ONHI.Location, ONHI.ZoneId, ONHI.LocProfile
FROM
(
SELECT  wmslocation.zoneid as ZoneId, wmslocation.locprofileid as LocProfile, OH.itemid as Item, OH.inventcolorid as Color, OH.inventsizeid as Size, wmslocation.wmslocationid as Location, 
wmslocation.sortcode AS SortCode, OH.physicalinvent as Qty, OH.reservphysical AS Reserved 
FROM wmslocation
LEFT OUTER JOIN 
( SELECT inventsum.itemid, inventsum.physicalinvent, inventsum.reservphysical, inventdim.inventcolorid, inventdim.inventsizeid, inventdim.wmslocationid
  FROM inventsum
  LEFT OUTER JOIN inventdim
  ON inventsum.inventdimid = inventdim.inventdimid
  WHERE inventsum.physicalinvent > 0 AND inventdim.inventlocationid = '4010'
) as OH
ON wmslocation.wmslocationID = OH.wmslocationid
WHERE wmslocation.inventlocationid = '4010' AND wmslocation.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', 'W001', 'Bulk'/*, 'Overflow' */) AND wmslocation.zoneid NOT IN ( 'Current Pick', 'Invalid' )
) AS ONHI
WHERE ONHI.Qty IS NULL 
ORDER BY ONHI.SortCode, ONHI.Location