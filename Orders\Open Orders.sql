--Open orders report- LA modified

 SELECT 
 	st.salesid
	, CAST(  sl.createddatetime AS Date  ) 	AS CreatedOn
	, sl.itemid 							AS Item
	, id.INVENTCOLORID 						AS Color
	, id.INVENTSIZEID 						AS Size
	, CONVERT( DECIMAl, sl.qtyordered, 10 ) AS Qty
	, st.MCROrderStopped 					AS Hold
	, wll.SHIPMENTID						AS ShipmentID
	, ReleasedToWHDate = wll.CREATEDDATETIME
	, wll.LOADID							AS LoadID
	, CASE 
		WHEN shptbl.SHIPMENTSTATUS IS NULL THEN 'Not released'
		WHEN shptbl.SHIPMENTSTATUS = 0 THEN 	'Open'
		WHEN shptbl.SHIPMENTSTATUS = 1 THEN 	'Waved'
		WHEN shptbl.SHIPMENTSTATUS = 2 THEN 	'In process'
		WHEN shptbl.SHIPMENTSTATUS = 3 THEN 	'In packing'
		WHEN shptbl.SHIPMENTSTATUS = 4 THEN 	'Loaded'
		WHEN shptbl.SHIPMENTSTATUS = 5 THEN 	'Shipped'
		ELSE 									'Received'
	END AS LDStat -- Same name, but shipment status

-- Not using the load status because put together Open and Not released.
/*
(
	CASE WHEN wlt.LOADSTATUS = 0 THEN 'Open' 
		WHEN wlt.LOADSTATUS = 1 THEN 'Posted' 
    	WHEN wlt.LOADSTATUS = 2 THEN 'Waved' 
		WHEN wlt.LOADSTATUS = 3 THEN 'In process'
		WHEN wlt.LOADSTATUS = 4 THEN CASE WHEN ISNULL( wll.LOADID, '' ) = '' THEN 'Open' ELSE 'In packing' END
		WHEN wlt.LOADSTATUS = 5 THEN 'Loaded'
		WHEN wlt.LOADSTATUS = 6 THEN 'Shipped'
	ELSE 'Not released'  END	) AS LDStat
*/

FROM SALESTABLE st WITH(NOLOCK) JOIN salesline sl WITH(NOLOCK)	
	ON  st.salesid = sl.salesid AND st.dataareaid = sl.dataareaid AND st.Partition = sl.Partition AND sl.salesstatus = 1 -- only open sales lines,2 -delivered, 3 - invoiced, 4 - canceled
	AND sl.qtyordered > 0 AND sl.custgroup not like 'W%'
JOIN inventdim id WITH(NOLOCK) ON  sl.inventdimid = id.inventdimid AND sl.dataareaid = id.dataareaid AND sl.Partition = id.Partition
LEFT JOIN WHSLOADLINE wll WITH(NOLOCK) 	ON sl.INVENTTRANSID = wll.INVENTTRANSID AND sl.DATAAREAID = wll.DATAAREAID  AND sl.Partition = wll.Partition AND wll.INVENTTRANSTYPE = 0 -- Sales transaction types only
LEFT JOIN WHSLOADTABLE wlt WITH(NOLOCK) ON wll.LOADID = wlt.LOADID AND wll.DATAAREAID = wlt.DATAAREAID AND wll.Partition = wlt.Partition
LEFT JOIN WHSSHIPMENTTABLE shptbl WITH(NOLOCK) ON shpTbl.SHIPMENTID = wll.SHIPMENTID AND shptbl.[PARTITION] = wll.[PARTITION] AND shptbl.DATAAREAID = 'ha'
WHERE st.salesstatus = 1 -- Open orders
ORDER BY st.SALESID ASC

--sp_columns salesline

/*
SELECT 	st.salesid, 
CAST( 
    CASE  WHEN DATEPART( mm, sl.createddatetime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, sl.createddatetime ) -- Daylight Savings Months 
          WHEN DATEPART( mm, sl.createddatetime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, sl.createddatetime ) -- No DST
          WHEN DATEPART( mm, sl.createddatetime ) = 3 
          THEN CASE WHEN DATEPART( dd, sl.createddatetime ) < 8 OR DATEPART( dd, sl.createddatetime ) > 14 THEN  DATEADD( hh, - 5, sl.createddatetime ) -- No DST
                    WHEN DATEPART( dd, sl.createddatetime ) - DATEPART( w, sl.createddatetime ) + 1  >= 8 THEN  DATEADD(hh, - 4, sl.createddatetime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, sl.createddatetime )
                END
          WHEN DATEPART( dd, sl.createddatetime ) - DATEPART( w, sl.createddatetime ) + 1  >= 1 --  After first Sunday, November
          THEN DATEADD( hh, - 4, sl.createddatetime )
          ELSE DATEADD( hh, - 5, sl.createddatetime ) END  AS Date ) AS CreatedOn , 
sl.itemid AS Item, id.INVENTCOLORID AS Color, id.INVENTSIZEID AS Size, CONVERT( DECIMAl, sl.qtyordered, 10 ) AS Qty,
st.MCROrderStopped AS Hold, wll.SHIPMENTID, wll.CREATEDDATETIME AS ReleasedToWHDate, wll.LOADID, (
CASE WHEN wlt.LOADSTATUS = 0 THEN 'Open' 
	WHEN wlt.LOADSTATUS = 1 THEN 'Posted' 
    WHEN wlt.LOADSTATUS = 2 THEN 'Waved' 
	WHEN wlt.LOADSTATUS = 3 THEN 'In process'
	WHEN wlt.LOADSTATUS = 4 THEN CASE WHEN ISNULL( wll.LOADID, '' ) = '' THEN 'Open' ELSE 'In packing' END
	WHEN wlt.LOADSTATUS = 5 THEN 'Loaded'
	WHEN wlt.LOADSTATUS = 6 THEN 'Shipped'
	ELSE 'Not released'  END	) AS LDStat
FROM SALESTABLE st WITH(NOLOCK) 
JOIN SALESLINE sl  WITH(NOLOCK) ON st.salesid = sl.salesid AND st.dataareaid = sl.dataareaid AND st.Partition = sl.Partition AND sl.salesstatus = 1 -- only open sales lines,2 -delivered, 3 - invoiced, 4 - canceled
AND sl.qtyordered > '0' AND sl.custgroup not like 'W%'
JOIN inventdim id WITH(NOLOCK) ON  sl.inventdimid = id.inventdimid AND sl.dataareaid = id.dataareaid AND sl.Partition = id.Partition
LEFT JOIN WHSLOADLINE wll WITH(NOLOCK) ON sl.INVENTTRANSID = wll.INVENTTRANSID AND sl.DATAAREAID = wll.DATAAREAID  AND sl.Partition = wll.Partition AND wll.INVENTTRANSTYPE = 0 -- Sales transaction types only
LEFT JOIN WHSLOADTABLE wlt WITH(NOLOCK) ON wll.LOADID = wlt.LOADID AND wll.DATAAREAID = wlt.DATAAREAID AND wll.Partition = wlt.Partition
--INNER JOIN WHSWORKTABLE wt ON wt.LOADID = wll.LOADID AND wll.DATAAREAID = wt.DATAAREAID AND wll.Partition = wt.Partition
WHERE st.salesstatus = 1 -- Open orders
ORDER BY st.SALESID ASC

SELECT wlt.LOADID, wlt.HASHIPMENTSTATUS
FROM WHSLOADTABLE wlt
WHERE wlt.LOADSTATUS = 4 AND wlt.MODIFIEDDATETIME > '10/10/21'

*/