-- ONHIAllDim - Excel query
-- Starting to modify this query on 7/25/2023
-- The goal is to get all the information at once

  SELECT --TOP 100
     CASE WHEN ISNULL( idim.INVENTSIZEID, '' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
          WHEN ISNULL( idim.INVENTCOLORID,'' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID
          ELSE itbl.ITEMID
     END AS 'SKU'
    --, itbl.NAMEALIAS
    , prodtrans.DESCRIPTION AS 'Description'
    , idim.[INVENTSTATUSID] AS 'Status'
    , CONVERT( DECIMAL( 10, 0 ), isum.PHYSICALINVENT ) AS 'Qty'
    , CONVERT( DECIMAL( 10, 0 ), isum.RESERVPHYSICAL ) AS 'Reserve'
    --, CONVERT( DECIMAL(10, 0 ), isum.ONORDER ) AS 'OnOrder' 
    , CONVERT( DECIMAL(10, 0), isum.physicalinvent - isum.reservphysical - isum.onorder) AS Available
    , idim.[LICENSEPLATEID] AS 'LicensePlate'
    , idim.[WMSLOCATIONID] AS 'Bin'
    , wmsloc.LOCPROFILEID AS 'Bin Type'
    , CONVERT( DECIMAL( 10, 2 ), physd.[WEIGHT],    0)  AS 'Weight'
    , CONVERT( DECIMAL( 10, 2 ), physd.[DEPTH],     0 ) AS 'Depth'
    , CONVERT( DECIMAL( 10, 2 ), physd.[WIDTH],     0 ) AS 'Width'
    , CONVERT( DECIMAL( 10, 2 ), physd.[HEIGHT],    0 ) AS 'Height'
    --, physd.UOM
    , CONVERT( DECIMAL( 10, 2 ), ISNULL( physd.[DEPTH], 0 ) * ISNULL( physd.[WIDTH], 0) * ISNULL( physd.[HEIGHT], 0 ) ) AS 'Volume'  
    , physd.HA_MEASUREDBY   AS MeasuredBy
    , CAST( physd.HA_MEASUREDDATE AT TIME Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME ) AS MeasuredDate
    , physd.MODIFIEDBY
    , CAST( physd.MODIFIEDDATETIME AT TIME Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME ) AS MODIFIEDDATETIME
    --, COUNT(*) OVER (PARTITION BY itbl.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID) AS TotalLocCount_
    --, COUNT(*) OVER (PARTITION BY itbl.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, wmsloc.LOCPROFILEID) AS LocCoun_ByLocProfile
    --, prod.cost AS Cost
    --, ( isum.physicalinvent - isum.reservphysical - isum.onorder ) * prod.cost AS 'ExtCost'
FROM 
    INVENTDIM idim 
    LEFT JOIN INVENTSUM isum  WITH (NOLOCK)        ON idim.INVENTDIMID = isum.INVENTDIMID          AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION]
    LEFT JOIN WHSPHYSDIMUOM physd WITH (NOLOCK)     ON isum.ITEMID  = physd.ITEMID  AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID
        AND physd.DATAAREAID = isum.DATAAREAID AND physd.[PARTITION] = isum.[PARTITION]
    INNER JOIN WMSLOCATION wmsloc                   ON idim.WMSLOCATIONID = wmsloc.WMSLOCATIONID    AND wmsloc.DATAAREAID = idim.DATAAREAID AND wmsloc.[PARTITION] = idim.[PARTITION]
    INNER JOIN INVENTTABLE itbl                     ON isum.ITEMID      = itbl.ITEMID               AND itbl.DATAAREAID = isum.DATAAREAID AND itbl.[PARTITION] = isum.[PARTITION]
    INNER JOIN ECORESPRODUCTTRANSLATION prodtrans   ON itbl.PRODUCT = prodtrans.PRODUCT             AND prodtrans.[PARTITION] = itbl.[PARTITION]
/*
LEFT JOIN HARMSPRODUCT prod ON 
        CASE
            WHEN ISNULL( idim.INVENTSIZEID, '' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
            WHEN ISNULL( idim.INVENTCOLORID,'' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID
            ELSE itbl.ITEMID 
        END = prod.ITEMSKU  
        AND prod.DATAAREAID = isum.DATAAREAID AND prod.[PARTITION] = idim.[PARTITION]
*/
WHERE 
    isum.PHYSICALINVENT <> 0 
    AND isum.PHYSICALINVENT < 99999 
    AND idim.INVENTLOCATIONID = '4010' 
    AND idim.INVENTSITEID = 'HA USA' 
    AND wmsloc.LOCPROFILEID IN ( 'Bulk', /*'Inbound', 'No LP Track',*/ 'W001', 'Picking', 'Picking A','CUBISCAN' ) 
    AND itbl.ITEMID NOT IN ( '30991', '3333', '9999', '9997' ) 
    --AND itbl.ITEMID LIKE '3%'
/*
GROUP BY 
    idim.INVENTLOCATIONID, itbl.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, prodtrans.DESCRIPTION, idim.[INVENTSTATUSID], idim.[LICENSEPLATEID], idim.[WMSLOCATIONID], isum.PHYSICALINVENT, isum.RESERVPHYSICAL, isum.onorder, wmsloc.LOCPROFILEID, prod.COST
*/
ORDER BY 
    'SKU', 'Bin Type', 'Bin'
