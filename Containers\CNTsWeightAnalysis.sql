
-- Calculating weights on the non-AB containers
-- <PERSON>'s request, 06/19/2024

-- What would be the allowed difference between the expected and actual weight?
-- Using 75%
DECLARE @WeightAllowance DECIMAL(20,2) = 75;
DECLARE @DaysToReport   INT = 365;
DECLARE @SplitReporAt   INT = 150;

WITH CNTWeight AS(
SELECT  --TOP 20
    cnt.CONTAINERID                                                         AS [Container]
    , cnt.CONTAINERTYPECODE                                                 AS [ContainerType]
    , CAST(cnt.WEIGHT AS DECIMAL(20, 2) )                                   AS [ExpectedWeight]
    , CAST(cnt.HAWHSAJILLUSWEIGHT AS DECIMAL(20, 2) )                       AS [ActualWeight]
    , CAST(ABS((cnt.HAWHSAJILLUSWEIGHT - cnt.WEIGHT)) AS DECIMAL(20, 2) )   AS [WeightDiff]
    , CAST(
    CASE 
        WHEN cnt.WEIGHT > cnt.HAWHSAJILLUSWEIGHT 
            THEN 
                (cnt.WEIGHT / cnt.HAWHSAJILLUSWEIGHT - 1) * 100
            ELSE 
                (cnt.HAWHSAJILLUSWEIGHT / cnt.WEIGHT - 1 ) * 100
    END  AS DECIMAL(20, 0) )                                              AS [OverUnderPerCent]
FROM
    [DAX_ARCHIVE].[arc].WHSCONTAINERTABLE cnt
    INNER JOIN [DAX_ARCHIVE].[arc].WHSWORKTABLE wktbl ON cnt.CONTAINERID = wktbl.CONTAINERID AND wktbl.DATAAREAID = cnt.DATAAREAID AND cnt.[PARTITION] = wktbl.[PARTITION]
    INNER JOIN [DAX_ARCHIVE].[arc].WHSWORKLINE wkln ON wkln.WORKID = wktbl.WORKID AND wkln.DATAAREAID = wktbl.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wkln.LINENUM = 1.00
WHERE
    --cnt.CONTAINERTYPECODE <> 'AB'
    wktbl.WORKTEMPLATECODE = '4010 Direct'  -- Not AB
    AND cnt.DATAAREAID = 'ha'
    AND cnt.CONTAINERSTATUS = 2 -- Closed
    AND cnt.HAWHSAJILLUSWEIGHT > 0 -- Already shipped
    AND cnt.WEIGHT > 0             -- Avoiding possible errors 
    AND wkln.ITEMID <> '3333'
    AND wktbl.CREATEDDATETIME BETWEEN GETUTCDATE() - @DaysToReport AND GETUTCDATE() - @SplitReporAt
UNION
SELECT  --TOP 20
    cnt.CONTAINERID                                                         AS [Container]
    , cnt.CONTAINERTYPECODE                                                 AS [ContainerType]
    , CAST(cnt.WEIGHT AS DECIMAL(20, 2) )                                   AS [ExpectedWeight]
    , CAST(cnt.HAWHSAJILLUSWEIGHT AS DECIMAL(20, 2) )                       AS [ActualWeight]
    , CAST(ABS((cnt.HAWHSAJILLUSWEIGHT - cnt.WEIGHT)) AS DECIMAL(20, 2) )   AS [WeightDiff]
    , CAST(
    CASE 
        WHEN cnt.WEIGHT > cnt.HAWHSAJILLUSWEIGHT 
            THEN 
                (cnt.WEIGHT / cnt.HAWHSAJILLUSWEIGHT - 1) * 100
            ELSE 
                (cnt.HAWHSAJILLUSWEIGHT / cnt.WEIGHT - 1 ) * 100
    END  AS DECIMAL(20, 0) )                                              AS [OverUnderPerCent]
FROM
    [DAX_PROD].[dbo].WHSCONTAINERTABLE cnt
    INNER JOIN [DAX_PROD].[dbo].WHSWORKTABLE wktbl ON cnt.CONTAINERID = wktbl.CONTAINERID AND wktbl.DATAAREAID = cnt.DATAAREAID AND cnt.[PARTITION] = wktbl.[PARTITION]
    INNER JOIN [DAX_PROD].[dbo].WHSWORKLINE wkln ON wkln.WORKID = wktbl.WORKID AND wkln.DATAAREAID = wktbl.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wkln.LINENUM = 1.00
WHERE
    --cnt.CONTAINERTYPECODE <> 'AB'
    wktbl.WORKTEMPLATECODE = '4010 Direct'  -- Not AB
    AND cnt.DATAAREAID = 'ha'
    AND cnt.CONTAINERSTATUS = 2 -- Closed
    AND cnt.HAWHSAJILLUSWEIGHT > 0 -- Already shipped
    AND cnt.WEIGHT > 0             -- Avoiding possible errors 
    AND wkln.ITEMID <> '3333'
    AND wktbl.CREATEDDATETIME >= GETUTCDATE() - @SplitReporAt
)
SELECT
    CAST(AVG(ExpectedWeight)    AS DECIMAL(20, 2) )  AS [AvgExpectedWeight]
    , CAST(AVG(ActualWeight)    AS DECIMAL(20, 2) )  AS [AvgActualWeight]
    , CAST(MIN(WeightDiff)      AS DECIMAL(20, 2) )  AS [MinDiffWeight]
    , CAST(MAX(WeightDiff)      AS DECIMAL(20, 2) )  AS [MaxDiffWeight]
    , CAST(STDEV(WeightDiff)    AS DECIMAL(20, 2) )  AS [DiffStdDev] 
FROM
    CNTWeight
WHERE
    1 = 1
    AND OverUnderPerCent <= @WeightAllowance
    --WeightProportion < @WeightAllowance
    --AND WeightDiff > 6
/*
SELECT TOP 20 * 
FROM 
    SALESTABLE st
WHERE 
    ISNULL(st.CUSTOMERREF, '') <> ''
    AND st.INVENTLOCATIONID = '4010'
    --st.salesid = '45395498'

SELECT * FROM SALESLINE WHERE SALESID = '45284779'
SELECT * FROM WHSWORKLINE WHERE CONTAINERID = 'CN015599100'
SELECT * FROM WHSWORKTABLE WHERE CONTAINERID = 'CN015599100'
*/