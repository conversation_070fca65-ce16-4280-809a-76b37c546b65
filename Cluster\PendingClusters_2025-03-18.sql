-- Author: <PERSON>

--Pending clusters, working version
-- Modified on 7/8/2022 to align it with the new clustering proces.
-- A cluster could be associated with more than one wave
-- 2/20/2025
-- Adding the SLA Date for better filtering on the PBI dashboard
-- Changing to one line the Daylight savings calculation
-- Changing query format(leading commas)
-- 3/7/2025
-- Adding literals for the partition and dataarea on the WHERE clause
-- 3/12/2025
-- Adding the VoicePickUser and VoicePutUser to the query
-- Adding the VoiceStatus to the query
-- 3/18/2025
-- Removing orders already shipped from the query

DECLARE @ReportDays INT = 20;

WITH PendCL AS (
    SELECT 
        CT.CLUSTERID,
        CT.CLUSTERPROFILEID,
        CT.CREATEDBY,
        CT.CREATEDDATETIME,
        vput.[STATUS] AS PutStatus,  -- Keep status unaggregated
        MAX(wkusr.USERNAME) AS VoicePutUser,
        MAX(voicepick.VoicePickUser) AS VoicePickUser,
        COUNT(CL.WORKID) AS WkPending,
        MIN(CAST(ll.CREATEDDATETIME AS DATE)) AS ReleasedToWH
    FROM WHSWORKCLUSTERLINE CL
    INNER JOIN WHSWORKCLUSTERTABLE CT 
        ON CL.CLUSTERID = CT.CLUSTERID
        AND CL.DATAAREAID = 'ha' AND CL.[PARTITION] = 5637144576
        AND CT.DATAAREAID = 'ha' AND CT.[PARTITION] = 5637144576
    INNER JOIN WHSWORKTABLE WT 
        ON CL.WORKID = WT.WORKID
        AND WT.DATAAREAID = 'ha' AND WT.[PARTITION] = 5637144576
        AND WT.WORKSTATUS < 2
        AND WT.WORKTRANSTYPE = 2
        AND WT.WORKTEMPLATECODE LIKE '4010%' 
        AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%'
        AND WT.CREATEDDATETIME > DATEADD(DAY, -@ReportDays, GETDATE())
    INNER JOIN WHSCONTAINERTABLE CNTTbl 
        ON WT.CONTAINERID = CNTTbl.CONTAINERID
        AND CNTTbl.DATAAREAID = 'ha' AND CNTTbl.[PARTITION] = 5637144576
        AND (CNTTbl.SHIPCARRIERTRACKINGNUM = '' 
             OR CNTTbl.SHIPCARRIERTRACKINGNUM IS NULL)
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs 
        ON CNTTbl.CONTAINERID = hacs.CARTONID
        AND hacs.TRACKINGNUMBER IS NULL
    LEFT JOIN (
        SELECT 
            vip.WORKID,
            wkuser.USERNAME AS VoicePickUser,
            ROW_NUMBER() OVER (
                PARTITION BY vip.WORKID 
                ORDER BY vip.[STATUS]
            ) AS rn
        FROM HAVOICEINTEGRATIONQUEUEPICKS vip
        INNER JOIN WHSWORKUSER wkuser 
            ON vip.WORKUSER = wkuser.USERID
            AND wkuser.DATAAREAID = 'ha'
        WHERE vip.STATUS < 5
    ) voicepick 
        ON WT.WORKID = voicepick.WORKID 
        AND voicepick.rn = 1
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput 
        ON WT.WORKID = vput.WORKID
    LEFT JOIN WHSWORKUSER wkusr 
        ON vput.WORKUSER = wkusr.USERID
    OUTER APPLY (
        SELECT MIN(CREATEDDATETIME) AS CREATEDDATETIME
        FROM WHSLOADLINE 
        WHERE SHIPMENTID = WT.SHIPMENTID
          AND ORDERNUM = WT.ORDERNUM
          AND LOADID = WT.LOADID
          AND DATAAREAID = 'ha'
    ) ll
    GROUP BY 
        CT.CLUSTERID, 
        CT.CLUSTERPROFILEID, 
        CT.CREATEDBY, 
        CT.CREATEDDATETIME,
        vput.[STATUS]  -- Critical change: group by status
),
WKC AS (
    SELECT 
        CL.CLUSTERID,
        COUNT_BIG(CL.WORKID) AS WkCount
    FROM WHSWORKCLUSTERLINE CL
    INNER JOIN WHSWORKTABLE WT 
        ON CL.WORKID = WT.WORKID
        AND WT.WORKTRANSTYPE = 2
        AND WT.WORKTEMPLATECODE LIKE '4010%'
        AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%'
        AND WT.CREATEDDATETIME > DATEADD(DAY, -@ReportDays, GETDATE())
        AND WT.DATAAREAID = 'ha'
    WHERE CL.DATAAREAID = 'ha'
    GROUP BY CL.CLUSTERID
)
SELECT
    PendCL.CLUSTERID AS [ClusterId],
    PendCL.CREATEDBY AS [CreatedBy],
    FORMAT(PendCL.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 
          'MMM dd, yyyy hh:mmtt') AS [CreatedOn],
    FORMAT(PendCL.ReleasedToWH, 'MMM dd, yyyy') AS [SLA_Date],
    CASE 
        WHEN PutStatus IS NULL THEN 
            CASE WHEN VoicePickUser IS NULL THEN 'N/A' ELSE 'In Progress' END
        ELSE 
            CASE PutStatus
                WHEN 0 THEN 'Pending'
                WHEN 2 THEN 'Completed'
                WHEN 3 THEN 'Error'
                WHEN 4 THEN 'Reset'
                WHEN 5 THEN 'Manually Picked'
                WHEN 6 THEN 'Canceled'
                ELSE 'N/A'
            END
    END AS [VoiceStatus],
    COALESCE(VoicePutUser, VoicePickUser, 'N/A') AS [VoiceUser],
    PendCL.WkPending,
    WKC.WkCount AS [ClusterTotal],
    PendCL.CLUSTERPROFILEID AS [ClusterProfile]
FROM PendCL
INNER JOIN WKC 
    ON PendCL.CLUSTERID = WKC.CLUSTERID
ORDER BY 
    --[SLA_Date], 
    [ClusterId];