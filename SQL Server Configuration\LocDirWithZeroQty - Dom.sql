--I wrote this query to find location directive lines with zero from quantities, and the last time they were modified.
--<PERSON><PERSON>

SELECT t.LocDirName, MAX(l.modifiedDateTime) AS LastModified

  FROM whsLocDirLine l

INNER JOIN whsLocDirTable t

    ON l.dataareaId = t.dataareaId

   AND l.partition = t.partition

   AND l.RefRecId = t.RecId

WHERE l.dataareaId = 'ha'

   AND l.partition = 5637144576

   AND l.fromQty = 0

GROUP BY t.LocDirName

ORDER BY MAX(l.modifiedDateTime) DESC