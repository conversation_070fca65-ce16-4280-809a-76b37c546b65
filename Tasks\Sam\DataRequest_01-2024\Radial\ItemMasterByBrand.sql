SELECT --TOP 100
     CASE WHEN ISNULL( physd.ECORESITEMSIZENAME, '' ) <> '' THEN itbl.ITEMID + '-' + physd.ECORESITEMCOLORNAME + '-' + physd.ECORESITEMSIZENAME
          WHEN ISNULL( physd.ECORESITEMSIZENAME,'' ) <> '' THEN itbl.ITEMID + '-' + physd.ECORESITEMCOLORNAME
          ELSE itbl.ITEMID
     END                                                AS 'SKU'
    --, itbl.NAMEALIAS
    , prodtrans.DESCRIPTION                             AS 'Description'
    , 'ea'                                              AS [UnitOfMeasure]
    , ercdept.Name          AS [Department]
    , ercdiv.Name           AS [Division]
    , ercbrand.NAME         AS [Brand]
    --, hafc.SLOTTIERVALUE
    , CASE WHEN ISNULL(hafc.SLOTTIERVALUE, '') = '' 
        THEN 'N/A' 
        ELSE 
            CASE WHEN len(hafc.SLOTTIERVALUE) = 5  THEN RIGHT(hafc.SLOTTIERVALUE, 1 ) ELSE RIGHT(hafc.SLOTTIERVALUE, 2 ) END
    END    AS [SKU_Velocity]
    , CONVERT( DECIMAL( 10, 2 ), physd.[WEIGHT],    0)  AS 'Weight'
    , CONVERT( DECIMAL( 10, 2 ), physd.[DEPTH],     0 ) AS 'Depth'
    , CONVERT( DECIMAL( 10, 2 ), physd.[WIDTH],     0 ) AS 'Width'
    , CONVERT( DECIMAL( 10, 2 ), physd.[HEIGHT],    0 ) AS 'Height'
    , [UnitOfMeasure] = 'in'
    , [UnitOfWeight] = 'lb'
    --, physd.UOM
   
FROM WHSPHYSDIMUOM physd
    JOIN INVENTTABLE itbl                     ON physd.ITEMID = itbl.ITEMID       AND itbl.DATAAREAID = physd.DATAAREAID              AND itbl.[PARTITION] = physd.[PARTITION]
    --INNER JOIN INVENTDIM idim                     ON itbl.ITEMID  = physd.ITEMID       AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID
    JOIN ECORESPRODUCT             erp         ON itbl.PRODUCT             = erp.RECID
    --LEFT JOIN ECORESCATEGORY            ercclass    ON ercclass.RECID           = erp.HAECORESCATEGORYCLASS -- Class
    JOIN ECORESCATEGORY            ercdept     ON ercdept.RECID            = erp.HAECORESCATEGORYDEPARTMENT -- Department
    JOIN ECORESCATEGORY            ercdiv      ON ercdiv.RECID             = erp.HAECORESCATEGORYDIVISION -- Division
    JOIN ECORESCATEGORY            ercbrand    ON ercbrand.RECID           = erp.HAECORESCATEGORYBRAND -- This join goes to the top category(Brand)
    JOIN ECORESPRODUCTTRANSLATION prodtrans   ON itbl.PRODUCT = prodtrans.PRODUCT AND prodtrans.[PARTITION] = itbl.[PARTITION]
    LEFT JOIN HAFORECASTREPLENISHMENTTABLE hafc ON hafc.ITEM = itbl.ITEMID    AND hafc.COLOR = physd.ECORESITEMCOLORNAME AND hafc.SIZE_ = physd.ECORESITEMSIZENAME
/*
LEFT JOIN HARMSPRODUCT prod ON 
        CASE
            WHEN ISNULL( idim.INVENTSIZEID, '' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
            WHEN ISNULL( idim.INVENTCOLORID,'' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID
            ELSE itbl.ITEMID 
        END = prod.ITEMSKU  
        AND prod.DATAAREAID = isum.DATAAREAID AND prod.[PARTITION] = idim.[PARTITION]
*/
WHERE 
    itbl.ITEMID NOT IN ( '30991', '3333', '9999', '9997' ) 
    AND physd.HEIGHT > 0
    --AND hafc.SLOTTIERVALUE LIKE '%AA'
    --AND itbl.ITEMID LIKE '3%'
/*
GROUP BY 
    idim.INVENTLOCATIONID, itbl.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, prodtrans.DESCRIPTION, idim.[INVENTSTATUSID], idim.[LICENSEPLATEID], idim.[WMSLOCATIONID], isum.PHYSICALINVENT, isum.RESERVPHYSICAL, isum.onorder, wmsloc.LOCPROFILEID, prod.COST
*/
ORDER BY 
    'SKU'
/*
SELECT *
FROM WHSPHYSDIMUOM
*/

--sp_columns inventtable