
WITH TotalWork AS (

SELECT
    CASE WHEN WORKTRANSTYPE = 11 THEN 'Replenishments' ELSE 'Returns' END AS [WorkType]
    , WORKID
    , CREATEDDATETIME
FROM 
     [DAX_Archive].[arc].WHSWORKTABLE
WHERE
    WORKTRANSTYPE IN (11, 12) 
    AND WORKSTATUS = 4
    AND CREATEDDATETIME BETWEEN '01/01/2023 05:00:00 am' AND '01/01/2024 04:59:59 am'
    AND INVENTSITEID = 'HA USA'
    AND INVENTLOCATIONID = '4010'
--GROUP BY WORKTRANSTYPE

UNION

SELECT
    CASE WHEN WORKTRANSTYPE = 11 THEN 'Replenishments' ELSE 'Returns' END AS [WorkType]
    , WORKID
    , CREATEDDATETIME
FROM 
    [DAX_PROD].[dbo].WHSWORKTABLE
WHERE
    WORKTRANSTYPE IN (11, 12) 
    AND WORKSTATUS = 4
    AND CREATEDDATETIME BETWEEN '01/01/2023 05:00:00 am' AND '01/01/2024 04:59:59 am'
    --AND INVENTSITEID = 'HA USA'
    AND INVENTLOCATIONID = '4010'
--GRO<PERSON> BY WORKTRANSTYPE
)
SELECT
    WorkType, COUNT(*) AS Total
FROM
    TotalWork
GROUP BY
    WorkType

/*
SELECT *
/*
    CASE WHEN WORKTRANSTYPE = 11 THEN 'Replenishments' ELSE 'Returns' END AS [WorkType]
    , COUNT (*) AS TOTAL
*/
FROM 
  
    [DAX_Archive].[arc].WHSWORKTABLE
    --[DAX_PROD].[dbo].WHSWORKTABLE

WHERE
    WORKTRANSTYPE IN (11, 12) 
    AND WORKSTATUS = 4
    AND CREATEDDATETIME BETWEEN '06/01/2024 05:00:00 am' AND '06/05/2024 04:59:59 am'
    AND INVENTSITEID = 'HA USA'
    AND INVENTLOCATIONID = '4010'
GROUP BY WORKTRANSTYPE
*/