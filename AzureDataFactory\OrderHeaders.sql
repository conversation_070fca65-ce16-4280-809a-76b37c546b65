

DECLARE @startDateTimePST AS DATETIME = '@{variables('start_date')}', 

    @endDateTimePST AS DATETIME = '@{variables('end_date')}'; 

 

DECLARE @startDateTimeUTC AS DATETIME = @startDateTimePST 

      AT TIME ZONE 'Pacific Standard Time' 

      AT TIME ZONE 'UTC', 

    @endDateTimeUTC AS DATETIME = @endDateTimePST 

      AT TIME ZONE 'Pacific Standard Time' 

      AT TIME ZONE 'UTC'; 

 

WITH SalesTable AS ( 

  SELECT  

    SALESID AS SalesID 

    ,HADWSALESID AS SFCCOrderID 

    ,SALESORIGINID AS SalesOrigin 

    ,C<PERSON><PERSON><PERSON>OUNT AS CustomerAccount 

    ,EMAIL AS OrderEmail 

    ,<PERSON><PERSON>IVER<PERSON>NA<PERSON> AS DeliveryName 

    ,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>LADDRESS AS DeliveryPostalAddressID 

    ,MOD<PERSON>IEDDATETIME AS ModifiedDateTimeUTC 

    ,CREATEDDATETIME AS CreatedDateTimeUTC 

    ,HAORDERDATETIME AS OrderDateTimeUTC 

    ,PARTITION 

    ,DATAAREAID 

    ,CASE WHEN SALESSTATUS = 1 THEN 'BACKORDER' 

       WHEN SALESSTATUS = 2 THEN 'DELIVERED' 

       WHEN SALESSTATUS = 3 THEN 'INVOICED' 

       WHEN SALESSTATUS = 4 THEN 'CANCELLED' 

       ELSE 'NONE' END AS SALESSTATUS 

    ,RECID 

  FROM [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.SALESTABLE WITH (NOLOCK) 

  WHERE MODIFIEDDATETIME >= @startDateTimeUTC  

    AND MODIFIEDDATETIME < @endDateTimeUTC 

    AND SALESORIGINID IN ('WEB','CALLCENTER')), 

SalesTotals AS ( 

  SELECT  

    sl.SALESID 

    ,SUM(sl.salesqty) AS OrderUnits 

    ,SUM(sl.salesqty * sl.HACostAmount) AS OrderCostDlrs 

    ,SUM(sl.salesqty * sl.HAMSRP) AS OrderMSRPDlrs 

    ,SUM(sl.salesqty * sl.salesprice) AS SalesPriceDlrs 

    ,SUM(sl.linedisc) AS OrderDiscountDlrs 

    ,SUM(sl.lineamount) AS OrderSoldDlrs 

    ,SUM((sl.salesqty * sl.HAMSRP) - (sl.salesqty * sl.HACostAmount)) AS OrderInitialMarginDlrs 

    ,SUM((sl.salesqty * sl.salesprice) - (sl.salesqty * sl.HACostAmount)) AS OrderMaintainedMarginDlrs 

  FROM [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.SALESLINE sl WITH (NOLOCK) 

  JOIN SalesTable st ON  

    st.SalesID = sl.SALESID 

    AND st.DATAAREAID = sl.DATAAREAID 

    AND st.PARTITION = sl.PARTITION 

  GROUP BY sl.SALESID), 

ShippingTotals AS ( 

  SELECT 

    st.SalesID 

    ,COALESCE(SUM(mt.VALUE), SUM(mtj.VALUE)) AS ShippingAmount 

  FROM SalesTable st 

  LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.MARKUPTRANS mt WITH (NOLOCK) ON 

    mt.TRANSRECID = st.RECID 

    AND mt.DATAAREAID = st.DATAAREAID 

    AND mt.PARTITION = st.PARTITION 

    --AND mt.MARKUPCODE = 'Shipping' 

    AND mt.TRANSTABLEID = '366' 

  LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.CUSTINVOICEJOUR cij WITH (NOLOCK) ON 

    cij.SALESID = st.SalesID 

    AND cij.DATAAREAID = st.DATAAREAID 

    AND cij.PARTITION = st.PARTITION 

  LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.MARKUPTRANS mtj WITH (NOLOCK) ON 

    mtj.TRANSRECID = cij.RECID 

    AND mtj.DATAAREAID = st.DATAAREAID 

    AND mtj.PARTITION = st.PARTITION 

    --AND mtj.MARKUPCODE = 'Shipping' 

    AND mtj.TRANSTABLEID = '62' 

  GROUP BY st.SalesID 

  ) 

SELECT  

  stbl.SalesID 

  ,stbl.SFCCOrderID 

  ,stbl.RECID AS AXHeaderRecID 

  ,stbl.SalesOrigin 

  ,stbl.CustomerAccount 

  ,stbl.OrderEmail 

  ,stbl.DeliveryName 

  ,stbl.SalesStatus 

  ,dpav.ADDRESS AS DeliveryStreet 

  ,dpav.CITY AS DeliveryCity 

  ,dpav.STATE AS DeliveryState 

  ,dpav.ZIPCODE AS DeliveryZip 

  ,dpav.COUNTRYREGIONID AS DeliveryCountry 

  ,shp.ShippingAmount 

  ,stls.OrderUnits 

  ,stls.OrderCostDlrs 

  ,stls.OrderMSRPDlrs 

  ,stls.SalesPriceDlrs 

  ,stls.OrderDiscountDlrs 

  ,stls.OrderSoldDlrs 

  ,stls.OrderInitialMarginDlrs 

  ,stls.OrderMaintainedMarginDlrs 

  ,stbl.OrderDateTimeUTC 

  ,stbl.ModifiedDateTimeUTC 

  ,stbl.CreatedDateTimeUTC 

FROM SalesTable stbl  

LEFT JOIN SalesTotals stls ON 

  stls.SALESID = stbl.SalesID 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.DIRPARTYPOSTALADDRESSVIEW dpav WITH (NOLOCK) ON 

  dpav.POSTALADDRESS = stbl.DeliveryPostalAddressID 

LEFT JOIN ShippingTotals shp ON 

  shp.SalesID = stbl.SalesID 