
-- Days to report back

<PERSON><PERSON><PERSON><PERSON> @ReportDays AS INT = 30

SELECT
    --TOP 20 sl.*
    sl.SALESID                              AS [Order]
    , sl.ITEMID                             AS [Item]
    , idim.INVENTCOLORID                    AS [Color]
    , idim.INVENTSIZEID                     AS [Size]
    , sl.NAME                               AS [Name]
    , CAST(sl.QTYORDERED AS INT)            AS [Qty]
    , CAST(sl.SALESPRICE AS DECIMAL(18, 2)) AS [Price]
    , CAST(sl.LINEAMOUNT AS DECIMAL(18, 2)) AS [LineAmount]
    , st.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'    AS [CreatedDateTime]
FROM
    SALESLINE sl
    INNER JOIN SALESTABLE st ON sl.SALESID = st.SALESID AND sl.DATAAREAID = st.DATAAREAID AND sl.[PARTITION] = st.[PARTITION]
    INNER JOIN inventdim IDIM on IDIM.INVENTDIMID = SL.INVENTDIMID and IDIM.DATAAREAID = 'ha' AND idim.[PARTITION] = 5637144576
WHERE
    --st.SALESTYPE = 4 -- Returned orders
    st.SALESTYPE = 3 -- Returned orders
    AND st.CREATEDDATETIME > GETUTCDATE() - @ReportDays
    AND sl.SALESSTATUS = 1 -- Open orders
    AND st.MCRORDERSTOPPED = 0 -- Not On Hold
    AND sl.CUSTGROUP = 'RETAIL'
    AND st.INVENTLOCATIONID = '4010'
    AND RIGHT(CAST(sl.SALESPRICE AS DECIMAL(18,2)), 2) = '79'  --filter to Final Sale pricing

    --sp_columns salesline