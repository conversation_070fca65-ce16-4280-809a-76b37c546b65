--LastSOPerSKU
-- This query is used to get the last sales order transaction for each SKU in the Picking locations.
-- It retrieves the last sales order transaction date for each SKU in the Picking locations.
-- It's very slow. Use with caution.

DECLARE @ItemId NVARCHAR(20) = '48552';
--DECLARE @ColorId NVARCHAR(10) = 'M23';
--DECLARE @SizeId NVARCHAR(10) = 'S';

with OH AS (
SELECT
    isum.itemid             AS Item
    , idim.inventcolorid    AS Color
    , idim.inventsizeid     AS Size
    --, idim.WMSLOCATIONID
    --, CAST(SUM(isum.physicalinvent) AS INT) AS Qty
    --, CAST(SUM(isum.reservphysical) AS INT) AS Reserved
FROM 
    inventsum isum
JOIN inventdim idim ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = idim.DATAAREAID AND isum.PARTITION = idim.[PARTITION]
JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = idim.WMSLOCATIONID AND loc.DATAAREAID = idim.DATAAREAID AND loc.[PARTITION] = idim.[PARTITION]
WHERE 
    isum.physicalinvent > 0 AND idim.inventlocationid = '4010'
    --AND idim.INVENTSITEID = 'HA USA' 
    AND loc.LOCPROFILEID IN ('W001', 'Picking', 'Picking A', 'Picking D', 'PalletPicking')
    AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997')
    --AND isum.RESERVPHYSICAL > 0
GROUP BY
    isum.itemid, idim.inventcolorid, idim.inventsizeid
)
, TransPerSKU AS (
SELECT
    --TOP 1
        --itorigin.REFERENCEID AS ReferenceId, 
        --CAST(MAX(itrans.MODIFIEDDATETIME) AS DATE) AS LastSOTransaction
        itrans.ITEMID
         , idim.INVENTCOLORID
         , idim.INVENTSIZEID
        --, itrans.STATUSISSUE
        , itrans.INVENTDIMID    AS[Itrans_DimId]
        --, isum.INVENTDIMID      AS [Isum_DimId]
        , idim.INVENTDIMID      AS [Idim_DimId]
        --, itrans.INVOICEID
        , itrans.VOUCHER
        --, itrans.VOUCHERPHYSICAL
        , CAST(itrans.QTY AS DECIMAL(20,0)) AS QTY
        , itorigin.REFERENCECATEGORY
        ,CASE 
          WHEN itorigin.REFERENCECATEGORY = 0 THEN 'Sales Order'
          WHEN itorigin.REFERENCECATEGORY = 2 THEN 'Production Order'
          WHEN itorigin.REFERENCECATEGORY = 3 THEN 'Purchase Order'
          WHEN itorigin.REFERENCECATEGORY = 4 THEN 'Inventory Transaction'
          WHEN itorigin.REFERENCECATEGORY = 5 THEN 'Inventory Adjustment'
          WHEN itorigin.REFERENCECATEGORY = 6 THEN 'Transfer'
          WHEN itorigin.REFERENCECATEGORY = 13 THEN 'Inventory Counting'
          WHEN itorigin.REFERENCECATEGORY = 15 THEN 'Quarantine Order'
          WHEN itorigin.REFERENCECATEGORY = 21 THEN 'Transfer order shipment'
          WHEN itorigin.REFERENCECATEGORY = 22 THEN 'Transfer order receive'
          WHEN itorigin.REFERENCECATEGORY = 201 THEN 'Work'
          WHEN itorigin.REFERENCECATEGORY = 202 THEN 'Quarantine'
          ELSE 'Unknown'
     END AS [ReferenceCategoryDescription]
        , itorigin.REFERENCEID
        , itorigin.INVENTTRANSID
        , idim.WMSLOCATIONID
        , idim.LICENSEPLATEID
        , idim.INVENTSTATUSID
        , idim.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS Idim_ModDT
        , itrans.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS Itrans_ModDT
        --, isum.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS Isum_ModDT
        --, MAX(itrans.RECID) AS LastTransactionRecId
        --, itrans.RECID AS LastTransactionRecId
    FROM INVENTTRANS itrans 
    LEFT JOIN INVENTTRANSORIGIN itorigin  WITH (NOLOCK)  ON itrans.INVENTTRANSORIGIN   = itorigin.RECID              
        AND itorigin.[PARTITION] = itrans.[PARTITION]     AND itorigin.DATAAREAID  = itrans.DATAAREAID
    JOIN inventdim idim WITH (NOLOCK) ON idim.INVENTDIMID = itrans.INVENTDIMID 
        AND idim.DATAAREAID = itrans.DATAAREAID AND idim.[PARTITION] = itrans.[PARTITION]
    --JOIN inventsum isum WITH (NOLOCK) ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION]
    --JOIN OH ON OH.Item = itrans.ITEMID AND OH.Color = idim.INVENTCOLORID AND OH.Size = idim.INVENTSIZEID
    WHERE 
        itrans.ITEMID = '82573'
        --AND idim.INVENTCOLORID = 'M23'
        --AND idim.INVENTSIZEID = 'S'
        AND itrans.DATAAREAID = 'ha'
        AND itrans.[PARTITION] = 5637144576
        --AND idim.[PARTITION]
        AND idim.INVENTLOCATIONID = '4010'
        AND itrans.STATUSISSUE = 1 -- Sold
        --AND itorigin.REFERENCECATEGORY = 0 -- Sales Order 
    /*GROUP BY 
          itrans.ITEMID
         , idim.INVENTCOLORID
         , idim.INVENTSIZEID
         , itrans.MODIFIEDDATETIME*/
    ORDER BY itrans.MODIFIEDDATETIME DESC
)
SELECT 
    OH.Item + '-' + OH.Color + '-' + OH.Size AS SKU,
    --OH.Qty      AS TotalQty,
    --OH.Reserved AS TotalReserved,
    MAX(Itrans_ModDT) AS LastTransaction
FROM LastSOPerSKU, OH
GROUP BY OH.Item,OH.Color, OH.Size--, OH.Qty, OH.Reserved 
--ORDER BY LastSOTransaction
--SELECT * FROM LastSOPerSKU