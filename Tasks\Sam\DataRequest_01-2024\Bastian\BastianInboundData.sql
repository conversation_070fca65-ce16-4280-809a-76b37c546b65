-- Inbound Data
-- <PERSON>'s request 11/3/2023

SELECT
    wktbl.ORDERNUM                                                                          AS [PO_Number]
    , purchln.LINENUMBER                                                                    AS [PO_Line]
    --, wktbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'         AS [KY_DeliveredDateTime]
    , MIN(wktbl.WORKCLOSEDUTCDATETIME) AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'   AS [KY_ProcessDateTime]
    --, CAST(MIN(purchln.DELIVERYDATE) AS DATE)                                                    AS [DeliveryDate]
    --, wktbl.WORKID
    , wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID                      AS [SKU]
    --, purchln.DELIVERYDATE
    , prodtrans.DESCRIPTION                                                                 AS 'Description'
    --, SUBSTRING(purchln.NAME, 1, LEN(LTRIM(RTRIM(purchln.NAME)))/ 2  )                          AS [ItemDescription]
    
    
    , CAST(purchln.PURCHQTY AS INT)                                                         AS [ReceiptQty]
    --, CAST(wkln.QTYWORK AS INT)                                                             AS [WorkQty]
    --, wktbl.WORKID                                                                          AS [WorkIdentifier]
    , purchln.PURCHUNIT                                                                     AS [QtyUOM]
    , purchtbl.ORDERACCOUNT                                                                 AS [Vendor]
FROM
    [DAX_PROD].[dbo].PURCHLINE purchln 
    JOIN [DAX_Archive].[arc].WHSWORKTABLE wktbl ON purchln.PURCHID = wktbl.ORDERNUM AND wktbl.DATAAREAID = purchln.DATAAREAID AND wktbl.[PARTITION] = purchln.[PARTITION] 
    JOIN [DAX_Archive].[arc].WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wkln.LINENUM = 1 
        AND wkln.ITEMID = purchln.ITEMID AND wkln.INVENTDIMID = purchln.INVENTDIMID
    JOIN [DAX_Archive].[arc].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
    JOIN [DAX_PROD].[dbo].PURCHTABLE purchtbl ON wktbl.ORDERNUM = purchtbl.PURCHID AND wktbl.DATAAREAID = purchtbl.DATAAREAID AND wktbl.[PARTITION] = purchtbl.[PARTITION] 
    INNER JOIN [DAX_PROD].[dbo].INVENTTABLE itbl ON purchln.ITEMID = itbl.ITEMID AND purchln.DATAAREAID = itbl.DATAAREAID AND purchln.[PARTITION] = itbl.[PARTITION]
    INNER JOIN [DAX_PROD].[dbo].ECORESPRODUCTTRANSLATION prodtrans ON itbl.PRODUCT = prodtrans.PRODUCT AND prodtrans.[PARTITION] = itbl.[PARTITION]
WHERE
    wktbl.WORKTRANSTYPE = 1 -- Purchase orders
    AND wktbl.WORKSTATUS = 4 -- Closed
    AND wktbl.INVENTSITEID = 'HA USA'
    AND wktbl.INVENTLOCATIONID = '4010'
    AND wktbl.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 05:00:00 AM'
GROUP BY 
     wktbl.ORDERNUM, purchln.LINENUMBER, wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, prodtrans.DESCRIPTION, CAST(purchln.PURCHQTY AS INT), purchln.PURCHUNIT, purchtbl.ORDERACCOUNT
ORDER BY
    PO_Number, PO_Line

--SELECT Top 5 * FROM PURCHLINE