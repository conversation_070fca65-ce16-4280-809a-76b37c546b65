-- Inventory data for <PERSON>'s request

USE DAX_PROD

SELECT 
    --GETUTCDATE(),                                                   AS [UTC_Date]
     idim.inventlocationid                                         AS [Warehouse]
    , loc.ZONEID                                                    AS [ZoneId]
    , SUBSTRING(loc.WMSLOCATIONID, 1, CHARINDEX('-', 
        loc.WMSLOCATIONID) - 1 )                                    AS [Aisle]
    , CASE WHEN ISNULL(loc.RACK, '') = '' OR loc.RACK = 0 
        THEN CAST(SUBSTRING(loc.WMSLOCATIONID, 
            CHARINDEX('-', loc.WMSLOCATIONID) + 1, 3 ) AS INT)  
        ELSE loc.RACK 
      END 
                                                                    AS [BayNumber]
    , RIGHT(loc.WMSLOCATIONID, 1 )                                  AS [Shelf]
    , idim.wmslocationid                                            AS [LocationName]
    , loc.LOCPROFILEID                                              AS [LocProfileId]  
    , locp.LOCTYPE                                                  AS [LocType]             
    , CASE 
        WHEN isnull(idim.inventcolorid, '') = '' THEN isum.itemid
        WHEN isnull(idim.inventsizeid, '') = '' THEN isum.itemid 
            + '-' + idim.inventcolorid
        ELSE isum.itemid + '-' + idim.inventcolorid + '-' 
        + idim.inventsizeid    
    END                                                             AS [SKU]
    , CONVERT( DECIMAL( 20, 0), isum.physicalinvent )               AS [Qty]
    , 'ea'                                                          AS [QtyFormFactor]
    , CAST(locp.DEPTH AS DECIMAL(10, 2) )                           AS [LocDepth]
    , CASE WHEN loc.ZONEID IN ('Bulk52', 'Bulk100') 
        THEN CAST(locp.WIDTH / 2 AS DECIMAL(10, 2) )    
        ELSE CAST(locp.WIDTH AS DECIMAL(10, 2) )   
    END                                                             AS [LocWidth]
    , CAST(locp.HEIGHT AS DECIMAL(10, 2) )                          AS [LocHeight]
    --, itrans.*
FROM inventsum isum
JOIN inventdim idim             ON isum.inventdimid = idim.inventdimid      AND isum.DATAAREAID = idim.DATAAREAID   AND isum.[PARTITION] = idim.[PARTITION] AND isum.physicalinvent > 0 
JOIN WMSLOCATION loc            ON loc.WMSLOCATIONID = idim.WMSLOCATIONID   AND loc.DATAAREAID  = idim.DATAAREAID   AND idim.[PARTITION] = loc.[PARTITION]
JOIN WHSLOCATIONPROFILE locp    ON locp.LOCPROFILEID = loc.LOCPROFILEID     AND locp.DATAAREAID = loc.DATAAREAID    AND locp.PARTITION = loc.PARTITION
--JOIN INVENTTRANS itrans ON itrans.INVENTDIMID = idim.INVENTDIMID AND itrans.DATAAREAID = idim.DATAAREAID AND itrans.[PARTITION] = idim.[PARTITION] --AND isum.ITEMID = itrans.ITEMID
WHERE 
    idim.inventlocationid = '4010'
    AND idim.INVENTSITEID = 'HA USA'
    AND loc.inventlocationid = '4010' 
    --AND (loc.locprofileid IN ('Invalid', 'Invalid_LP') OR (loc.ZONEID = 'Invalid' AND loc.locprofileid <> 'User')) AND isum.physicalinvent < 10000
    AND loc.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', 'Bulk' /*'W001','Offsite'*/) 
    AND loc.zoneid NOT IN ( 'Current Pick')
    --AND isum.ITEMID = '80124'
    --AND idim.INVENTDIMID = 0438431735
ORDER BY loc.WMSLOCATIONID

--sp_columns inventdim
--sp_columns wmslocation
--sp_columns whslocationprofile

--SELECT * FROM WMSLOCATIOn WHERE wmslocationid LIKE '12-058%' ORDER BY LEVEL_
--SELECT * FROM WHSLOCATIONPROFILE WHERE locprofileid LIKE 'Picking A'

/*
-- Getting an inventory snapshot from 2023
SELECT 
    CAST(ASOFDATE AS DATE)          AS [SnapShotDate]
    , SKU                           AS [SKU]
    , CAST(AVAILPHYSICAL AS INT)    AS [InventQty]
FROM [DAX_PROD].[dbo].[HAINVENTDETAILREPORTBATCHTMPHISTORY]
WHERE
    INVENTSITEID = 'HA USA'
    AND INVENTLOCATIONID = '4010'
    AND AVAILPHYSICAL BETWEEN 1 AND 1000000 
    AND CAST(ASOFDATE AS DATE) IN (SELECT FORMAT(EOMONTH(DATEFROMPARTS(2023, n, 1)), 'MM/dd/yyyy', 'en-US') AS 'LastDay'
                                    FROM (
                                        SELECT 1 AS n UNION ALL
                                        SELECT 2 UNION ALL
                                        SELECT 3 UNION ALL
                                        SELECT 4 UNION ALL
                                        SELECT 5 UNION ALL
                                        SELECT 6 UNION ALL
                                        SELECT 7 UNION ALL
                                        SELECT 8 UNION ALL
                                        SELECT 9 UNION ALL
                                        SELECT 10 UNION ALL
                                        SELECT 11 UNION ALL
                                        SELECT 12
                                    ) AS months
                                    )
ORDER BY SnapShotDate, SKU
*/
--('1/31/2023', '2/28/2023', '3/31/2023', '4/30/2023';

