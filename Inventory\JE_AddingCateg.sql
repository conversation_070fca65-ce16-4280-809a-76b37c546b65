
/*
join inventtable it ON vt.itemid=it.itemid
join ecoresproduct ep ON it.itemid=ep.displayproductnumber
join ecoresproductcategory epc ON epc.product=it.product
join ECORESCATEGORYHIERARCHY ech ON epc.CATEGORYHIERARCHY=ech.recid
join ecorescategory low1 ON epc.CATEGORY = low1.recid
join ecorescategory class ON low1.PARENTCATEGORY=class.recid
join ECORESCATEGORY dept ON class.PARENTCATEGORY=dept.recid
join ECORESCATEGORY div ON dept.PARENTCATEGORY=div.recid
*/

SELECT 
    ITEMID
    , NAMEALIAS
    , ITEMBUYERGROUPID
    , PRIMARYVENDORID
    , ORIGCOUNTRYREGIONID
    , HAMSRP
    , HASIZERANGE
    , HAPRODUCTCLASS
FROM INVENTTABLE invtbl
JOIN ECORESPRODUCT ecorp            ON ecorp.DISPLAYPRODUCTNUMBER = invtbl.ITEMID AND invtbl.[PARTITION] = invtbl.[PARTITION]
JOIN ECORESPRODUCTCATEGORY epc      ON epc.product = invtbl.product
JOIN ECORESCATEGORYHIERARCHY ech    ON epc.CATEGORYHIERARCHY = ech.recid
JOIN ECORESCATEGORY low1            ON epc.CATEGORY = low1.recid
JOIN ECORESCATEGORY class           ON low1.PARENTCATEGORY=class.recid
JOIN ECORESCATEGORY dept            ON class.PARENTCATEGORY=dept.recid
JOIN ECORESCATEGORY div             ON dept.PARENTCATEGORY=div.recid
WHERE ITEMID = '44403'

SELECT TOP 20 * 
FROM ECORESPRODUCT ep
JOIN ECORESCATEGORY ecg ON ep.HAECORESCATEGORYBRAND = ecg.RECID
WHERE
    ep.DISPLAYPRODUCTNUMBER = '44403'

SELECT TOP 10 * FROM VENDTABLE
WHERE
    INVENTSITEID = 'HA USA'
    AND INVENTLOCATION = '4010'
    AND ACCOUNTNUM = '011038'