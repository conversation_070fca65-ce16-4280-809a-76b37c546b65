

-- Power BI report from <PERSON>

/*Closed Containers 7pm-7am*/
SELECT  WHSContainerTable.ContainerID,
		--WHSContainerTable.CONTAINERTYPECODE,
		--WHSCONTAINERTABLE.MODIFIEDDATETIME AS UTC_ContainerClosedDATETIME, ---UTC TIME container got closed
		WHSSHIPMENTTABLE.ordernum,
--WHSCONTAINERTABLE.MASTERTRACKINGNUM,
--WHSCONTAINERTABLE.SHIPCARRIERTRACKINGNUM,
		Sum (WHSCONTAINERLINE.Qty) AS Qty,

	CASE				---this is for modified date time of the day before. therefore dont need a lot of convertions from past or too much into the future
		/*2023*/
		WHEN DATEPART(YEAR, <PERSON><PERSON><PERSON><PERSON>AINERTABLE.MODIFIEDDATETIME) = 2023 AND WHSCONTAINERTABLE.MODIFIEDDATETIME BETWEEN '2023-03-12 02:00:00.000' AND '2023-11-05 02:00:00.000' THEN DATEADD(hh, -4, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ERTABL<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) 
		WHEN DATEPART(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>BL<PERSON>.<PERSON>ODIFIEDDATETIME) = 2023 AND WHSCONTAINERTABLE.MODIFIEDDATETIME NOT BETWEEN '2023-03-12 02:00:00.000' AND '2023-11-05 02:00:00.000' THEN DATEADD(hh, -5, WHSCONTAINERTABLE.MODIFIEDDATETIME) 
		/*2022*/
		WHEN DATEPART(YEAR, WHSCONTAINERTABLE.MODIFIEDDATETIME) = 2022 AND WHSCONTAINERTABLE.MODIFIEDDATETIME BETWEEN '2022-03-13 02:00:00.000' AND '2022-11-06 02:00:00.000' THEN DATEADD(hh, -4, WHSCONTAINERTABLE.MODIFIEDDATETIME) 
		WHEN DATEPART(YEAR, WHSCONTAINERTABLE.MODIFIEDDATETIME) = 2022 AND WHSCONTAINERTABLE.MODIFIEDDATETIME NOT BETWEEN '2022-03-13 02:00:00.000' AND '2022-11-06 02:00:00.000' THEN DATEADD(hh, -5, WHSCONTAINERTABLE.MODIFIEDDATETIME) 
		/*2021*/
		WHEN DATEPART(YEAR, WHSCONTAINERTABLE.MODIFIEDDATETIME) = 2021 AND WHSCONTAINERTABLE.MODIFIEDDATETIME BETWEEN '2021-03-14 02:00:00.000' AND '2021-11-07 02:00:00.000' THEN DATEADD(hh, -4, WHSCONTAINERTABLE.MODIFIEDDATETIME) 
		WHEN DATEPART(YEAR, WHSCONTAINERTABLE.MODIFIEDDATETIME) = 2021 AND WHSCONTAINERTABLE.MODIFIEDDATETIME NOT BETWEEN '2021-03-14 02:00:00.000' AND '2021-11-07 02:00:00.000' THEN DATEADD(hh, -5, WHSCONTAINERTABLE.MODIFIEDDATETIME) 
	END AS EST_ContModifiedDate,
/*
CASE	-----------------This gives me the current time in EST(good time-eastern) THE QUERY RUN
			
			WHEN DATEPART(YEAR, GETDATE()) = 2022 AND GETDATE() BETWEEN '2022-03-13 02:00:00.000' AND '2022-11-06 02:00:00.000' THEN DATEADD(hh, +3, GETDATE()) 
			WHEN DATEPART(YEAR, GETDATE()) = 2022 AND GETDATE() NOT BETWEEN '2022-03-13 02:00:00.000' AND '2022-11-06 02:00:00.000' THEN DATEADD(hh, +3, GETDATE()) 
			WHEN DATEPART(YEAR, GETDATE()) = 2021 AND GETDATE() BETWEEN '2021-03-14 02:00:00.000' AND '2021-11-07 02:00:00.000' THEN DATEADD(hh, +3, GETDATE()) 
			WHEN DATEPART(YEAR, GETDATE()) = 2021 AND GETDATE() NOT BETWEEN '2021-03-14 02:00:00.000' AND '2021-11-07 02:00:00.000' THEN DATEADD(hh, +3, GETDATE()) 
	END AS EST_TODAYCURRENTTIME
*/
DATEADD(hh, +3, GETDATE() )  AS EST_TODAYCURRENTTIME -- Will work while the serever stays in Pacific time
INTO #TEMP1
FROM WHSCONTAINERTABLE          
LEFT JOIN WHSSHIPMENTTABLE ON WHSCONTAINERTABLE.SHIPMENTID = WHSSHIPMENTTABLE.SHIPMENTID------THIS IS FOR SHIPMENT STATUS = 5 AKA---SHIPPED CONTAINER
LEFT JOIN WHSCONTAINERLINE ON WHSCONTAINERTABLE.CONTAINERID = WHSCONTAINERLINE.CONTAINERID----THIS IS TO GET QTY IN A CONTAINER
WHERE WHSCONTAINERTABLE.MODIFIEDDATETIME > getdate() - 2 AND SHIPCARRIERTRACKINGNUM NOT LIKE '' --AND WHSSHIPMENTTABLE.SHIPMENTSTATUS = 5
GROUP BY WHSCONTAINERTABLE.CONTAINERID,  WHSSHIPMENTTABLE.ORDERNUM--,WHSCONTAINERTABLE.MODIFIEDDATETIME, WHSContainerTable.CONTAINERTYPECODE, WHSCONTAINERTABLE.MASTERTRACKINGNUM, WHSCONTAINERTABLE.SHIPCARRIERTRACKINGNUM

SELECT * ,
    CONVERT(VARCHAR(20), EST_ContModifiedDate, 100) AS 'AM/PM'      -- Just the date
    , DATEPART(HOUR, EST_ContModifiedDate) AS CloseContainerHOUR    -- Hour
    , CONVERT(VARCHAR(20), EST_ContModifiedDate, 111) AS CLOSEDCONTAINERDATE		/*this will be used to compare if the close date is today or not*/
    , CONVERT(VARCHAR(20), EST_TODAYCURRENTTIME, 111) AS ESTCURRENTDATE 			/*this will be used to compare if the close date is today or not*/
    , DATEPART(HOUR, EST_TODAYCURRENTTIME) AS EST_CURRENTHOUR  -- Current KY hour 
INTO 
    #TEMP2
FROM 
    #TEMP1
WHERE 
    EST_ContModifiedDate > getdate() - 2  --tHIS IS GETTING THE LAST TWO DAYS IN EASTERN TIME

/*This section will gather the data from the last 2 days*/

SELECT *,
CASE 
	WHEN CLOSEDCONTAINERDATE = ESTCURRENTDATE THEN 0
	WHEN CLOSEDCONTAINERDATE < ESTCURRENTDATE THEN 1
END AS TODAY0orYesterday1,
CASE 
	WHEN CloseContainerHOUR BETWEEN 6 AND 15 THEN 1 ELSE 2
	--WHEN CloseContainerHOUR NOT BETWEEN 4 AND 15 THEN 2
END AS SHIFT
FROM #TEMP2
WHERE DATEDIFF( DAY, ESTCURRENTDATE, CLOSEDCONTAINERDATE) != -2  -- Only one day difference
ORDER BY CLOSEDCONTAINERDATE

--SELECT GETDATE(), GETUTCDATE()