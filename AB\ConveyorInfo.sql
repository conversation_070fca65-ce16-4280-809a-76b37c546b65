SELECT TOP (1000) [cntnr_name]
      ,[cntnr_type]
      ,[<PERSON>anner_num]
      ,[Sort_code]
      ,[destination]
      ,[fail_reason]
      ,[cntnr_detail_1]
      ,[cntnr_detail_2]
      ,[cntnr_detail_3]
      ,[cntnr_detail_4]
      ,[cntnr_detail_5]
      ,[status]
      ,[date_stamp]
FROM [Conveyor].[dbo].[WMS_EXA_IMP_DIV_CNFRM]
WHERE
    date_stamp > '12/05/2023'
    AND cntnr_name IN ('CN015434021', 'CN015433363', 'CN015431515')
    --AND destination = 2
ORDER BY
    cntnr_name