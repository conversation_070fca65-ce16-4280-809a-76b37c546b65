/*
Updated on 11/16/2023 to count for Manual Moves
Transforming to most units sold on 10/18/2024
*/
USE DAX_PROD

DECLARE @ReportDays AS INT = 90
DECLARE @MinSalesQty AS INT = @ReportDays * 3 -- using this criteria, 10/25/2024

SELECT TOP 500
    SKUSales.SKU
    , SKUSales.Description
    , SKUSales.Qty + OH_Resv.Reserve  AS [UnitsSold]
FROM
(
SELECT 
    wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
    , prodtrans.DESCRIPTION AS 'Description'
    --, wktbl.WORKID
    , CONVERT( DECIMAL( 10, 0 ), SUM( wkln.INVENTQTYWORK ) )     AS 'Qty'
 FROM
    WHSWORKTABLE wktbl
    INNER JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    INNER JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
    INNER JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
    INNER JOIN INVENTTABLE itbl                     ON wkln.ITEMID      = itbl.ITEMID               AND itbl.DATAAREAID = wkln.DATAAREAID AND itbl.[PARTITION] = wkln.[PARTITION]
    INNER JOIN ECORESPRODUCTTRANSLATION prodtrans   ON itbl.PRODUCT = prodtrans.PRODUCT             AND prodtrans.[PARTITION] = itbl.[PARTITION]
WHERE
    wktbl.CREATEDDATETIME > GETUTCDATE() - @ReportDays
    AND wktbl.WORKTRANSTYPE = 2  -- Sales Order
    AND wkln.WORKTYPE = 1 -- Pick. To get only one line
    AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
    AND loc.locprofileid LIKE '%Picking%' -- Only Picking locations
    AND wktbl.WORKSTATUS < 5 -- Not cancelled
GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, prodtrans.DESCRIPTION
HAVING COUNT( * ) > @MinSalesQty 
) AS SKUSales
INNER JOIN
(
SELECT 
    isum.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID     AS 'SKU'
    , CAST(isum.RESERVPHYSICAL AS INT)   AS 'Reserve'
FROM
    INVENTSUM isum
    JOIN INVENTDIM idim ON idim.INVENTDIMID = isum.INVENTDIMID AND idim.DATAAREAID = 'ha' AND isum.PARTITION = idim.PARTITION
WHERE
    1 = 1
    --isum.ITEMID = '81024'
    --AND idim.INVENTCOLORID = '541'
    --AND idim.INVENTSIZEID = '110'
    AND idim.INVENTSTATUSID = 'Available'
    AND (isum.PHYSICALINVENT = 0.00 AND isum.RESERVPHYSICAL > 0 AND isnull(idim.WMSLOCATIONID, '') = '') -- Total number of reservations
) AS OH_Resv
ON SKUSales.SKU = OH_Resv.SKU
WHERE 
    1 = 1
    --AND SKUSales.SKU LIKE '81259-75Z%'
ORDER BY SKUSales.Qty DESC

--sp_columns inventsum
/*
SELECT *
FROM 
    WHSWORKLINE
WHERE 
    WORKID = 'WK0025337844'

SELECT 
    sl.SALESID              AS OrderNumber
    , sl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID AS SKU
    , CONVERT( DECIMAL( 10, 0), SUM( sl.QTYORDERED ) ) AS Qty
FROM 
    salesline sl
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = sl.INVENTDIMID AND idim.[PARTITION] = sl.[PARTITION] AND sl.DATAAREAID = 'ha'
WHERE
    sl.SALESSTATUS = 3 -- Invoiced
    AND sl.SALESTYPE = 3  -- Sales Order
    AND sl.CUSTGROUP NOT LIKE 'AMAZON'
    AND sl.MODIFIEDDATETIME > GETUTCDATE() - 5
    --AND sl.SALESID = '42248988'
GROUP BY sl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, sl.SALESID
--HAVING COUNT( * ) > 10 -- More than 10 pieces sold


SELECT *
FROM
    WHSWORKTABLE wktbl
WHERE 
    wktbl.ORDERNUM = '43558356'


*/

--sp_columns inventsum