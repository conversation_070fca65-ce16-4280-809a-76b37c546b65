-- Open orders report

USE DAX_PROD

SELECT 

  -- Some fields are not being used on this report( 4/6/2022 )
     st.salesid                                AS OrderNum                                          
    , st.dlvmode                              AS DeliveryMode
    , sl.itemid                               AS Item
    , id.INVENTCOLORID                        AS Color
    , id.INVENTSIZEID                         AS Size
    , CONVERT( DECIMAL, sl.qtyordered, 10 )   AS Qty
    , st.MCROrderStopped                      AS Hold
    , wll.SHIPMENTID                          AS Shipment
    , wll.CREATEDDATETIME                     AS ReleasedToWHDate -- UTC Time. Glitch on the original query
    , wll.LOADID                              AS 'Load'
    , CASE WHEN ISNULL(wktbl.WORKID, '' ) <> '' THEN 
        CASE WHEN wktbl.FROZEN = 1 THEN 'Yes' ELSE 'No' END 
       ELSE
          'NoWork' END AS WkBlocked
    --, wlt.LOADSTATUS                          AS LOADSTATUS
    ,	
    CASE 
		  WHEN shptbl.SHIPMENTSTATUS IS NULL THEN 'Not released'
		  WHEN shptbl.SHIPMENTSTATUS = 0 THEN 	'Open'
		  WHEN shptbl.SHIPMENTSTATUS = 1 THEN 	'Waved'
		  WHEN shptbl.SHIPMENTSTATUS = 2 THEN 	'In process'
		  WHEN shptbl.SHIPMENTSTATUS = 3 THEN 	'In packing'
		  WHEN shptbl.SHIPMENTSTATUS = 4 THEN 	'Loaded'
		  WHEN shptbl.SHIPMENTSTATUS = 5 THEN 	'Shipped'
		  ELSE 									'Received'
	  END AS LDStat -- Same name, but shipment status

-- Not using the load status because put together Open and Not released.
/*
(
	CASE WHEN wlt.LOADSTATUS = 0 THEN 'Open' 
		WHEN wlt.LOADSTATUS = 1 THEN 'Posted' 
    	WHEN wlt.LOADSTATUS = 2 THEN 'Waved' 
		WHEN wlt.LOADSTATUS = 3 THEN 'In process'
		WHEN wlt.LOADSTATUS = 4 THEN CASE WHEN ISNULL( wll.LOADID, '' ) = '' THEN 'Open' ELSE 'In packing' END
		WHEN wlt.LOADSTATUS = 5 THEN 'Loaded'
		WHEN wlt.LOADSTATUS = 6 THEN 'Shipped'
	ELSE 'Not released'  END	) AS LDStat
*/
  , wavt.WAVEID                               AS Wave
  , wavt.WAVETEMPLATENAME                     AS WaveTemplate
  , cnln.CONTAINERID                         AS Container
  , wktbl.WORKID
  , wkcl.CLUSTERID                            AS Cluster
  , wktbl.WORKTEMPLATECODE                    AS WorkTemplate
FROM SALESTABLE st WITH( NOLOCK ) 
INNER JOIN SALESLINE sl  WITH( NOLOCK )    
  ON st.salesid = sl.salesid AND st.dataareaid = sl.dataareaid AND st.Partition = sl.Partition 
  AND sl.salesstatus = 1 -- only open sales lines, 2 - delivered, 3 - invoiced, 4 - canceled
  AND sl.qtyordered > 0 -- Not empty
  AND sl.custgroup not like 'W%' -- Not Wholesale

INNER JOIN inventdim id WITH( NOLOCK ) ON  sl.inventdimid = id.inventdimid AND sl.dataareaid = id.dataareaid AND sl.Partition = id.Partition

LEFT JOIN WHSLOADLINE wll WITH( NOLOCK ) ON sl.INVENTTRANSID = wll.INVENTTRANSID AND sl.DATAAREAID = wll.DATAAREAID  AND sl.Partition = wll.Partition 
  AND wll.INVENTTRANSTYPE = 0 -- Sales transaction types only
--LEFT JOIN WHSLOADTABLE wlt WITH( NOLOCK ) ON wll.LOADID = wlt.LOADID AND wll.DATAAREAID = wlt.DATAAREAID AND wll.Partition = wlt.Partition
-- Trying to make a left join with the waves line table without creating temp tables or nested queries

LEFT JOIN WHSWAVELINE wavl WITH( NOLOCK ) ON wavl.LOADID = wll.LOADID AND wavl.ORDERNUM = wll.ORDERNUM AND wavl.SHIPMENTID = wll.SHIPMENTID -- Trying to avoid duplicates
    AND wavl.[PARTITION] = wll.[PARTITION] AND wavl.DATAAREAID = wll.DATAAREAID

LEFT JOIN WHSWAVETABLE wavt WITH( NOLOCK ) ON wavt.WAVEID = wavl.WAVEID AND wavt.[PARTITION] = wavl.[PARTITION] AND wavl.DATAAREAID = wavt.DATAAREAID

LEFT JOIN WHSSHIPMENTTABLE shptbl WITH( NOLOCK ) ON shpTbl.SHIPMENTID = wll.SHIPMENTID AND shpTbl.LOADID = wll.LoadID AND shptbl.[ORDERNUM] = wll.[ORDERNUM] 
  AND shptbl.[PARTITION] = wll.[PARTITION] AND shptbl.DATAAREAID = 'ha'

LEFT JOIN WHSCONTAINERLINE cnln WITH( NOLOCK ) ON wll.SHIPMENTID IS NOT NULL AND cnln.[PARTITION] = wll.[PARTITION] AND cnln.DATAAREAID = wll.DATAAREAID
  AND cnln.ITEMID = wll.ITEMID AND cnln.INVENTDIMID = wll.INVENTDIMID AND wll.SHIPMENTID = cnln.SHIPMENTID AND wll.QTY = cnln.QTY

LEFT JOIN WHSWORKTABLE wktbl WITH( NOLOCK ) ON wll.SHIPMENTID IS NOT NULL AND wktbl.SHIPMENTID = cnln.SHIPMENTID AND wktbl.CONTAINERID = cnln.CONTAINERID
  AND wktbl.[PARTITION] = cnln.[PARTITION] AND wktbl.DATAAREAID = cnln.DATAAREAID

LEFT JOIN WHSWORKCLUSTERLINE wkcl WITH( NOLOCK ) ON wll.SHIPMENTID IS NOT NULL AND wkcl.WORKID = wktbl.WORKID AND wkcl.[PARTITION] = wktbl.[PARTITION] AND wkcl.DATAAREAID = wktbl.DATAAREAID

WHERE 
  st.salesstatus = 1 -- Open orders
  --AND wll.ORDERNUM IN ('43174062', '43174039', '43172807')

  --AND wll.SHIPMENTID = '**********'
  
ORDER BY st.SALESID ASC


--sp_columns whsloadline
--sp_columns whsshipmenttable
-- sp_columns whsworkclusterline
-- sp_columns whscontainerline
/*


SELECT TOP 20 *
FROM
  WHSWORKTABLE

-- Same report grouped by shipment

-- Open orders report, grouped by shipment
SELECT 
	st.salesid , wll.SHIPMENTID, st.dlvmode AS DeliveryMode, CAST( wll.CREATEDDATETIME AS DATE ) As ReleaseDate, --wll.LOADID, sl.itemid AS Item, id.INVENTCOLORID AS Color, id.INVENTSIZEID AS Size, 
    SUM( CONVERT( DECIMAL, sl.qtyordered, 10 ) )  AS Qty,
   wlt.LOADSTATUS,
(
CASE 
    WHEN wlt.LOADSTATUS = 0 THEN '1 Open' 
    WHEN wlt.LOADSTATUS = 1 THEN '2 Posted' 
    WHEN wlt.LOADSTATUS = 2 THEN '3 Waved' 
	  WHEN wlt.LOADSTATUS = 3 THEN '4 In process'
	  WHEN wlt.LOADSTATUS = 4 THEN CASE WHEN ISNULL( wll.LOADID, '' ) = '' THEN '1 Open' ELSE '5 In packing' END
	  WHEN wlt.LOADSTATUS = 5 THEN '6 Loaded'
	  WHEN wlt.LOADSTATUS = 6 THEN '7 Shipped'
	  ELSE '0 Not released' 
END	) AS LDStat,
wavt.WAVETEMPLATENAME AS WaveTemplate
FROM SALESTABLE st WITH(NOLOCK) 
JOIN SALESLINE sl  WITH(NOLOCK) ON  st.salesid = sl.salesid AND st.dataareaid = sl.dataareaid AND st.Partition = sl.Partition AND sl.salesstatus = 1 AND sl.qtyordered > '0' AND sl.custgroup not like 'W%'
--JOIN inventdim id WITH(NOLOCK) ON  sl.inventdimid = id.inventdimid AND sl.dataareaid = id.dataareaid AND sl.Partition = id.Partition
LEFT JOIN WHSLOADLINE wll WITH(NOLOCK) ON sl.INVENTTRANSID = wll.INVENTTRANSID AND sl.DATAAREAID = wll.DATAAREAID  AND sl.Partition = wll.Partition AND wll.INVENTTRANSTYPE = 0 -- Sales transaction types only
LEFT JOIN WHSLOADTABLE wlt WITH(NOLOCK) ON wll.LOADID = wlt.LOADID AND wll.DATAAREAID = wlt.DATAAREAID AND wll.Partition = wlt.Partition
LEFT JOIN WHSWAVELINE wavl ON wavl.SHIPMENTID = wll.ShipmentID AND wavl.dataareaid = wll.dataareaid AND wavl.Partition = wll.Partition
LEFT JOIN WHSWAVETABLE wavt ON wavl.WAVEID = wavt.WAVEID AND wavl.dataareaid = wavt.dataareaid AND wavl.Partition = wavt.Partition
WHERE st.salesstatus = 1 -- Open orders
GROUP BY wll.SHIPMENTID, st.salesid, st.dlvmode, wll.LOADID, CAST( wll.CREATEDDATETIME AS DATE ), WLT.LoadStatus, wavt.WAVETEMPLATENAME
ORDER BY st.SALESID ASC
*/