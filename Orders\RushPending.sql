
-- Find Pending Rush orders(not shipped)

WITH rush AS
(
SELECT
    wst.ORDERNUM
  --, wst.ACCOUNTNUM
    , wst.SHIPMENTID
    , CASE WHEN wst.HALOCDIRECTFAIL = 1 THEN 'Yes' ELSE 'No' END AS LDF
    , CASE WHEN ISNULL(cnttbl.CONTAINERID, '') = '' THEN 'N/A' ELSE cnttbl.CONTAINERID END AS [ContainerId]
    , CASE WHEN wktbl.WORKID IS NULL THEN 'N/A' ELSE wktbl.WORKID END AS [WORKID]
    --, wktbl.WORKID
    , CASE WHEN wktbl.FROZEN = 1 THEN 'Yes' ELSE 'No' END   AS WkBlocked
    , CASE 
        WHEN wktbl.WORKSTATUS = 0 THEN 'Open' 
        WHEN wktbl.WORKSTATUS = 1 THEN 'In process'
        WHEN wktbl.WORKSTATUS = 4 THEN 'Closed'
        ELSE 'N/A'
    END AS WkStatus
    , wkusr.USERNAME
    , CASE WHEN ISNULL(wkcl.CLUSTERID, '') = '' THEN 'N/A' ELSE wkcl.CLUSTERID END AS 'ClusterId'
    , CASE WHEN ISNULL(wkct.CLUSTERID, '') = '' THEN 'N/A' ELSE wkct.CLUSTERPROFILEID END AS 'ClusterProfile'
    --, CONVERT( DECIMAL( 10, 0), SUM( cntln.QTY ) )                                          AS Units
    , wst.MODECODE
    --, st.DLVMODE
    , wst.WAVEID
    , wvt.WAVETEMPLATENAME
    , (SELECT CAST(SUM(ll.QTY) AS INT) FROM WHSLOADLINE ll WHERE ll.SHIPMENTID = wst.SHIPMENTID AND ll.ORDERNUM = st.SALESID AND ll.LOADID = wst.LOADID AND ll.DATAAREAID = 'ha') AS Units
    , (SELECT MIN(ll.CREATEDDATETIME) FROM WHSLOADLINE ll WHERE ll.SHIPMENTID = wst.SHIPMENTID AND ll.ORDERNUM = st.SALESID AND ll.LOADID = wst.LOADID  AND ll.DATAAREAID = 'ha') AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS [ReleasedToWH]
    --, wst.DELIVERYNAME                                                                      AS ShipTo
    --, hacs.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS ActualShippedTime
    --, *
FROM
    SALESTABLE st
    -- Taking into account orders with shipments created
    JOIN WHSSHIPMENTTABLE wst               ON st.SALESID = wst.ORDERNUM                AND st.[PARTITION] = wst.[PARTITION]        AND wst.DATAAREAID = 'ha' 
    JOIN WHSWAVETABLE wvt                   ON wvt.WAVEID = wst.WAVEID                  AND wvt.[PARTITION] = wst.[PARTITION]       AND wvt.DATAAREAID = 'ha'
    LEFT JOIN WHSCONTAINERTABLE cnttbl      ON wst.SHIPMENTID = cnttbl.SHIPMENTID       AND cnttbl.DATAAREAID = 'ha'                AND cnttbl.[PARTITION] = wst.[PARTITION]
    LEFT JOIN WHSCONTAINERLINE cntln        ON cntln.CONTAINERID = cnttbl.CONTAINERID   AND cnttbl.[PARTITION] = cntln.[PARTITION]  AND cnttbl.DATAAREAID = cntln.DATAAREAID
    LEFT JOIN WHSWORKTABLE wktbl            ON wktbl.CONTAINERID = cnttbl.CONTAINERID   AND wktbl.DATAAREAID = cnttbl.DATAAREAID    AND wktbl.[PARTITION] = cnttbl.[PARTITION]
    LEFT JOIN WHSWORKCLUSTERLINE wkcl       ON wkcl.WORKID = wktbl.WORKID               AND wkcl.[PARTITION] = wktbl.[PARTITION]    AND wkcl.DATAAREAID = wktbl.DATAAREAID
    LEFT JOIN WHSWORKCLUSTERTABLE wkct      ON wkcl.CLUSTERID = wkct.CLUSTERID          AND wkcl.[PARTITION] = wkct.[PARTITION]     AND wkct.DATAAREAID = 'ha'
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs   ON hacs.CARTONID = cnttbl.CONTAINERID       AND hacs.[PARTITION] = cnttbl.[PARTITION]   AND cnttbl.DATAAREAID = hacs.DATAAREAID
    LEFT JOIN WHSWORKUSER wkusr             ON wkusr.USERID = wktbl.LOCKEDUSER          AND wkusr.DATAAREAID = wktbl.DATAAREAID     AND wkusr.[PARTITION] = wktbl.[PARTITION]   
WHERE
    --wll.CREATEDDATETIME > GETUTCDATE() - @DaysToCheck
    wst.SHIPMENTSTATUS < 5 -- Not Shipped
    AND st.SALESSTATUS = 1 -- Open order
    AND (cnttbl.SHIPCARRIERTRACKINGNUM = '' OR hacs.TRACKINGNUMBER IS NULL )-- Not shipped and without tracking
    AND wst.MODECODE IN ( '1D', '2D', '3D', 'SA', 'IE' )
    
    --AND hacs.CREATEDDATETIME IS NULL -- Not shipped yet
    --AND wst.SHIPCONFIRMUTCDATETIME > CONCAT( @DayToCheck, '07:30:00 PM' )
    --AND wst.ORDERNUM = '********'
GROUP BY
    cnttbl.CONTAINERID, wst.SHIPMENTID, wst.LOADID, wst.ORDERNUM, wst.MODECODE, wst.HALOCDIRECTFAIL --, st.DLVMODE, st.CREATEDDATETIME
    , hacs.CREATEDDATETIME,  wst.WAVEID, wktbl.FROZEN, wkcl.CLUSTERID, wktbl.WORKSTATUS, wkusr.USERNAME, wktbl.WORKID
    , wkct.CLUSTERID, wkct.CLUSTERPROFILEID, wvt.WAVETEMPLATENAME, st.SALESID
)
SELECT 
    ORDERNUM        AS [OrderNum]
    , MODECODE      AS ShipMethod
    , ReleasedToWH
    , Units
    --, ACCOUNTNUM 
    --, SHIPMENTID    AS [ShipmentId]
    , LDF
    , CONTAINERID   AS [ContainerId]
    , WORKID        AS [WorkId]
   , WkBlocked     AS [WorkBlocked]
    , WkStatus      AS [WorkStatus]
    , CASE WHEN ISNULL(Username, '') = '' THEN 'N/A' ELSE Username END AS LockedBy
    , CLUSTERID     AS [ClusterId]
    , ClusterProfile
    
    --, ShipTo
    , WAVEID    AS [WaveId]
    , [WAVETEMPLATENAME] AS [WaveTemplate]
    --, DayReleased
    --, ReleasedToWH
    --, ExpectedShipTime  AS MustShipBy
    --, ActualShippedTime
    --, KYShipConfirmDateTime
    /*, --CASE WHEN DATEPART(yy,ActualShippedTime) = 1899 THEn 'No' 
      CASE WHEN ActualShippedTime IS NULL  THEN 'No'   
       WHEN CAST(ActualShippedTime AS DATETIME2) > CAST(ExpectedShipTime AS DATETIME2) THEN 'No' ELSE 'Yes' END AS ShippedOnTime*/
FROM
    rush
ORDER BY
    ReleasedToWH
