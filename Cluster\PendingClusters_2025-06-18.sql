-- Author: <PERSON>

--Pending clusters, working version
-- Modified on 7/8/2022 to align it with the new clustering proces.
-- A cluster could be associated with more than one wave
-- 2/20/2025
-- Adding the SLA Date for better filtering on the PBI dashboard
-- Changing to one line the Daylight savings calculation
-- Changing query format(leading commas)
-- 3/7/2025
-- Adding literals for the partition and dataarea on the WHERE clause
-- 3/12/2025
-- Adding the VoicePickUser and VoicePutUser to the query
-- Adding the VoiceStatus to the query
-- 3/18/2025
-- Removing orders already shipped from the query
-- 5/13/2025
-- Making the query more readable and efficient
-- 6/8/2025
-- Adding back the partition and dataarea to the joins

DECLARE @ReportDays INT = 20;

WITH PendCl AS (
    SELECT 
		CT.CLUSTERID            AS ClusterID
        , CT.CREATEDBY          AS CreatedBy
		, CT.CREATEDDATETIME    AS CreatedOn
        , ll.CREATEDDATETIME    AS [ReleasedToWH]
        , vput.[STATUS]         AS VoicePutStatus
        , wkusr.USERNAME        AS VoicePutUser
        , voicepick.USERNAME    AS VoicePickUser
		, COUNT( CL.WORKID )    AS ClusterWorkPending
        , CLTotal.ClusterWorkTotal 
        , CT.CLUSTERPROFILEID   AS ClusterProfileID
	FROM 
		WHSWORKCLUSTERLINE CL
	INNER JOIN WHSWORKCLUSTERTABLE CT 	        ON CL.CLUSTERID 		= CT.CLUSTERID 		AND CT.DATAAREAID 		= CL.DATAAREAID AND CT.[PARTITION] 		= CL.[PARTITION]
	INNER JOIN WHSWORKTABLE WT 			        ON CL.WORKID 			= WT.WORKID 		AND WT.DATAAREAID 		= CL.DATAAREAID AND WT.[PARTITION] 		= CL.[PARTITION]
        AND WT.WORKSTATUS		< 2	-- Open, In progress
		AND WT.WORKTRANSTYPE	= 2 -- Sales orders
		AND WT.WORKTEMPLATECODE LIKE '4010%' AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' 
		AND WT.CREATEDDATETIME > DATEADD(DAY, -@ReportDays, GETUTCDATE())
	INNER JOIN WHSCONTAINERTABLE CNTTbl         ON CNTTbl.CONTAINERID 	= WT.CONTAINERID 	    AND CNTTbl.DATAAREAID 	= WT.DATAAREAID AND CNTTbl.[PARTITION] 	= WT.[PARTITION]
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs       ON hacs.CARTONID        = cnttbl.CONTAINERID    AND hacs.[PARTITION]        = cnttbl.[PARTITION]   AND cnttbl.DATAAREAID = hacs.DATAAREAID
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput  ON vput.WORKID          = WT.WORKID             AND vput.DATAAREAID         = WT.DATAAREAID AND vput.[PARTITION]    = WT.[PARTITION]
    LEFT JOIN WHSWORKUSER wkusr 		        ON wkusr.USERID 		= vput.WORKUSER 	    AND wkusr.DATAAREAID 	    = vput.DATAAREAID AND wkusr.[PARTITION] = vput.[PARTITION]
	-- Using OUTER APPLY to get the voice pick user
    -- It might be a record present or not
    OUTER APPLY (
        SELECT 
            --TOP 1 
            wkuser.USERNAME
        FROM HAVOICEINTEGRATIONQUEUEPICKS vip
        INNER JOIN WHSWORKUSER wkuser WITH (NOLOCK) 
            ON wkuser.USERID = vip.WORKUSER 
            AND wkuser.DATAAREAID = 'ha' AND wkuser.[PARTITION] = vip.[PARTITION]
        WHERE vip.WORKID = WT.WORKID
        AND vip.STATUS < 7 AND vip.DATAAREAID = 'ha' AND vip.[PARTITION] = WT.[PARTITION]
        GROUP BY
            /*vip.WORKID, */vip.[STATUS], wkuser.USERNAME
        --ORDER BY vip.[STATUS] DESC
    ) voicepick
    -- Using CROSS APPLY to get the created date of the load line
    -- It has to be a record created
    CROSS APPLY (
        SELECT CAST(MIN(CREATEDDATETIME) AS DATE) AS CREATEDDATETIME
        FROM WHSLOADLINE 
        WHERE SHIPMENTID = WT.SHIPMENTID AND ORDERNUM = WT.ORDERNUM
        AND LOADID = WT.LOADID AND DATAAREAID = 'ha' AND [PARTITION] = WT.[PARTITION]
    ) ll
    CROSS APPLY(
       SELECT 
        COUNT( CLine.WORKID ) AS ClusterWorkTotal
        FROM 
            WHSWORKCLUSTERLINE CLine
            -- It might be needed to join with the worktable to remove canceled work
            INNER JOIN WHSWORKTABLE WT2 ON WT2.WORKID = CLine.WORKID AND CLine.DATAAREAID = 'ha' AND CLine.[PARTITION] = WT2.[PARTITION]
        WHERE 
            CLine.CLUSTERID = CT.CLUSTERID
            AND WT2.WORKSTATUS < 5 -- Don't count canceled work
            AND WT2.WORKTRANSTYPE = 2 -- Sales orders
        GROUP BY CLine.CLUSTERID
    ) ClTotal
    WHERE	
    /*
		WT.WORKSTATUS		< 2	-- Open, In progress
		AND WT.WORKTRANSTYPE	= 2 -- Sales orders
		AND WT.WORKTEMPLATECODE LIKE '4010%' AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' 
		AND WT.CREATEDDATETIME > ( GETDATE() -  @ReportDays ) AND
     */   
       (cnttbl.SHIPCARRIERTRACKINGNUM = '' OR cnttbl.SHIPCARRIERTRACKINGNUM IS NULL) -- Not shipped(including the tracking number added to the table) or tracking number not available
        AND hacs.TRACKINGNUMBER IS NULL -- Not shipped yet
		AND CL.DATAAREAID 		= 'ha' AND CL.[PARTITION] 		= 5637144576
		AND CT.DATAAREAID 		= 'ha' AND CT.[PARTITION] 		= 5637144576
		AND WT.DATAAREAID 		= 'ha' AND WT.[PARTITION] 		= 5637144576
		AND CNTTbl.DATAAREAID 	= 'ha' AND CNTTbl.[PARTITION] 	= 5637144576
		--AND CT.CLUSTERID = 'CL001134674'
	GROUP BY 
		CT.CLUSTERID, CT.CLUSTERPROFILEID, CT.CREATEDBY, CT.CREATEDDATETIME
		, vput.[STATUS], wkusr.USERNAME, voicepick.USERNAME, ll.CREATEDDATETIME
        , ClTotal.ClusterWorkTotal
)
SELECT 
	PendCl.CLUSTERID		AS [ClusterId]
	, PendCl.CreatedBy      AS [CreatedBy]
	-- PendCl.CreatedOn AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS [CreatedOn]
	, FORMAT(PendCl.CreatedOn AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 'MMM dd, yyyy hh:mmtt')  AS [CreatedOn]
	, FORMAT(PendCl.ReleasedToWH, 'MMM dd, yyyy')   AS [SLA_Date]
    , CASE 
        WHEN VoicePutStatus IS NULL THEN CASE WHEN VoicePickUser IS NULL THEN 'N/A' ELSE 'In Progress' END 
        ELSE 
            CASE 
                WHEN VoicePutStatus = 0 THEN 'Pending'
                WHEN VoicePutStatus = 2 THEN 'Completed'
                WHEN VoicePutStatus = 3 THEN 'Error' -- Not sure if this is the correct status
                WHEN VoicePutStatus = 4 THEN 'Reset'
                WHEN VoicePutStatus = 5 THEN 'Manually Picked'
                WHEN VoicePutStatus = 6 THEN 'Canceled'
                ELSE 'N/A'
            END END AS [VoiceStatus]
    , COALESCE(VoicePutUser, VoicePickUser,'N/A')   AS [VoiceUser]
	, PendCl.ClusterWorkPending  		            AS WkPending
	, ClusterWorkTotal  			                AS ClusterTotal
	, PendCl.CLUSTERPROFILEID                       AS ClusterProfile
FROM 
    PendCl
ORDER BY 
	--SLA_Date, 
    ClusterId;    
    
