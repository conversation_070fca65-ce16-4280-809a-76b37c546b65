

-- Order lines items "On order"


SELECT 
    TOP 10 
    sl.SALESID              AS [Order]
    , sl.ITEMID               AS [ItemId]
    , idim.INVENTCOLORID    AS [Color]
    , idim.INVENTSIZEID     AS [Size]
    , sl.NAME               AS [Name]
    , CAST(sl.QTYORDERED AS INT)    AS [Qty]
   -- , itrans.*
FROM
    SALESLINE sl
    INNER JOIN INVENTDIM idim ON idim.INVENTDIMID = sl.INVENTDIMID AND idim.DATAAREAID = 'ha' AND idim.[PARTITION] = sl.[PARTITION]
    JOIN INVENTTRANSORIGIN itorig ON itorig.INVENTTRANSID = sl.INVENTTRANSID AND sl.DATAAREAID = 'ha' AND sl.[PARTITION] = itorig.[PARTITION]
    JOIN INVENTTRANS itrans ON itrans.INVENTTRANSORIGIN = itorig.RECID AND itorig.DATAAREAID = itrans.DATAAREAID AND itorig.[PARTITION] = itrans.[PARTITION]
    JOIN salestable st ON sl.SALESID = st.salesid AND st.DATAAREAID = 'ha' AND st.[PARTITION] = sl.[PARTITION]
WHERE
    sl.SALESSTATUS = 1 -- Open Order
    AND itrans.STATUSISSUE = 6 -- On order
    AND st.SALESSTATUS = 1
    --AND sl.SALESID IN ('46161810', '46162090', '46162419')