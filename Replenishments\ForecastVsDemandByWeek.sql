
USE DAX_PROD

DECLARE @ReportDays AS INT = 45

SELECT
    CONVERT( nvarchar, DATEADD(dd, -(DATEPART(dw, RepWk.CreatedDate) - 1), RepWk.CreatedDate), 101 ) AS WeekStart
    , CONVERT( nvarchar,DATEADD(dd, 7 -(DATEPART(dw, RepWk.CreatedDate)), RepWk.CreatedDate), 101 )  AS WeekEnd
    , DATEPART( wk, RepWk.CreatedDate)        AS WeekOfTheYear
    , SUM( RepWk.MinMaxCarton ) AS MinMaxCartons
    , SUM( RepWk.DemandCarton ) AS DemandCartons
FROM
(
    SELECT
    wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
    , wktbl.WORKID
    ,
     CAST(
    CASE WHEN wktbl.CREATEDDATETIME < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, wktbl.CREATEDDATETIME ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
		DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, wktbl.CREATEDDATETIME ) AS nvarchar(4)) AS datetime) ) ) 
		THEN    DATEADD(hh, - 5, wktbl.CREATEDDATETIME ) 
		ELSE 
		CASE    WHEN wktbl.CREATEDDATETIME < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, wktbl.CREATEDDATETIME ) AS nvarchar(4)) AS datetime ) + 1 -
		DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, wktbl.CREATEDDATETIME ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, wktbl.CREATEDDATETIME ) 
				ELSE dateadd(hh, - 5, wktbl.CREATEDDATETIME ) 
		END 
	END AS DATE ) AS CreatedDate
    --, wktbl.WORKTEMPLATECODE        AS WorkTemplate
    --, CASE WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 'MinMax' ELSE 'Demand' END AS ReplenType
    , CASE WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 1 ELSE 0 END  AS MinMaxCarton
    , CASE WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 0 ELSE 1 END  AS DemandCarton
      , CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )     AS 'CartonQty'
FROM
    WHSWORKTABLE wktbl
    LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
    LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
WHERE
    wktbl.CREATEDDATETIME > GETUTCDATE() - ( @ReportDays + 7 )  -- Replenishments since the previous week
    AND wktbl.WORKTRANSTYPE = 11  -- Replenishment
    AND wkln.WORKTYPE = 2 -- Put. To get only one line
    AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
    AND loc.locprofileid LIKE '%Picking%' -- Only Picking locations
    AND wktbl.WORKSTATUS < 5 -- Not cancelled
) AS RepWk
GROUP BY
    DATEPART( wk, RepWk.CreatedDate)
    , DATEADD(dd, -(DATEPART(dw, RepWk.CreatedDate) - 1), RepWk.CreatedDate) 
    , DATEADD(dd, 7 -(DATEPART(dw, RepWk.CreatedDate)), RepWk.CreatedDate) 
   -- , RepWk.ReplenType
ORDER BY
    DATEPART( wk, RepWk.CreatedDate)

/*
SELECT
    DATEADD(dd, -(DATEPART(dw, GETDATE()) - 1), GETDATE()) AS WeekStart
    , DATEADD(dd, 7 -(DATEPART(dw, GETDATE())), GETDATE())  AS WeekEnd
*/