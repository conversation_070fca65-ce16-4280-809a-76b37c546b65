-- ONHIAllDim - Excel query
-- Starting to modify this query on 7/25/2023
-- The goal is to get all the information at once

/*
    5/9/2024
  Modifying the Excel query to use it in the 'SKU Pending Cubiscan report'
*/

WITH vols AS
(
    SELECT
        physd.ITEMID                AS [Item]
        , physd.ECORESITEMCOLORNAME AS [Color]
        , physd.ECORESITEMSIZENAME  AS [Size]
        , CONVERT( DECIMAL( 10, 2 ), ISNULL( physd.[DEPTH], 0 ) * ISNULL( physd.[WIDTH], 0) * ISNULL( physd.[HEIGHT], 0 ) ) AS 'Volume'
    FROM 
        WHSPHYSDIMUOM physd
 -- This was causing an error: some SKUs with zero dimensions(new ones) were not showing up in the results
   /* WHERE  
        physd.WEIGHT > 0 
        AND physd.DEPTH > 0 
        AND physd.WIDTH > 0 
        AND physd.HEIGHT > 0*/
),
avgvol AS  --Average Volumes by It<PERSON> and <PERSON><PERSON>
(
    SELECT
        vols.Item
        --, physd.ECORESITEMCOLORNAME AS [Color]
        , vols.[Size]
        , CONVERT( DECIMAL( 10, 2 ), AVG( vols.Volume ) ) AS 'AVG_Volume'
    FROM 
        vols
    GROUP BY
        vols.Item, vols.Size
), invdata AS
(
  SELECT --TOP 100
     CASE WHEN ISNULL( idim.INVENTSIZEID, '' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
          WHEN ISNULL( idim.INVENTCOLORID,'' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID
          ELSE itbl.ITEMID
     END AS 'SKU'
    --, itbl.NAMEALIAS
    , itbl.ITEMID + '-' + idim.INVENTSIZEID             AS [Item-Size]
    , itbl.ITEMID                                       AS [Item]
    , idim.INVENTCOLORID                                AS [Color]
    , idim.INVENTSIZEID                                 AS [Size]
    --, prodtrans.DESCRIPTION                             AS 'Description'
    , idim.[INVENTSTATUSID]                             AS 'Status'
    , CONVERT( DECIMAL( 10, 0 ), isum.PHYSICALINVENT )  AS 'Qty'
    --, CONVERT( DECIMAL( 10, 0 ), isum.RESERVPHYSICAL )  AS 'Reserve'
    --, CONVERT( DECIMAL(10, 0 ), isum.ONORDER ) AS 'OnOrder' 
    --, CONVERT( DECIMAL(10, 0), isum.physicalinvent - isum.reservphysical - isum.onorder) AS Available
    --, idim.[LICENSEPLATEID] AS 'LicensePlate'
    , idim.[WMSLOCATIONID] AS 'Bin'
    , wmsloc.LOCPROFILEID AS 'Bin Type'
    , CONVERT( DECIMAL( 10, 2 ), physd.[WEIGHT],    0)  AS 'Weight'
    , CONVERT( DECIMAL( 10, 2 ), physd.[DEPTH],     0 ) AS 'Depth'
    , CONVERT( DECIMAL( 10, 2 ), physd.[WIDTH],     0 ) AS 'Width'
    , CONVERT( DECIMAL( 10, 2 ), physd.[HEIGHT],    0 ) AS 'Height'
    --, physd.UOM
    , CONVERT( DECIMAL( 10, 2 ), ISNULL( physd.[DEPTH], 0 ) * ISNULL( physd.[WIDTH], 0) * ISNULL( physd.[HEIGHT], 0 ) ) AS 'Volume'  
    , avgvol.AVG_Volume                                                                                                 AS [Avg_Vol_ItemSize]
    --, CONVERT( DECIMAL(10, 2), AVG( ISNULL( physd.[DEPTH], 0 ) * ISNULL( physd.[WIDTH], 0) * ISNULL( physd.[HEIGHT], 0 ) ) OVER (PARTITION BY physd.ITEMID, physd.ECORESITEMSIZENAME ) ) AS AVG_VOL_ITEMSIZE
    , physd.HA_MEASUREDBY   AS MeasuredBy
    , CAST( physd.HA_MEASUREDDATE AT TIME Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME ) AS MeasuredDate
    , physd.MODIFIEDBY
    , CAST( physd.MODIFIEDDATETIME AT TIME Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME ) AS MODIFIEDDATETIME
    --, COUNT(*) OVER (PARTITION BY itbl.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID) AS TotalLocCount_
    --, COUNT(*) OVER (PARTITION BY itbl.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, wmsloc.LOCPROFILEID) AS LocCoun_ByLocProfile
    --, prod.cost AS Cost
    --, ( isum.physicalinvent - isum.reservphysical - isum.onorder ) * prod.cost AS 'ExtCost'
FROM 
    INVENTDIM idim 
    LEFT JOIN INVENTSUM isum  WITH (NOLOCK)        ON idim.INVENTDIMID = isum.INVENTDIMID          AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION]
    LEFT JOIN WHSPHYSDIMUOM physd WITH (NOLOCK)     ON isum.ITEMID  = physd.ITEMID  AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID
        AND physd.DATAAREAID = isum.DATAAREAID AND physd.[PARTITION] = isum.[PARTITION]
    INNER JOIN WMSLOCATION wmsloc                   ON idim.WMSLOCATIONID = wmsloc.WMSLOCATIONID    AND wmsloc.DATAAREAID = idim.DATAAREAID AND wmsloc.[PARTITION] = idim.[PARTITION]
    INNER JOIN INVENTTABLE itbl                     ON isum.ITEMID      = itbl.ITEMID               AND itbl.DATAAREAID = isum.DATAAREAID AND itbl.[PARTITION] = isum.[PARTITION]
    INNER JOIN ECORESPRODUCTTRANSLATION prodtrans   ON itbl.PRODUCT = prodtrans.PRODUCT             AND prodtrans.[PARTITION] = itbl.[PARTITION]
    INNER JOIN avgvol ON avgvol.Item = itbl.ITEMID  AND physd.ECORESITEMSIZENAME = avgvol.[Size]
/*
LEFT JOIN HARMSPRODUCT prod ON 
        CASE
            WHEN ISNULL( idim.INVENTSIZEID, '' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
            WHEN ISNULL( idim.INVENTCOLORID,'' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID
            ELSE itbl.ITEMID 
        END = prod.ITEMSKU  
        AND prod.DATAAREAID = isum.DATAAREAID AND prod.[PARTITION] = idim.[PARTITION]
*/
WHERE 
    isum.PHYSICALINVENT BETWEEN 1 AND 5000 
    AND idim.INVENTLOCATIONID = '4010' 
    AND idim.INVENTSITEID = 'HA USA' 
    --AND idim.[INVENTSTATUSID] = 'Available'
    AND wmsloc.LOCPROFILEID IN ( 'Bulk','CUBISCAN', 'Overflow', 'PalletPicking' ,'Picking', 'Picking A', 'Picking D')
    AND itbl.ITEMID NOT IN ( '30991', '3333', '9999', '9997' ) --GC, RESHIP, FS-GC
    --AND itbl.ITEMID LIKE '3%'
)
SELECT 
    *
    --, CONVERT( DECIMAL(10, 2), AVG( invdata.Volume) OVER (PARTITION BY invdata.Item, invdata.Size) ) AS AVG_VOL_ITEMSIZE
    , CASE WHEN invdata.Avg_Vol_ItemSize <> 0 THEN CAST( (invdata.Volume/Avg_Vol_ItemSize) AS DECIMAL(12,2) )  ELSE 0.00 END AS '%OFFSET'
FROM
    invdata   
--WHERE
--    Volume - AVG_VOL_ITEMSIZE <> 0
--ORDER BY 
--    'Item-Size', 'Color', 'Bin Type', 'Bin'
