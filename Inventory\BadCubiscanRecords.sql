
-- Locating items with bad UPC numbers from the Cubiscan

-- The 'UPDATED' column is 'Y' when needs to be updated ????
/*
6/4/2024

All records marked with 'Y' on the UPDATED column are invalid: deleting them all.
*/

--BEGIN TRANSACTION;

SELECT *
--DELETE
FROM
    HACUBISCANIMPORT
WHERE
    LEN(ITEMID) <> 12
    OR UPDATED = 'Y'
    --OR SEQNUM IN (30231, 30230,30227,30214,30200,24091, 23004)


SELECT *
--DELETE
FROM
    HA_CUBISCAN_ITEM_INFO_IMPORT
WHERE
    --LEN(ITEM_ID) <> 12
    UPDATED = 'Y'

--COMMIT;