
SELECT 
    cnttbl.CONTAINERID
    --, shptbl.SHIPMENTID
    , CAST(cnttbl.MOD<PERSON>IE<PERSON><PERSON><PERSON><PERSON><PERSON> AT TIME ZONE 'UTC' AT TIME ZONE 'EASTERN STANDARD TIME' AS DATETIME) AS ShippedDT
    , shptbl.ADDRESS
    , shptbl.MODECODE   AS ShpDelivMode
    --, CHARINDEX('BOX', UPPER(shptbl.ADDRESS))
    -- , cnttbl.HAWHSSHIPMETHOD
   -- , hsmt.CARRIER
    --, hsmt.CARRIERSERVICE
    , hsmt.SHIPMETHODDESCRIPTION
   -- , hsmt.SHIPMETHOD
    /*
    , CASE 
        WHEN cnttbl.HAWHSSHIPMETHOD = 40 THEN 'FedEx Ground'
        WHEN cnttbl.HAWHSSHIPMETHOD = 41 THEN 'FedEx Home'
        WHEN cnttbl.HAWHSSHIPMETHOD = 42 THEN 'FedEx SmartPost Parcel Select'
        WHEN cnttbl.HAWHSSHIPMETHOD = 63 THEN 'FedEx SmartPost Parcel Select Lightweight'
        WHEN cnttbl.HAWHSSHIPMETHOD = 70 THEN 'UPS Next Day Air Saver'
        WHEN cnttbl.HAWHSSHIPMETHOD = 71 THEN 'UPS 2nd Day Air'
        WHEN cnttbl.HAWHSSHIPMETHOD = 72 THEN 'UPS 3 Day Select'
        WHEN cnttbl.HAWHSSHIPMETHOD = 73 THEN 'UPS Ground'
        ELSE 'Unknown'
    END As ShipMethod
    */
    , cnttbl.SHIPCARRIERTRACKINGNUM
    , cnttbl.MASTERTRACKINGNUM
FROM
    WHSSHIPMENTTABLE shptbl
    JOIN WHSCONTAINERTABLE cnttbl ON cnttbl.SHIPMENTID = shptbl.SHIPMENTID AND cnttbl.[PARTITION] = shptbl.[PARTITION] AND cnttbl.DATAAREAID = shptbl.DATAAREAID
    JOIN [HASHIPMETHODTRANSLATION] hsmt ON	hsmt.HAWHSSHIPMETHOD = cnttbl.HAWHSSHIPMETHOD AND hsmt.DATAAREAID = cnttbl.DATAAREAID
WHERE
    cnttbl.MODIFIEDDATETIME > '7/28/2023 8:15:00 pm' -- Change between shifts
    AND shptbl.SHIPMENTSTATUS = 5 -- Shipped
    AND (UPPER(shptbl.ADDRESS) LIKE '%PO BOX%' OR UPPER(shptbl.ADDRESS) LIKE '%P.O. BOX%') -- PO Boxes filter
    AND CHARINDEX('BOX', UPPER(LTRIM(shptbl.ADDRESS))) < 7 -- PO Box at the beginning. If it's not, is a user input error
    AND NOT (shptbl.MODECODE = 'S' AND cnttbl.HAWHSSHIPMETHOD IN (42,63)) -- Exclude 'S' shipped with FedEx SmartPost, which is OK
    AND ISNULL( cnttbl.SHIPCARRIERTRACKINGNUM, '' ) <> '' -- Already shipped with tracking
