
DECLARE @startDateTimePST AS DATETIME = '@{variables('start_date')}', 

    @endDateTimePST AS DATETIME = '@{variables('end_date')}'; 

 

DECLARE @startDateTimeUTC AS DATETIME = @startDateTimePST 

      AT TIME ZONE 'Pacific Standard Time' 

      AT TIME ZONE 'UTC', 

    @endDateTimeUTC AS DATETIME = @endDateTimePST 

      AT TIME ZONE 'Pacific Standard Time' 

      AT TIME ZONE 'UTC'; 

 

WITH SalesTable AS ( 

  SELECT  

    SALESID AS SalesID 

    ,HADWSALESID AS SFCCOrderID 

    ,SALESORIGINID AS SalesOrigin 

    ,CUS<PERSON><PERSON>OUNT AS CustomerAccount 

    ,EMAIL AS OrderEmail 

    ,<PERSON><PERSON>IVER<PERSON>NA<PERSON> AS DeliveryName 

    ,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>LADDRESS AS DeliveryPostalAddressID 

    ,M<PERSON><PERSON>IEDDATETIME AS ModifiedDateTimeUTC 

    ,CREATEDDATETIME AS CreatedDateTimeUTC 

    ,HAORDERDATETIME AS OrderDateTimeUTC 

    ,PARTITION 

    ,DATAAREAID 

    ,RECID 

  FROM [PRODSQL02.HAN<PERSON>ANDERSSON.COM].DAX_PROD.dbo.SALESTABLE 

  WHERE MODIFIEDDATETIME >= @startDateTimeUTC  

    AND MODIFIEDDATETIME < @endDateTimeUTC 

    AND SALESORIGINID IN ('WEB','CALLCENTER') 

    ), 

CreditCardTrans AS ( 

  SELECT * FROM ( 

    SELECT  

      st.SalesID 

      ,cct.RECID AS CCTRecID 

      ,cct.CREDITCARDNUMSECURE 

      ,cct.LOCATION 

      ,RANK() OVER (PARTITION BY st.SalesID 

             ORDER BY cct.RECID DESC) AS CCTRank 

    FROM [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.CREDITCARDTRANS cct  

    JOIN SalesTable st ON  

      st.SalesID = cct.SALESID 

      AND st.DATAAREAID = cct.DATAAREAID 

      AND st.PARTITION = cct.PARTITION 

      AND cct.TRXTYPE IN ('A', 'C')) r 

  WHERE r.CCTRank = 1) 

SELECT  

  st.SalesID 

  ,mcp.RECID AS AXPaymentRecID 

  ,CONVERT(money, mcp.AMOUNT) AS Amount 

  ,rtt.NAME AS PaymentTypeDescription 

  ,lloc.DESCRIPTION AS BillingName 

  ,adr.STREET AS BillingStreet 

  ,adr.CITY AS BillingCity 

  ,adr.STATE AS BillingState 

  ,adr.ZIPCODE AS BillingZip 

  ,adr.COUNTRYREGIONID AS BillingCountry 

  ,phone.LOCATOR AS BillingPhone 

  ,mcp.CREATEDDATETIME AS CreatedDateUTC 

  ,mcp.MODIFIEDDATETIME AS ModifiedDateUTC 

FROM SalesTable st  

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.MCRCUSTPAYMTABLE mcp ON 

  mcp.REFRECID = st.RECID 

  AND mcp.REFTABLEID = '366' 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.RETAILTENDERTYPETABLE rtt ON 

  rtt.TENDERTYPEID = mcp.TENDERTYPEID 

LEFT JOIN CreditCardTrans cct ON 

  cct.SalesID = st.SalesID 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.LOGISTICSPOSTALADDRESS adr ON 

  adr.RECID = cct.LOCATION 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.LOGISTICSLOCATION lloc ON 

  lloc.RECID = adr.LOCATION 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.DIRPARTYLOCATION ploc ON 

  ploc.LOCATION = adr.LOCATION 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.DIRPARTYTABLE AS dpt ON 

  dpt.RECID = ploc.PARTY 

LEFT JOIN [PRODSQL02.HANNAANDERSSON.COM].DAX_PROD.dbo.LOGISTICSELECTRONICADDRESS AS phone WITH (NOLOCK)  

  ON phone.RECID = dpt.PRIMARYCONTACTPHONE 

order by AXPaymentRecID 