
/*
 Dividing the list of containers from a wave of batchs of 10.
 This is useful when an AB document generation job fails, and the containers have to be reset

*/

--Generated using ChatGPT-4o mini, 1/2/2025

WITH ContainerList AS (
    -- Step 1: Generate sequential row numbers for each container in the specified WAVEID
    SELECT 
        cnttbl.ContainerID,  -- Container ID from the Container table
        ROW_NUMBER() OVER (PARTITION BY shptbl.WAVEID ORDER BY cnttbl.ContainerID) AS rn  -- Generate sequential row numbers per wave (WAVEID)
    FROM 
        WHSCONTAINERTABLE cnttbl
        JOIN WHSSHIPMENTTABLE shptbl 
            ON cnttbl.SHIPMENTID = shptbl.SHIPMENTID  -- Join the shipment tables to match containers with their respective shipments
            AND cnttbl.DATAAREAID = 'ha'  -- Filter by the data area
            AND cnttbl.[PARTITION] = shptbl.[PARTITION]  -- Ensure partition match
    WHERE
        shptbl.WAVEID = 'WV000249784'  -- Filter containers based on the specified WAVEID
),
BatchGroups AS (
    -- Step 2: Group containers into batches of 10 based on their row number
    SELECT 
        ContainerID,  -- Select the container ID
        (rn - 1) / 10 + 1 AS BatchNumber  -- Calculate the batch number by dividing the row number by 10. Each batch contains 10 containers.
    FROM ContainerList
)
SELECT 
    -- Step 3: Concatenate the container IDs for each batch into a comma-separated string
    STUFF((
        -- Subquery to concatenate container IDs for each batch
        SELECT ', ' + b.ContainerID  -- Concatenate the container IDs with a comma separator
        FROM BatchGroups b
        WHERE b.BatchNumber = bg.BatchNumber  -- Filter to include only the current batch
        FOR XML PATH('')  -- Use FOR XML PATH to concatenate values into a single string
    ), 1, 2, '') AS ContainerIDs  -- Use STUFF to remove the leading comma and space
FROM (
    -- Step 4: Select distinct batch numbers (this ensures we process each batch once)
    SELECT DISTINCT BatchNumber
    FROM BatchGroups
) AS bg
ORDER BY bg.BatchNumber;  -- Order the final result by the batch number



-- Gemini 2.0 flash experimental
-- This query retrieves a list of ContainerIDs, grouped into batches of 10, for a specific WAVEID.
-- It is optimized for SQL Server 2016 compatibility, avoiding the use of STRING_AGG.

WITH ContainerList AS (
    -- This CTE selects the necessary data from WHSCONTAINERTABLE and WHSSHIPMENTTABLE,
    -- joining them on SHIPMENTID and filtering by DATAAREAID, PARTITION, and WAVEID.
    SELECT 
        cnttbl.ContainerID,
        shptbl.SHIPMENTID,
        shptbl.WAVEID,
        -- Assigns a row number to each container within the result set, ordered by ContainerID.
        -- The PARTITION BY clause is removed as the WHERE clause already filters by a single WAVEID.
        ROW_NUMBER() OVER (ORDER BY cnttbl.ContainerID) AS rn
    FROM 
        WHSCONTAINERTABLE cnttbl
        JOIN WHSSHIPMENTTABLE shptbl 
            ON cnttbl.SHIPMENTID = shptbl.SHIPMENTID 
            AND cnttbl.DATAAREAID = 'ha' 
            AND cnttbl.[PARTITION] = shptbl.[PARTITION]
    WHERE
        shptbl.WAVEID = 'WV000249784' -- Filter by the desired WAVEID.
),
BatchedContainers AS (
    -- This CTE calculates the batch number for each container based on its row number.
    -- Each batch contains up to 10 containers.
    SELECT
        ContainerID,
        -- Calculates the batch number: (row number - 1) / 10 + 1
        (rn - 1) / 10 + 1 AS BatchNumber
    FROM ContainerList
)
SELECT
    -- This is the main SELECT statement that concatenates the ContainerIDs for each batch.
    STUFF((
        -- This subquery concatenates the ContainerIDs for a specific batch using FOR XML PATH('').
        SELECT ', ' + ContainerID
        FROM BatchedContainers c2
        WHERE c2.BatchNumber = b.BatchNumber -- Correlates with the outer query to get containers for the current batch.
        ORDER BY ContainerID -- Ensures consistent ordering of ContainerIDs within each batch. This is CRUCIAL.
        FOR XML PATH('') -- Concatenates the ContainerIDs into a comma-separated string.
    ), 1, 2, '') AS ContainerIDs -- Removes the leading ', ' from the concatenated string using STUFF.
FROM (SELECT DISTINCT BatchNumber FROM BatchedContainers) b -- Selects distinct batch numbers for the outer query.
ORDER BY b.BatchNumber; -- Orders the results by batch number.

-- Key Improvements over the original query:
-- 1. Reduced scope of FOR XML PATH(''): It now operates on smaller batches, significantly improving performance.
-- 2. Pre-calculated BatchNumber: Avoids redundant calculations.
-- 3. Clearer logic and structure using CTEs.
-- 4. CRUCIAL: ORDER BY within the FOR XML PATH('') subquery to ensure consistent string ordering.


--sp_columns WHSCONTAINERTABLE
--sp_columns WHSSHIPMENTTABLE