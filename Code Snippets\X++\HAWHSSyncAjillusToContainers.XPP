// 6/14/16, <PERSON>, HA_ISS0530_AjillusConfirmationInfo: Update the Ajillus info from the stagin table to container table

public void run()
{
    HAShippedCartonStaging  shippedCartonStaging, shippedCartonStagingMax;
    WHSContainerTable       containerTable;
    MHXOutboundQueue        outboundQueue;
    MHXSubscription         subscription;
    HaShipMethodTranslation shipMethodTranslation;
    #OCCRetryCount

    try
    {
        ttsbegin;
        while select forupdate containerTable
            exists join shippedCartonStaging
                where shippedCartonStaging.ShipDate     >= systemDateGet() - gNumberOfDay
                    && containerTable.ContainerId       == shippedCartonStaging.CartonId
        {
            select firstOnly Weight, ShipDate, ShipMethod, ShipperId, ShippingCost, TrackingNumber, RecId from shippedCartonStagingMax
                order by RecId desc
                where shippedCartonStagingMax.ShipDate  >= systemDateGet() - gNumberOfDay
                    && containerTable.ContainerId       == shippedCartonStagingMax.CartonId;

            if (shippedCartonStagingMax)
			// This is the main part: it's taking the information about a shipped package, stored at a temporary table(HAShippedCartonStaging), and
			// updating the containers table(WHSContainerTable)
            {
                containerTable.HAWHSAjillusWeight       = shippedCartonStagingMax.Weight;
                containerTable.HAWHSShipDate            = shippedCartonStagingMax.ShipDate;
                containerTable.HAWHSShipMethod          = shippedCartonStagingMax.ShipMethod;
                containerTable.HAWHSShipperId           = shippedCartonStagingMax.ShipperId;
                containerTable.HAWHSShippingCost        = shippedCartonStagingMax.ShippingCost;
                containerTable.ShipCarrierTrackingNum   = shippedCartonStagingMax.TrackingNumber;
                containerTable.doUpdate();

                select firstOnly forUpdate outboundQueue
                    where outboundQueue.MHXData1 == containerTable.ContainerId
                    exists join subscription
                        where subscription.MHXSubscriptionId == outboundQueue.MHXSubscriptionId
                        &&    subscription.HAIntegrationPoint == HAIntegrationPoint::Conveyor;

                if (outboundQueue)
				// Leo -According to my findings, the following code is the one changing the information on the conveyor
                {
                    select firstOnly ShipMethodDescription from shipMethodTranslation
                        where shipMethodTranslation.HAWHSShipMethod == shippedCartonStagingMax.ShipMethod;

                    if (outboundQueue.MHXData2 != shipMethodTranslation.ShipMethodDescription)
                    {
                        outboundQueue.MHXData2 = shipMethodTranslation.ShipMethodDescription;
                        outboundQueue.MHXOutboundStatus = MHXOutboundStatus::Created;

                        outboundQueue.update();
                    }
                }
            }
        }
        ttscommit;
    }
    catch (Exception::Deadlock)
    {
        retry;
    }
    catch (Exception::UpdateConflict)
    {
        if (appl.ttsLevel() == 0)
        {
            if (xSession::currentRetryCount() >= #RetryNum)
            {
                throw Exception::UpdateConflictNotRecovered;
            }
            else
            {
                retry;
            }
        }
        else
        {
            throw Exception::UpdateConflict;
        }
    }
    catch
    {
        throw error("Containter updated fail.");
    }
}