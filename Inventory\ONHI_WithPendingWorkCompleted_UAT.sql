

-- Calculating Floor capacity, taking in account pending work

SELECT
  ONHI_FC.[Location]
  , ONHI_FC.ZoneId
  , ONHI_FC.AisleSortCode
  , ONHI_FC.LocSortCode
  , ONHI_FC.SKU
  , CONVERT( DECIMAL( 10, 0 ), 0.5*( ONHI_FC.Occupied + ONHI_FC.IncomingWork + ABS(ONHI_FC.Occupied - ONHI_FC.IncomingWork) )  ) AS Occupied -- Calculating the MAX value
  --, CASE WHEN CAST( hagpft.DATETIMESTAMP AS DATE ) > CAST( GETDATE() - 2 AS DATE ) THEN 1 ELSE 0 END AS Online
  , CASE WHEN ( hagpft.AVAILABILITY = 'in stock' OR  ONHI_FC.RESERVPHYSICAL > 0 ) THEN 'Yes' ELSE 'No' END  AS Online
  , CASE WHEN ISNULL(hafc.SlotTier, '' ) = '' 
        THEN 0 
        ELSE 
          CASE WHEN hafc.FCQty > 0 THEN 1 ELSE 0 END 
    END AS Forecasted
FROM
(
SELECT  
     DISTINCT loc.wmslocationid	AS 'Location'
    , loc.zoneid			AS ZoneId
    , loc.locprofileid	AS LocProfile
    , 
    CASE WHEN 
            OH.PHYSICALINVENT > 0 THEN OH.itemid +'-' + OH.INVENTCOLORID + '-' +  OH.inventsizeid	
         ELSE
            wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
    END AS SKU   
    
    --, CONVERT( DECIMAL( 10, 0 ), OH.physicalinvent	)		AS Qty
    , CONVERT( DECIMAL( 10, 0),  OH.reservphysical	)		AS Reserved 
    , CASE WHEN OH.PHYSICALINVENT >= 1 THEN 1 ELSE 0 END    AS Occupied
    , CASE WHEN wkln.QTYWORK >= 1 THEN 1 ELSE 0 END         AS IncomingWork
    , OH.RESERVPHYSICAL
   -- , MAX( wkln.WORKID )                                    AS WORKID
    --, MAX(wkln.WORKCLASSID)                                 AS WorkClassID
    , loc.HAAisleSortCode           AS 'AisleSortCode'
    , loc.sortcode                  AS 'LocSortCode'
FROM wmslocation loc
LEFT OUTER JOIN whsworkline wkln ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha' AND wkln.WORKTYPE = 2 AND wkln.WORKSTATUS < 4
LEFT OUTER JOIN inventdim idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = wkln.DATAAREAID
LEFT OUTER JOIN 

( SELECT DISTINCT idim.wmslocationid, isum.itemid, idim.inventcolorid, idim.inventsizeid, isum.physicalinvent, isum.reservphysical
  FROM inventsum isum
 LEFT OUTER JOIN inventdim idim ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = 'ha' AND isum.PARTITION = '5637144576'
  WHERE isum.physicalinvent > 0 AND idim.inventlocationid = '4010' AND idim.DATAAREAID = 'ha'-- KY DC only
) as OH -- Pulls location with inventory. Avoiding duplicate records because inventsum has many records
ON loc.wmslocationID = OH.wmslocationid
WHERE 
    loc.inventlocationid = '4010' 
    AND loc.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking' /*'Offsite','Bulk'*/) 
	AND loc.zoneid NOT IN ( 'Current Pick') -- Exclude GCs, 3333s
--GROUP BY
 --   loc.WMSLOCATIONID--, OH.ITEMID, OH.INVENTCOLORID, OH.INVENTSIZEID, wkln.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, OH.PHYSICALINVENT, wkln.QTYWORK
) AS ONHI_FC
LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = ONHI_FC.SKU
LEFT JOIN 
(
    SELECT 
        Item + '-' + Color +'-' + Size_   AS SKU
        , SlotTierValue                   AS SlotTier
        , HAForecastStartDate
        , HAFORECASTDAY1 + HAFORECASTDAY2 + HAFORECASTDAY3 + HAFORECASTDAY4 + HAFORECASTDAY5 + HAFORECASTDAY6 + HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 + HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14 AS FCQty
    FROM    
        HAFORECASTREPLENISHMENTTABLE hafc 
) AS hafc
ON hafc.SKU = ONHI_FC.SKU
ORDER BY 
  ONHI_FC.LocSortCode

--sp_columns wmslocation