-- Geodis Data
-- Pulling the data for the Amazon transfer to Geodis


SELECT wl.WMSLOCATIONID                                         AS "Location",
    haig.GLOBALTRADEITEMNUMBER                                  AS UPC, 
    wl.ITEMID +'-' + id.INVENTCOLORID + '-' + id.INVENTSIZEID   AS SKU, 
    CONVERT( DECIMAL, wl.INVENTQTYWORK, 4 )                     AS Qty, 
    wt.TargetLicensePlateId                                     AS LP,
    CASE    WHEN DATEPART( mm, wl.WORKCLOSEDUTCDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wl.WORKCLOSEDUTCDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wl.WOR<PERSON><PERSON>OSEDUTCDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wl.WORKCLOSEDUTCDATETIME ) -- No DST
        WHEN DATEPART( mm, wl.WORKCLOSEDUTCDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, wl.W<PERSON><PERSON>CLOSEDUTCDATETIME ) < 8 OR DATEPART( dd, wl.W<PERSON><PERSON><PERSON>OSEDUTCDATETIME ) > 14 THEN  DATEADD( hh, - 5, wl.WORKCLOSEDUTCDATETIME ) -- No DST
                    WHEN DATEPART( dd, wl.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wl.WORKCLOSEDUTCDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wl.WORKCLOSEDUTCDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wl.WORKCLOSEDUTCDATETIME )
             END
        WHEN DATEPART( dd, wl.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wl.WORKCLOSEDUTCDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wl.WORKCLOSEDUTCDATETIME )
        ELSE DATEADD( hh, - 4, wl.WORKCLOSEDUTCDATETIME )
END  AS WorkClosedOn 
FROM WHSWORKLINE wl
INNER JOIN WHSWORKTABLE wt ON wt.WORKID = wl.WORKID
INNER JOIN INVENTDIM id ON wl.INVENTDIMID = id.INVENTDIMID
INNER JOIN HAINVENTITEMGTIN haig ON wl.ITEMID = haig.ITEMID AND id.INVENTCOLORID = haig.INVENTCOLORID AND id.INVENTSIZEID = haig.INVENTSIZEID
WHERE	wt.ORDERNUM IN ( 'TO-00074367', 'TO-00074366') -- Only pick lines are populated 
    AND wt.WORKSTATUS = 4 -- Closed Work


SELECT TOP 20*
FROM HAINVENTITEMGTIN 

SELECT TOP 20*
FROM WHSWORKLINE