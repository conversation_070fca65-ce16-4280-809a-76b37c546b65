-- Inbound Data
-- Sam's request 11/3/2023

SELECT
    wktbl.ORDERNUM                                                                          AS [PO_Number]
    --, wktbl.WORKID
    ,   CASE 
            WHEN isnull(idim.inventcolorid, '') = '' THEN wkln.itemid
            WHEN isnull(idim.inventsizeid, '') = '' THEN wkln.itemid + '-' + idim.inventcolorid
            ELSE wkln.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid    
        END                                                 AS [SKU]
    , CAST(wkln.QTYWORK AS INT)                                                             AS [Qty]
    , wkln.UNITID                                                                           AS [QuantityUOM]
    , wkln.wmslocationid                                                                    AS [PutAwayLocation]
    , wktbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'         AS [KY_ReceivedDateTime]
    , wktbl.WORKCLOSEDUTCDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'   AS [KY_PutAwayDateTime]
   -- , wkln.WORKID, wkln.WORKCLASSID, wktbl.WORKTEMPLATECODE ,loc.LOCPROFILEID
FROM
    [DAX_Archive].[arc].WHSWORKTABLE wktbl
    JOIN [DAX_Archive].[arc].WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION] --AND wkln.LINENUM = MAX(wkln.LINENUM)
    JOIN [DAX_Archive].[arc].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
    JOIN [DAX_PROD].[dbo].WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.DATAAREAID = wkln.DATAAREAID AND loc.PARTITION = wkln.PARTITION
WHERE
    wktbl.WORKTRANSTYPE = 1 -- Purchase orders
    AND wktbl.WORKSTATUS = 4 -- Closed
    AND wkln.WORKTYPE   = 2 -- Put
    AND wkln.WORKCLASSID IN ('BulkPut', 'PutCubeOUT', 'FwdThrow', 'RecPutaway')
    AND loc.LOCPROFILEID IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', 'Bulk')
    AND wktbl.INVENTSITEID = 'HA USA'
    AND wktbl.INVENTLOCATIONID = '4010'
    AND wktbl.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 05:00:00 AM'

--ORDER BY wktbl.WORKID
/*
UNION
SELECT
    wktbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'           AS [KY_ReceivedDateTime]
    , wktbl.WORKCLOSEDUTCDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'   AS [KY_PutAwayDateTime]
    --, wktbl.WORKID
    , wktbl.ORDERNUM                                                                        AS [OrderNum]
    , wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID                      AS [SKU]
    , CAST(wkln.QTYWORK AS INT)                                                             AS [Qty]
FROM
    [DAX_PROD].[dbo].WHSWORKTABLE wktbl
    JOIN [DAX_PROD].[dbo].WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wkln.LINENUM = 1
    JOIN [DAX_PROD].[dbo].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
WHERE
    wktbl.WORKTRANSTYPE = 1
    AND wktbl.WORKSTATUS = 4 -- Closed
    AND wktbl.INVENTSITEID = 'HA USA'
    AND wktbl.INVENTLOCATIONID = '4010'
    AND wktbl.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 05:00:00 AM'
*/
ORDER BY
    wkln.WORKID

