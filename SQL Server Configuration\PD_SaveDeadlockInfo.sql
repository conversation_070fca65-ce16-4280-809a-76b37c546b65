/*
https://blog.sqlauthority.com/2023/06/19/understanding-sql-server-deadlocks-a-beginners-guide/
https://learn.microsoft.com/en-us/sql/relational-databases/sql-server-deadlocks-guide?view=sql-server-ver16&source=recommendations

The following query can view all deadlock events captured by the system_health session ring buffer:
SQL Server 2012 or up
*/


SELECT xdr.value('@timestamp', 'datetime') AS [Date],
    xdr.query('.') AS [Event_Data]
FROM (SELECT CAST([target_data] AS XML) AS Target_Data
            FROM sys.dm_xe_session_targets AS xt
            INNER JOIN sys.dm_xe_sessions AS xs ON xs.address = xt.event_session_address
            WHERE xs.name = N'system_health'
              AND xt.target_name = N'ring_buffer'
    ) AS XML_Data
CROSS APPLY Target_Data.nodes('RingBufferTarget/event[@name="xml_deadlock_report"]') 
AS XEventData(xdr)
ORDER BY [Date] DESC;