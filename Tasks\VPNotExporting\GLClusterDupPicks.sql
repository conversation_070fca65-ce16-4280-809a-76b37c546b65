


SELECT
    GLMultP.CLUSTERID
    , GLMultP.CreatedOn
    , GLMultP.WMSLOCATIONID
    , GLPicks.WOR<PERSON><PERSON>
    , GLPicks.MODIFIEDBY
    --, COUNT( GLPicks.WMSLOCATIONID )
FROM
(
SELECT
    ct.CLUSTERID
    , CAST( 
    CASE    WHEN DATEPART( mm, ct.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, ct.CREATEDDATETIME ) -- Daylight Savings Months 
            WHEN DATEPART( mm, ct.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, ct.CREATEDDATETIME ) -- No DST
            WHEN DATEPART( mm, ct.CREATEDDATETIME ) = 3 
            THEN CASE   WHEN DATEPART( dd, ct.CREATEDDATETIME ) < 8 OR DATEPART( dd, ct.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, ct.CREATEDDATETIME ) -- No DST
                        WHEN DATEPART( dd, ct.CREATEDDATETIME ) - DATEPART( w, ct.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, ct.CREATEDDATETIME ) -- Last Sunday after March 8
                        ELSE DATEADD(hh, - 5, ct.CREATEDDATETIME )
                END
            WHEN DATEPART( dd, ct.CREATEDDATETIME ) - DATEPART( w, ct.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
            THEN DATEADD( hh, - 5, ct.CREATEDDATETIME )
            ELSE DATEADD( hh, - 4, ct.CREATEDDATETIME )
    END   AS DATE )  AS CreatedOn
    , ct.CLUSTERPROFILEID
    , wkln.WMSLOCATIONID
    , COUNT( * ) AS TotalPicks
FROM
    WHSWORKCLUSTERTABLE ct
    LEFT JOIN WHSWORKCLUSTERLINE cl ON ct.CLUSTERID = cl.CLUSTERID AND ct.[PARTITION] = cl.[PARTITION] AND ct.DATAAREAID = cl.DATAAREAID
    LEFT JOIN WHSWORKLINE wkln ON wkln.WORKID = cl.WORKID AND wkln.[PARTITION] = cl.[PARTITION] AND wkln.DATAAREAID = cl.DATAAREAID
    LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wktbl.PARTITION = wkln.PARTITION AND wktbl.DATAAREAID = wkln.DATAAREAID
    LEFT JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.[PARTITION] = wkln.[PARTITION] AND loc.DATAAREAID = wkln.DATAAREAID
WHERE
    wkln.WORKSTATUS BETWEEN 1 AND 4 -- Not Open
    AND ISNULL( ct.CLUSTERID, '' ) <> '' -- Already clustered
    AND loc.LOCPROFILEID LIKE 'PalletPicking'
    AND wktbl.WORKSTATUS < 5 -- Not Canceled
    AND ct.CREATEDDATETIME > GETUTCDATE() - 3
GROUP BY
    ct.CLUSTERID, ct.CREATEDDATETIME, ct.CLUSTERPROFILEID, wkln.WMSLOCATIONID
HAVING
    COUNT( * ) > 1 -- More than One pick from Gaylords on the same cluster
) AS GLMultP
LEFT JOIN
(
SELECT
    ct.CLUSTERID
    , wkln.WORKID
    , wkln.WMSLOCATIONID
    , wkln.MODIFIEDBY
FROM
    WHSWORKCLUSTERTABLE ct
    LEFT JOIN WHSWORKCLUSTERLINE cl ON ct.CLUSTERID = cl.CLUSTERID AND ct.[PARTITION] = cl.[PARTITION] AND ct.DATAAREAID = cl.DATAAREAID
    LEFT JOIN WHSWORKLINE wkln ON wkln.WORKID = cl.WORKID AND wkln.[PARTITION] = cl.[PARTITION] AND wkln.DATAAREAID = cl.DATAAREAID
    LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wktbl.PARTITION = wkln.PARTITION AND wktbl.DATAAREAID = wkln.DATAAREAID
    LEFT JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.[PARTITION] = wkln.[PARTITION] AND loc.DATAAREAID = wkln.DATAAREAID
WHERE
    wkln.WORKSTATUS BETWEEN 1 AND 4 -- Not Open
    AND ISNULL( ct.CLUSTERID, '' ) <> '' -- Already clustered
    AND loc.LOCPROFILEID LIKE 'PalletPicking'
    AND wktbl.WORKSTATUS < 5 -- Not Canceled
    AND ct.CREATEDDATETIME > GETUTCDATE() - 3
) AS GLPicks
ON GLMultP.CLUSTERID = GLPicks. CLUSTERID AND GLMultP.WMSLOCATIONID = GLPicks.WMSLOCATIONID
/*GROUP BY
    GLMultP.ClusterID, GLPicks.WORKID,  GLPicks.MODIFIEDBY, GLMultP.WMSLOCATIONID
HAVING
    COUNT( DISTINCT GLPicks.WMSLOCATIONID ) > 1 */