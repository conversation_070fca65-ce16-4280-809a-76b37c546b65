

SELECT 
    *
FROM
(
    SELECT
        price.ITEMSKU AS SKU
        , CAST([PRICE] AS DECIMAL(18, 2) ) AS SalesPrice
        , price.PRICEDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS PriceDateTime
        , po.MAXPODate
    FROM
        [DAX_PROD].[dbo].[HAITEMSKUPRICE] price
        --Get the MAX pricedatetime for a SKU--
    JOIN
    (
        SELECT
            MAX(pricedatetime) AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS PriceDateTime
            , itemsku
        FROM [DAX_PROD].[dbo].[HAITEMSKUPRICE]
        WHERE CAST(PRICEDATETIME AS date) <= GETDATE()
        GROUP BY itemsku
    ) sub on price.ITEMSKU = sub.ITEMSKU AND price.PRICEDATETIME = sub.PriceDateTime
    --Get the MAX PO createdatetime for a SKU--
    LEFT JOIN
    (
        SELECT
            (ITEMID + '-' + dim.INVENTCOLORID + '-' + dim.INVENTSIZEID) AS SKU
            , MAX(pl.createddatetime) AS MAXPODate
            FROM 
                [DAX_PROD].[dbo].[PURCHLINE] pl
            JOIN 
                [DAX_PROD].[dbo].[INVENTDIM] dim on pl.INVENTDIMID = dim.inventdimid AND pl.DATAAREAID = dim.DATAAREAID AND pl.PARTITION = dim.PARTITION
            GROUP BY 
                (ITEMID + '-' + dim.INVENTCOLORID + '-' + dim.INVENTSIZEID)
    ) po on po.SKU = price.ITEMSKU
WHERE 
    price.DataAreaId = N'ha'
    AND price.Partition = 5637144576
    AND sub.PRICEDATETIME <= GETDATE() -- Activation Date must be Today or Prior
    AND po.MAXPODate < GETDATE() -- MAX PO must be in the Past
    GROUP BY price.ITEMSKU, PRICE, price.PRICEDATETIME, po.MAXPODate
) main
WHERE 
    --RIGHT(CAST(main.SalesPrice as decimal(18,2)),2) = '79'  --filter to Final Sale pricing
    RIGHT(main.SalesPrice, 2) = '79'  --filter to Final Sale pricing
ORDER BY
    MAXPODate DESC

--sp_columns HAITEMSKUPRICE