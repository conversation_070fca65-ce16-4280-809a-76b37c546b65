

--Waves Processing times, with batch jobs
-- Will tweak a little bit for reporting(11/25/2024)

DECLARE @DaystoReport INT = 6; -- Days to report, default 10 days
USE DAX_PROD;

SELECT 
	WPT.Wave
	--, CASE WHEN WPT.Attribute1 = 'AutoBagger' THEN 'Yes' ELSE 'No' END AS AB
	--, WPT.PreviousWaveFinishedAt 	AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS PreviousWaveHeldtime
	, WPT.ProcessingDateTime 		AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS ProcDateTime
	--, WPT.HeldDateTime 				AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS HeldDateTime
	, CAST( DATEDIFF( SECOND, WPT.PreviousWaveFinishedAt, WPT.ProcessingDateTime ) / 60.0 AS DECIMAL(10, 2) ) AS ProcessingGap
	, BJT.CREATEDBY			AS 'ProcessedBy'
	, WPT.Template
    , BJT.TimesFailed
    , WPT.Shipments
    --, WPT.LoadLines
    , CONVERT(decimal, WPT.Units, 10 ) AS TotalUnits
	, WVR.Replen AS ReplenCartons
    , WPT.ProcTimeMinutes
    --, WPT.ProcTimeSeconds
    , CAST(BJT.BatchJobTime/60 AS INT ) 			AS BatchJobsTime
	, CAST(AB_DocGen.AB_DocGenTime/60 AS INT ) 		AS AB_DocGenTime
	--WPT.ProcTimeSeconds / WPT.LoadLines AS TimePerLoadLine, WPT.ProcTimeSeconds / WPT.Shipments AS TimePerShipment
FROM
(
SELECT 
	wt.WAVEID							AS Wave, 
	wt.WAVEATTRIBUTECODE				AS Attribute1,
	wt.WAVETEMPLATENAME					AS Template,
	COUNT( DISTINCT wl.shipmentid ) 	AS Shipments,
	COUNT(  wl.LOADID )					AS LoadLines,
	SUM( ll.qty )						AS Units,
	LAG(wt.HELDUTCDATETIME) OVER (ORDER BY wt.EXECUTINGUTCDATETIME) AS PreviousWaveFinishedAt,
	wt.EXECUTINGUTCDATETIME				AS ProcessingDateTime, 
	wt.HELDUTCDATETIME					AS HeldDateTime
	, DATEDIFF( MINUTE, wt.EXECUTINGUTCDATETIME, wt.HELDUTCDATETIME ) AS ProcTimeMinutes
	, DATEDIFF( SECOND, wt.EXECUTINGUTCDATETIME, wt.HELDUTCDATETIME ) AS ProcTimeSeconds
	--CONVERT( varchar( 5 ), DATEADD( Minute, DATEDIFF( MINUTE, wt.EXECUTINGUTCDATETIME, wt.HELDUTCDATETIME ), 0 ), 114 ) AS ProcTime
FROM WHSWAVETABLE wt
LEFT JOIN WHSWAVELINE wl ON wl.WAVEID = wt.WAVEID AND wl.DATAAREAID = 'ha' AND wl.[PARTITION] = wt.[PARTITION]
LEFT JOIN WHSLOADLINE LL ON ll.SHIPMENTID = wl.SHIPMENTID AND ll.DATAAREAID = 'ha' AND ll.[PARTITION] = wl.[PARTITION] -- AND ll.ORDERNUM = wl.ORDERNUM( slower, no index on ordernum ) 
WHERE
	wt.EXECUTINGUTCDATETIME > GETUTCDATE() - @DaystoReport
	--wt.MODIFIEDDATETIME > GETUTCDATE() - @DaystoReport
	AND wt.WAVESTATUS > 1
	--AND wt.WAVETEMPLATENAME IN ( 'REGULAR DIRECT', 'Regular Dir - Gift', 'SINGLE DIRECT' )
	AND wt.[DESCRIPTION] NOT LIKE 'Replenishment%'
GROUP BY wt.WAVEID, wt.WAVEATTRIBUTECODE, wt.WAVETEMPLATENAME, wt.EXECUTINGUTCDATETIME, wt.HELDUTCDATETIME
) AS WPT -- Waves Processing Times
LEFT JOIN
(
	SELECT 
		SUBSTRING( bj.CAPTION, CHARINDEX( 'WV00', bj.CAPTION ), 11 ) AS Wave
		, SUM( DATEDIFF( SECOND, bj.STARTDATETIME, bj.ENDDATETIME ) )  AS BatchJobTime
		, SUM( CASE WHEN bj.CAPTION LIKE '%Start Step%' THEN 1 ELSE 0 END ) - 1 AS TimesFailed -- Start Step should be present only once
		, bj.CREATEDBY	
		--SUM( CASE WHEN bj.Status = 8 THEN 1 ELSE 0 END ) AS TimesFailed 
		--DATEDIFF( Minute, STARTDATETIME, ENDDATETIME ) AS BatchJobsTime 
	FROM 
		BATCHJOB bj
	WHERE 
		STATUS IN ( 3, 4, 8 ) -- Error, Ended  & Canceled
		AND bj.CAPTION Like 'Execute Wave%'
		--AND bj.CAPTION NOT LIKE 'Autobagger%'
		--AND bj.STARTDATETIME BETWEEN '4/25/22 13:00:00' AND '4/25/22 14:00:00'
		AND bj.STARTDATETIME BETWEEN GETUTCDATE() - @DaystoReport AND GETUTCDATE() 
	GROUP BY SUBSTRING( CAPTION, CHARINDEX( 'WV00', CAPTION ), 11 ), CREATEDBY
) AS BJT -- Total batch jobs time per wave
ON WPT.Wave = BJT.Wave
LEFT JOIN
(
	SELECT 
		SUBSTRING( bj.CAPTION, CHARINDEX( 'WV00', bj.CAPTION ), 11 ) AS Wave
		, SUM( DATEDIFF( SECOND, bj.STARTDATETIME, bj.ENDDATETIME ) )  AS AB_DocGenTime
	FROM 
		BATCHJOB bj
	WHERE 
		STATUS IN ( 3, 4, 8 ) -- Error, Ended  & Canceled
		AND bj.CAPTION Like 'Autobagger document generation%'
		--AND bj.CAPTION NOT LIKE 'Autobagger%'
		--AND bj.STARTDATETIME BETWEEN '4/25/22 13:00:00' AND '4/25/22 14:00:00'
		AND bj.STARTDATETIME BETWEEN GETUTCDATE() - @DaystoReport AND GETUTCDATE() 
	GROUP BY SUBSTRING( CAPTION, CHARINDEX( 'WV00', CAPTION ), 11 )
) AS AB_DocGen -- Total batch jobs time per wave
ON WPT.Wave = AB_DocGen.Wave
LEFT JOIN
(
	SELECT wavtblr.DEMANDWAVEID As Wave, COUNT( wkt.WORKID ) AS Replen
	FROM WHSWAVETABLE wavtblr  -- Replenishments waves
	LEFT JOIN WHSWAVETABLE wavtbl ON wavtblr.DEMANDWAVEID = wavtbl.WAVEID AND wavtbl.DATAAREAID = 'ha' AND wavtbl.[PARTITION] = wavtblr.[PARTITION]
	LEFT JOIN WHSWORKTABLE wkt ON wavtblr.WAVEID = wkt.WAVEID AND wkt.DATAAREAID = 'ha' AND wkt.[PARTITION] = wavtblr.[PARTITION] -- Work from replenishment wave
	WHERE
		wavtbl.MODIFIEDDATETIME > GETUTCDATE() - @DaystoReport
		AND wavtbl.WAVESTATUS > 1
		--AND wavtbl.WAVETEMPLATENAME IN ( 'REGULAR DIRECT', 'Regular Dir - Gift', 'SINGLE DIRECT' )
		AND wavtblr.[DESCRIPTION] LIKE 'Replenishment%'
	GROUP BY wavtblr.DEMANDWAVEID
) AS WVR
ON WPT.Wave = WVR.WAVE
--WHERE WPT.Shipments > 0
ORDER BY 
	--WPT.WAVE
	WPT.ProcessingDateTime
    