/*
On hand inventory(Only picking area).
Modifying to get only Gaylords
*/

USE DAX_PROD;

/*

join inventtable it on vt.itemid=it.itemid
join ecoresproduct ep on it.itemid=ep.displayproductnumber
join ecoresproductcategory epc on epc.product=it.product
join ECORESCATEGORYHIERARCHY ech on epc.CATEGORYHIERARCHY=ech.recid
join ecorescategory low1 on epc.CATEGORY = low1.recid
join ecorescategory class on low1.PARENTCATEGORY=class.recid
join ECORESCATEGORY dept on class.PARENTCATEGORY=dept.recid
join ECORESCATEGORY div on dept.PARENTCATEGORY=div.recid
*/
SELECT
    isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid        AS [SKU]
    , loc.wmslocationid	                                                    AS [Location]
    --, loc.locprofileid	                                                    AS [LocProfile]
    , loc.zoneid			                                                AS [ZoneId]
    /*, isum.itemid					                                        AS [Item]
    , idim.inventcolorid			                                        AS [Color]
    , idim.inventsizeid			                                            AS [Size]
    */
    , prodtrans.DESCRIPTION AS 'Description'
    , idim.INVENTSTATUSID                                                   AS [Status]
    , CONVERT( DECIMAL( 20,0), isum.physicalinvent )			            AS [Qty]
    , CONVERT( DECIMAL( 20,0), isum.reservphysical )			            AS [Reserved] 
    , CASE WHEN ( hagpft.AVAILABILITY = 'in stock' OR  isum.RESERVPHYSICAL > 0 ) THEN 'Yes' ELSE 'No' END  AS [Online]
    
     --CASE WHEN CAST( hagpft.DATETIMESTAMP AS DATE ) = CAST( GETDATE() AS DATE ) THEN 'Yes' ELSE 'No' END AS Online
FROM 
    wmslocation loc
    INNER JOIN inventdim idim                       WITH (NOLOCK) ON loc.WMSLOCATIONID  = idim.WMSLOCATIONID    AND loc.DATAAREAID = idim.DATAAREAID AND loc.[PARTITION] = idim.[PARTITION] AND idim.INVENTLOCATIONID = '4010'
    INNER JOIN inventsum isum                       WITH (NOLOCK) ON isum.inventdimid   = idim.inventdimid      AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION] AND isum.PHYSICALINVENT > 0
    INNER JOIN INVENTTABLE itbl                     WITH (NOLOCK) ON isum.ITEMID        = itbl.ITEMID           AND itbl.DATAAREAID = isum.DATAAREAID AND itbl.[PARTITION] = isum.[PARTITION]
    INNER JOIN ECORESPRODUCTTRANSLATION prodtrans   WITH (NOLOCK) ON itbl.PRODUCT       = prodtrans.PRODUCT     AND prodtrans.[PARTITION] = itbl.[PARTITION]
    LEFT OUTER JOIN hagoogleproductfeedtable hagpft WITH (NOLOCK) ON hagpft.MPN         = isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid
WHERE 
    loc.inventlocationid = '4010' 
    AND loc.locprofileid = 'PalletPicking'
    --AND loc.zoneid NOT IN ( 'Current Pick')
    --AND idim.INVENTSTATUSID <> 'Available'
    --AND isum.physicalinvent > 0 -- Only locations with inventory to be considered as candidates
ORDER BY 
    loc.SORTCODE DESC
 --Item, Color, Size

--sp_columns inventsum

--sp_columns wmslocation

