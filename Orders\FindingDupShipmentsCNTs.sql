
SELECT 
    o.OrderNum, s.Ship<PERSON>d, c.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>METHOD
FROM
    whsshipmenttable s
    INNER JOIN WHSCONTAINERTABLE c  ON c.SHIPMENTID = s.SHIPMENTID AND c.DATAAREAID = 'ha' AND c.[PARTITION] = s.[PARTITION]
    INNER JOIN 
    ( -- Orders with multiple shipments
        SELECT 
            OrderNum, COUNT(*) AS shipment_count
        FROM 
            whsshipmenttable
        GROUP BY 
            OrderNum
        HAVING 
            COUNT(*) > 1
    ) o ON o.OrderNum = s.OrderNum
WHERE EXISTS (
  SELECT 1
  FROM whsshipmenttable s2
  INNER JOIN WHSCONTAINERTABLE c2  ON c2.SHIPMENTID = s2.SHIPMENTID AND c2.DATAAREAID = 'ha' AND c2.[PARTITION] = s2.[PARTITION] 
  -- Filter within the subquery using WHERE
  WHERE 
    s2.OrderNum = s.OrderNum AND c2.HAWHSSHIPMETHOD <> c.HAWHSSHIPMETHOD
  GROUP BY 
    s2.OrderNum, c2.HAW<PERSON>SHIPMETHOD
  HAVING COUNT(*) > 1
)
ORDER BY
    ORDERNUM, s.ShipmentID
;