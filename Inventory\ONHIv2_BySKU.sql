/*
On hand inventory(Only picking area). Adding some more useful columns
Modified on 12/17/2024 to give the inventory to tan auditor, per <PERSON>'s requests
*/

USE DAX_PROD;

/*

join inventtable it on vt.itemid=it.itemid
join ecoresproduct ep on it.itemid=ep.displayproductnumber
join ecoresproductcategory epc on epc.product=it.product
join ECORESCATEGORYHIERARCHY ech on epc.CATEGORYHIERARCHY=ech.recid
join ecorescategory low1 on epc.CATEGORY = low1.recid
join ecorescategory class on low1.PARENTCATEGORY=class.recid
join ECORESCATEGORY dept on class.PARENTCATEGORY=dept.recid
join ECORESCATEGORY div on dept.PARENTCATEGORY=div.recid
*/
SELECT  
    OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid            AS [SKU]
    , OH.[Description]
    --, wmslocation.zoneid			                                            AS [ZoneId]
    /*, wmslocation.locprofileid	                                            AS [LocProfile]
    , OH.itemid					                                            AS [Item]
    , OH.inventcolorid			                                            AS [Color]
    , OH.inventsizeid			                                            AS [Size]*/
    , loc.wmslocationid	                                                    AS [Location]
    , loc.LOCPROFILEID                                                      AS [LocationProfile]
    , OH.INVENTSTATUSID                                                     AS [Status]
    , CONVERT( DECIMAL( 20,0), OH.physicalinvent )			                AS [Qty]
    , CONVERT( DECIMAL( 20,0), OH.reservphysical )			                AS [Reserved]
    
    --, CASE WHEN ( hagpft.AVAILABILITY = 'in stock' OR  OH.RESERVPHYSICAL > 0 ) THEN 'Yes' ELSE 'No' END  AS [Online]
    --, CAST( OH.LastPickWC AS DATE )                                         AS [LastMovement]

    --, OH.LastPickWC AT TIME ZONE 'UTC' AT TIME ZONE 'Pacific Standard Time'   AS [Isum_ModDT_PDT]
    --, OH.IDIM_ModDT AT TIME ZONE 'UTC' AT TIME ZONE 'Pacific Standard Time'   AS [Idim_ModDT_PDT]
    --, Oh.idim_ModBy
    , DATEDIFF( DD, OH.LastPickWC, GETUTCDATE() )                              AS [SittingDays]
FROM wmslocation loc
JOIN 
    ( 
        SELECT 
            isum.itemid
            , idim.inventcolorid
            , idim.inventsizeid
            , prodtrans.DESCRIPTION AS 'Description'
            , isum.physicalinvent
            , isum.reservphysical
            , idim.wmslocationid
            , idim.INVENTSTATUSID
            , MAX( isum.MODIFIEDDATETIME ) AS LastPickWC
            , idim.MODIFIEDBY         AS [idim_ModBy]
            , MAX(idim.MODIFIEDDATETIME)     AS IDIM_ModDT

        FROM inventdim idim
            JOIN inventsum                  isum        WITH (NOLOCK) ON isum.inventdimid   = idim.inventdimid      AND isum.DATAAREAID         = idim.DATAAREAID AND isum.[PARTITION]  = idim.[PARTITION] AND idim.inventlocationid = '4010'
            JOIN WMSLOCATION                loc         WITH (NOLOCK) ON loc.WMSLOCATIONID  = idim.WMSLOCATIONID    AND loc.DATAAREAID          = idim.DATAAREAID AND loc.[PARTITION]   = isum.[PARTITION] AND isum.PHYSICALINVENT <> 0 -- Including negative values
            JOIN INVENTTABLE                itbl        WITH (NOLOCK) ON isum.ITEMID        = itbl.ITEMID           AND itbl.DATAAREAID         = isum.DATAAREAID AND itbl.[PARTITION]  = isum.[PARTITION]
            JOIN ECORESPRODUCTTRANSLATION   prodtrans   WITH (NOLOCK) ON itbl.PRODUCT       = prodtrans.PRODUCT     AND prodtrans.[PARTITION]   = itbl.[PARTITION]
        WHERE 
            idim.INVENTSITEID = 'HA USA' 
            --AND isum.PHYSICALINVENT < 99999 
            --AND isum.physicalinvent <> 0 
            AND loc.LOCPROFILEID IN (  'W001', 'Picking', 'Picking A','Picking D', 'PalletPicking', 'Bulk', 'Overflow' ) 
            AND isum.ITEMID NOT IN ( '30991', '3333', '9999', '9997' ) 
        GROUP BY
            idim.WMSLOCATIONID, isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, isum.PHYSICALINVENT, isum.RESERVPHYSICAL, idim.INVENTSTATUSID, prodtrans.[DESCRIPTION], idim.MODIFIEDBY
    ) AS OH -- Pulls location with inventory. Avoiding duplicate records.
ON loc.wmslocationID = OH.wmslocationid
LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid
WHERE 
    loc.inventlocationid = '4010' 
    --AND wmslocation.locprofileid LIKE '%Picking%'
    AND loc.zoneid NOT IN ( 'Current Pick') -- Excluding GCs
    AND OH.INVENTSTATUSID NOT IN ('BulkReduce') -- Adjusted out, usually
    --AND OH.physicalinvent > 0 -- Only locations with inventory to be considered as candidates
    --AND OH.LastPickWC BETWEEN '2025-02-04 15:55:00.000' AND '2025-02-04 16:55:00.000'
ORDER BY 
    --Location
     SKU ASC 
    --, SittingDays ASC
 --Item, Color, Size

--sp_indexes inventsum

/*

-- Robert Wise(Rob, SSNW) - Inventory Issue from Feb 4th, 2025

select  res.*, id.* from WHSINVENTRESERVE res
	inner join INVENTDIM id on id.INVENTDIMID = res.INVENTDIMID
where res.itemid = '33175'
	and id.inventsizeid = '120'
	and id.INVENTCOLORID = 'S74'
	and id.WMSLOCATIONID = '69-086V'
*/