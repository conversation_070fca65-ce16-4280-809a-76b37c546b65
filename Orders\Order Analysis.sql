--Order Analysis
SELECT 
	 SALESID ,MIN(EMAIL) AS email, MIN([Order Date]) AS [Order Date]
	, SUM(WavedCnt) AS WavedCnt           , SUM(WavedUnits) AS WavedUnits
	, SUM(StuckCnt) AS StuckCnt           , SUM(StuckUnits) AS StuckUnits
	, SUM(OpenCnts) AS OpenCnts           , SUM(OpenUnits) AS OpenUnits
	, SUM(ShippedYesterdayCnt) AS ShippedYesterdayCnt, SUM(ShippedYesterdayUnits) AS ShippedYesterdayUnits
	, SUM(ShippedTodayCnt) AS ShippedTodayCnt    , SUM(ShippedTodayUnits) AS ShippedTodayUnits
FROM (
SELECT
	 st.SALESID,MIN(st.EMAIL) AS email,MIN(st.HAORDERDATETIME) AS [Order Date]
	,0 AS WavedCnt           , 0 AS WavedUnits
	,0 AS StuckCnt           , 0 AS StuckUnits
	,1 AS OpenCnts           , SUM(reserved.qty) AS OpenUnits
	,0 AS ShippedYesterdayCnt, 0 AS ShippedYesterdayUnits
	,0 AS ShippedTodayCnt    , 0 AS ShippedTodayUnits
FROM
	salesline sl
	INNER JOIN salestable st			ON   st.DATAAREAID = sl.DATAAREAID AND   st.PARTITION = sl.PARTITION AND st.salesid = sl.salesid  
	INNER JOIN INVENTTRANSORIGIN o		ON   sl.DATAAREAID =  o.DATAAREAID AND   sl.PARTITION =  o.PARTITION AND sl.INVENTTRANSID = o.INVENTTRANSID  
	INNER JOIN inventdim slid			ON slid.DATAAREAID = sl.DATAAREAID AND slid.PARTITION = sl.PARTITION AND slid.inventdimid = sl.INVENTDIMID AND slid.INVENTLOCATIONID = '4010'
	CROSS APPLY (
		SELECT -SUM(QTY) AS qty	FROM INVENTTRANS 
		WHERE INVENTTRANS.DATAAREAID = o.DATAAREAID AND INVENTTRANS.PARTITION = o.PARTITION 
			AND INVENTTRANS.INVENTTRANSORIGIN = o.RECID AND InventTrans.STATUSISSUE = 4
	) Reserved
WHERE sl.PARTITION = 5637144576 AND sl.DATAAREAID = 'ha'
	AND sl.SALESSTATUS = 1
	AND sl.REMAINSALESPHYSICAL > 0
	AND sl.ITEMID not in ('SHIPPING')
	AND Reserved.qty > 0
	AND sl.CREATEDDATETIME <= DATEADD(day, -1, GETDATE())
	AND not exists (
		SELECT 1 FROM WHSLOADLINE ll 
			JOIN WHSCONTAINERLINE cl ON ll.RECID = cl.LOADLINE AND cl.DATAAREAID = ll.DATAAREAID AND cl.PARTITION = ll.PARTITION
			JOIN HASHIPPEDCARTONSTAGING sc ON sc.CARTONID = cl.CONTAINERID AND sc.DATAAREAID = cl.DATAAREAID AND sc.PARTITION = cl.PARTITION AND sc.TRACKINGNUMBER != ''
		WHERE ll.INVENTTRANSID = sl.INVENTTRANSID AND ll.ORDERNUM = sl.SALESID AND ll.DATAAREAID = sl.DATAAREAID AND ll.PARTITION = sl.PARTITION)
GROUP BY
	st.SALESID
UNION ALL
SELECT
	 st.SALESID,MIN(st.EMAIL) AS email,MIN(st.HAORDERDATETIME) AS [Order Date]
	,1 AS WavedCnt           , isnull(SUM(releASedworkqty.qtyremain), 0) AS WavedUnits
	,0 AS StuckCnt           , 0 AS StuckUnits
	,0 AS OpenCnts           , 0 AS OpenUnits
	,0 AS ShippedYesterdayCnt, 0 AS ShippedYesterdayUnits
	,0 AS ShippedTodayCnt    , 0 AS ShippedTodayUnits
FROM
	salesline sl
	INNER JOIN salestable st			ON   st.DATAAREAID = sl.DATAAREAID AND   st.PARTITION = sl.PARTITION AND st.salesid = sl.salesid  
	INNER JOIN INVENTTRANSORIGIN o		ON   sl.DATAAREAID =  o.DATAAREAID AND   sl.PARTITION =  o.PARTITION AND sl.INVENTTRANSID = o.INVENTTRANSID  
	INNER JOIN inventdim slid			ON slid.DATAAREAID = sl.DATAAREAID AND slid.PARTITION = sl.PARTITION AND slid.inventdimid = sl.INVENTDIMID AND slid.INVENTLOCATIONID = '4010'
	CROSS APPLY (
       SELECT MIN(s.WAVEID) AS WaveId,SUM(QtyRemain) AS qtyremain FROM WHSLOADLINE ll
                    JOIN WHSWORKLINE       wl ON wl.DATAAREAID = ll.DATAAREAID AND wl.PARTITION = ll.PARTITION AND wl.LOADLINEREFRECID = ll.RECID AND wl.WORKSTATUS < 4 AND wl.WORKTYPE = 1
                    JOIN WHSLOADTABLE      lt ON lt.DATAAREAID = ll.DATAAREAID AND lt.PARTITION = ll.PARTITION AND lt.loadid = ll.LOADID
                    JOIN WHSSHIPMENTTABLE   s ON  s.DATAAREAID = ll.DATAAREAID AND  s.PARTITION = ll.PARTITION AND s.SHIPMENTID = ll.SHIPMENTID
                    JOIN WHSWAVETABLE       w ON  w.DATAAREAID =  s.DATAAREAID AND  w.PARTITION =  s.PARTITION AND w.WAVEID = s.WAVEID --AND w.WAVESTATUS > 2
        WHERE ll.DATAAREAID = sl.DATAAREAID AND ll.PARTITION = sl.PARTITION
		  AND ll.INVENTTRANSID = sl.INVENTTRANSID
          AND ll.ORDERNUM = sl.SALESID
	) AS releasedworkqty
WHERE sl.PARTITION = 5637144576 AND sl.DATAAREAID = 'ha'
	AND sl.SALESSTATUS = 1
	AND sl.REMAINSALESPHYSICAL > 0
	AND sl.ITEMID not in ('SHIPPING')
	AND ISNULL(releASedworkqty.waveid,'') <> ''
	AND st.MCRORDERSTOPPED = 0
	AND not exists (SELECT 1 FROM WHSLOADLINE ll JOIN WHSCONTAINERLINE cl ON ll.RECID = cl.LOADLINE AND cl.DATAAREAID = ll.DATAAREAID AND cl.PARTITION = ll.PARTITION
       JOIN HASHIPPEDCARTONSTAGING sc ON sc.CARTONID = cl.CONTAINERID AND sc.DATAAREAID = cl.DATAAREAID AND sc.PARTITION = cl.PARTITION AND sc.TRACKINGNUMBER != ''
       WHERE ll.INVENTTRANSID = sl.INVENTTRANSID AND ll.ORDERNUM = sl.SALESID AND ll.DATAAREAID = sl.DATAAREAID AND ll.PARTITION = sl.PARTITION)
GROUP BY
	st.SALESID
uniON ALL
SELECT
	 st.SALESID,MIN(st.EMAIL) AS email,MIN(st.HAORDERDATETIME) AS [Order Date]
	,0 AS WavedCnt           , 0 AS WavedUnits
	,1 AS StuckCnt           , SUM(reserved.qty) AS StuckUnits
	,0 AS OpenCnts           , 0 AS OpenUnits
	,0 AS ShippedYesterdayCnt, 0 AS ShippedYesterdayUnits
	,0 AS ShippedTodayCnt    , 0 AS ShippedTodayUnits
FROM
	salesline sl
	INNER JOIN salestable st			ON   st.DATAAREAID = sl.DATAAREAID AND   st.PARTITION = sl.PARTITION AND st.salesid = sl.salesid  
	INNER JOIN INVENTTRANSORIGIN o		ON   sl.DATAAREAID =  o.DATAAREAID AND   sl.PARTITION =  o.PARTITION AND sl.INVENTTRANSID = o.INVENTTRANSID  
	INNER JOIN inventdim slid			ON slid.DATAAREAID = sl.DATAAREAID AND slid.PARTITION = sl.PARTITION AND slid.inventdimid = sl.INVENTDIMID AND slid.INVENTLOCATIONID = '4010'
	CROSS APPLY (
		SELECT -SUM(QTY) AS qty
				FROM INVENTTRANS 
		WHERE INVENTTRANS.DATAAREAID = o.DATAAREAID AND INVENTTRANS.PARTITION = o.PARTITION 
			AND INVENTTRANS.INVENTTRANSORIGIN = o.RECID AND InventTrans.STATUSISSUE = 4
	) Reserved
WHERE sl.PARTITION = 5637144576 AND sl.DATAAREAID = 'ha'
	AND sl.SALESSTATUS = 1
	AND sl.REMAINSALESPHYSICAL > 0
	AND sl.ITEMID not in ('SHIPPING')
	AND st.MCRORDERSTOPPED = 1
	AND Reserved.qty > 0
	AND sl.CREATEDDATETIME <= DATEADD(day, -1, GETDATE())
	AND not exists (SELECT 1 FROM WHSLOADLINE ll JOIN WHSCONTAINERLINE cl ON ll.RECID = cl.LOADLINE AND cl.DATAAREAID = ll.DATAAREAID AND cl.PARTITION = ll.PARTITION
       JOIN HASHIPPEDCARTONSTAGING sc ON sc.CARTONID = cl.CONTAINERID AND sc.DATAAREAID = cl.DATAAREAID AND sc.PARTITION = cl.PARTITION AND sc.TRACKINGNUMBER != ''
       WHERE ll.INVENTTRANSID = sl.INVENTTRANSID AND ll.ORDERNUM = sl.SALESID AND ll.DATAAREAID = sl.DATAAREAID AND ll.PARTITION = sl.PARTITION)
GROUP BY
	st.SALESID
uniON ALL
SELECT
	 st.SALESID,MIN(st.EMAIL) AS email,MIN(st.HAORDERDATETIME) AS [Order Date]
	,0 AS WavedCnt           , 0 AS WavedUnits
	,0 AS StuckCnt           , 0 AS StuckUnits
	,0 AS OpenCnts           , 0 AS OpenUnits
	,1 AS ShippedYesterdayCnt, isnull(SUM(ll.PACKINGQTY), 0) AS ShippedYesterdayUnits
	,0 AS ShippedTodayCnt    , 0 AS ShippedTodayUnits
FROM
	salesline sl
	INNER JOIN salestable st			ON   st.DATAAREAID = sl.DATAAREAID AND   st.PARTITION = sl.PARTITION AND st.salesid = sl.salesid  
	INNER JOIN INVENTTRANSORIGIN o		ON   sl.DATAAREAID =  o.DATAAREAID AND   sl.PARTITION =  o.PARTITION AND sl.INVENTTRANSID = o.INVENTTRANSID  
	INNER JOIN inventdim slid			ON slid.DATAAREAID = sl.DATAAREAID AND slid.PARTITION = sl.PARTITION AND slid.inventdimid = sl.INVENTDIMID AND slid.INVENTLOCATIONID = '4010'
	INNER JOIN WHSLOADLINE ll			ON   ll.DATAAREAID = sl.DATAAREAID AND   ll.PARTITION = sl.PARTITION AND ll.INVENTTRANSID = sl.INVENTTRANSID AND ll.ORDERNUM = sl.SALESID
    JOIN WHSCONTAINERLINE			 cl ON   cl.DATAAREAID = ll.DATAAREAID AND   cl.PARTITION = ll.PARTITION AND ll.RECID = cl.LOADLINE
	JOIN HASHIPPEDCARTONSTAGING		 sc ON   sc.DATAAREAID = cl.DATAAREAID AND   sc.PARTITION = cl.PARTITION AND sc.CARTONID = cl.CONTAINERID AND sc.TRACKINGNUMBER != ''
    --JOIN WHSWORKLINE				 wl ON   wl.DATAAREAID = ll.DATAAREAID AND   wl.PARTITION = ll.PARTITION AND wl.LOADLINEREFRECID = ll.RECID --AND wl.WORKSTATUS < 4 AND wl.WORKTYPE = 1
    JOIN WHSLOADTABLE				 lt ON   lt.DATAAREAID = ll.DATAAREAID AND   lt.PARTITION = ll.PARTITION AND lt.loadid = ll.LOADID
    JOIN WHSSHIPMENTTABLE			  s ON    s.DATAAREAID = ll.DATAAREAID AND    s.PARTITION = ll.PARTITION AND s.SHIPMENTID = ll.SHIPMENTID
WHERE sl.PARTITION = 5637144576 AND sl.DATAAREAID = 'ha'
	--AND sl.SALESSTATUS = 1
	--AND sl.REMAINSALESPHYSICAL > 0
	AND sl.ITEMID not in ('SHIPPING')
	AND sc.SHIPDATE >= DATEADD(day, -2, GETDATE())
	AND sc.SHIPDATE <= DATEADD(day, -1, GETDATE())
GROUP BY
	st.SALESID
uniON ALL
SELECT
	 st.SALESID,MIN(st.EMAIL) AS email,MIN(st.HAORDERDATETIME) AS [Order Date]
	,0 AS WavedCnt           , 0 AS WavedUnits
	,0 AS StuckCnt           , 0 AS StuckUnits
	,0 AS OpenCnts           , 0 AS OpenUnits
	,0 AS ShippedYesterdayCnt, 0 AS ShippedYesterdayUnits
	,1 AS ShippedTodayCnt    , isnull(SUM(ll.PACKINGQTY), 0) AS ShippedTodayUnits
FROM
	salesline sl
	INNER JOIN salestable st			ON   st.DATAAREAID = sl.DATAAREAID AND   st.PARTITION = sl.PARTITION AND st.salesid = sl.salesid  
	INNER JOIN INVENTTRANSORIGIN o		ON   sl.DATAAREAID =  o.DATAAREAID AND   sl.PARTITION =  o.PARTITION AND sl.INVENTTRANSID = o.INVENTTRANSID  
	INNER JOIN inventdim slid			ON slid.DATAAREAID = sl.DATAAREAID AND slid.PARTITION = sl.PARTITION AND slid.inventdimid = sl.INVENTDIMID AND slid.INVENTLOCATIONID = '4010'
	INNER JOIN WHSLOADLINE ll			ON   ll.DATAAREAID = sl.DATAAREAID AND   ll.PARTITION = sl.PARTITION AND ll.INVENTTRANSID = sl.INVENTTRANSID AND ll.ORDERNUM = sl.SALESID
    JOIN WHSCONTAINERLINE			 cl ON   cl.DATAAREAID = ll.DATAAREAID AND   cl.PARTITION = ll.PARTITION AND ll.RECID = cl.LOADLINE
	JOIN HASHIPPEDCARTONSTAGING		 sc ON   sc.DATAAREAID = cl.DATAAREAID AND   sc.PARTITION = cl.PARTITION AND sc.CARTONID = cl.CONTAINERID AND sc.TRACKINGNUMBER != ''
    --JOIN WHSWORKLINE				 wl ON   wl.DATAAREAID = ll.DATAAREAID AND   wl.PARTITION = ll.PARTITION AND wl.LOADLINEREFRECID = ll.RECID --AND wl.WORKSTATUS < 4 AND wl.WORKTYPE = 1
    JOIN WHSLOADTABLE				 lt ON   lt.DATAAREAID = ll.DATAAREAID AND   lt.PARTITION = ll.PARTITION AND lt.loadid = ll.LOADID
    JOIN WHSSHIPMENTTABLE			  s ON    s.DATAAREAID = ll.DATAAREAID AND    s.PARTITION = ll.PARTITION AND s.SHIPMENTID = ll.SHIPMENTID
WHERE sl.PARTITION = 5637144576 AND sl.DATAAREAID = 'ha'
	--AND sl.SALESSTATUS = 1
	--AND sl.REMAINSALESPHYSICAL > 0
	AND sl.ITEMID not in ('SHIPPING')
	AND sc.SHIPDATE >= DATEADD(day, -1, GETDATE())
	--AND sc.SHIPDATE <= DATEADD(day, -1, GETDATE())
GROUP BY
	st.SALESID
) b
GROUP BY SALESID