--  Monitoring un exported lines on VoiceLink

DECLARE @MyUTCTime AS DATETIME = GETUTCDATE();

WITH PickExports AS (
SELECT 
    OrdersReadyToExport  AS 'OrdersToExportCount'
    , PicksReadyToExport AS 'PicksToExportCount'
    , CASE WHEN @MyUTCTime < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, @MyUTCTime ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
		DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, @MyUTCTime ) AS nvarchar(4)) AS datetime) ) ) 
		THEN    DATEADD(hh, - 5, @MyUTCTime ) -- Before DST starts
		ELSE 
		CASE    WHEN @MyUTCTime < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, @MyUTCTime ) AS nvarchar(4)) AS datetime ) + 1 -
		DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, @MyUTCTime ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, @MyUTCTime ) -- Before DST ends
				ELSE dateadd(hh, - 5, @MyUTCTime ) -- DST ended
		END 
	END AS [KYDateTime] 
FROM   
    [TKProdVoice01].[VoiceLink].[dbo].OrderAndPickCountView --WITH (NOLOCK)
)
SELECT
	OrdersToExportCount
	, PicksToExportCount
	, KYDateTime
FROM
	PickExports
