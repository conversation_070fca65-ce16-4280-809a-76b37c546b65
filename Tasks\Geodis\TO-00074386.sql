
-- ***********

USE DAX_PROD

SELECT 
    CASE WHEN wktbl.WORKSTATUS = 0 THEN 'Open' ELSE 'In Process' END AS WorkStatus,
    wkln.WMSLOCATIONID                                  AS Location,
    wkln.ITEMID                                         AS Item,
    idim.INVENTCOLORID                                  AS Color,
    idim.INVENTSIZEID                                   AS Size,
    CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )     AS Qty,
    wkln.USERID,
    wktbl.TARGETLICENSEPLATEID                          AS LicensePlate
FROM WHSWORKLINE wkln
INNER JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wkln.[PARTITION] = wktbl.[PARTITION] AND wktbl.DATAAREAID = wkln.DATAAREAID
INNER JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.[PARTITION] = wkln.[PARTITION]
WHERE 
    wktbl.ORDERNUM = '***********'
    AND wkln.WORKTYPE = 1
    AND wktbl.WORKSTATUS < 2
ORDER BY wkln.WMSLOCATIONID