

WITH ZoneDetails AS
(
    SELECT
        loc.ZONEID
        , SUM( CASE WHEN OH.Qty > 0 THEN 1 ELSE 0 END) AS [Occupied]
        , COUNT(*)  AS [Total]
        --, loc.WMSLOCATIONID AS [Location]
        --, OH.Qty
    FROM 
        WMSLOCATION loc
        LEFT JOIN (
            SELECT
                idim.WMSLOCATIONID   AS [Location]
                --, loc.LOCPROFILEID
                , CAST(isum.PHYSICALINVENT AS DECIMAL(20,0)) AS [Qty]
            FROM 
                WMSLOCATION loc
                JOIN inventdim idim ON idim.WMSLOCATIONID = loc.WMSLOCATIONID AND idim.DATAAREAID ='ha' AND idim.[PARTITION] = loc.[PARTITION] AND idim.inventlocationid = '4010'
                JOIN INVENTSUM isum ON isum.INVENTDIMID = idim.INVENTDIMID AND isum.DATAAREAID = 'ha' AND idim.[PARTITION] = isum.[PARTITION] AND isum.PHYSICALINVENT > 0
            WHERE 
                loc.LOCPROFILEID IN ('Picking', 'Picking A', 'PalletPicking')
        ) AS OH ON OH.[Location] = loc.WMSLOCATIONID
        --JOIN inventdim idim ON idim.WMSLOCATIONID = loc.WMSLOCATIONID AND idim.DATAAREAID ='ha' AND idim.[PARTITION] = loc.[PARTITION]
        --JOIN INVENTSUM isum ON isum.INVENTDIMID = idim.INVENTDIMID AND isum.DATAAREAID = 'ha' AND idim.[PARTITION] = isum.[PARTITION]
    WHERE 
        loc.LOCPROFILEID IN ('Picking', 'Picking A', 'PalletPicking')
    GROUP BY
        loc.ZONEID
)
SELECT
    *
    , Total - Occupied AS [EmptyBins]
FROM 
    ZoneDetails
ORDER BY
    ZONEID


