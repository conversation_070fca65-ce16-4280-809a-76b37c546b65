-- This query is used in ONHI_BySKU excel report to get the inventory by SKU in the Picking locations.
-- It retrieves the inventory details for items in the Picking locations, including quantity, reserved quantity, and last movement date.
-- Starting on this version on 5/30/2025

WITH OH AS(
SELECT 
    loc.zoneid              AS [ZoneId],
    loc.locprofileid        AS [LocProfile],
    isum.itemid             AS [Item],
    idim.inventcolorid      AS [Color],
    idim.inventsizeid       AS [Size],
    loc.wmslocationid       AS [Location],
    idim.INVENTSTATUSID     AS [Status],
    CONVERT(DECIMAL(20,0), isum.physicalinvent)                         AS [Qty],
    CONVERT(DECIMAL(20,0), isum.reservphysical)                         AS [Reserved],
    isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid    AS [SKU],
    CASE WHEN (hagpft.AVAILABILITY = 'in stock' OR isum.RESERVPHYSICAL > 0) THEN 'Yes' ELSE 'No' END AS [Online],
    CAST(MAX(isum.MODIFIEDDATETIME) AS DATE)                 AS LastInventTransaction,
    DATEDIFF(DD, MAX(isum.MODIFIEDDATETIME), GETUTCDATE())      AS [SittingDays] 
     
    --, IIF(LastSO.LastSOTransaction IS NOT NULL, CAST(LastSO.LastSOTransaction AS NVARCHAR(10)), 'N/A') AS LastSOTransaction
    --, COALESCE(LastSO.SALESORDER, 'N/A') AS LastSalesOrder
    --idim.*
FROM inventdim idim
JOIN inventsum isum WITH (NOLOCK) ON isum.inventdimid = idim.inventdimid 
    AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION]
JOIN WMSLOCATION loc WITH (NOLOCK) ON loc.WMSLOCATIONID = idim.WMSLOCATIONID AND loc.DATAAREAID = 'ha' AND loc.[PARTITION] = isum.[PARTITION]
LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid
-- Trying to find the last Sales Order assiciated with an SKU
-- It might not be the last transaction, but it is the last Sales Order
-- It's possible that some SKUs haven't been sold yet, so we will not have a Sales Order for them
--LEFT JOIN WHSWORKTRANS wktrans ON isum.INVENTDIMID = wktrans.INVENTDIMID AND isum.ITEMID = wktrans.ITEMID  
  --  AND isum.DATAAREAID = wktrans.DATAAREAID AND isum.[PARTITION] = wktrans.[PARTITION]-- SKU
/*OUTER APPLY 
(
    SELECT  
        --MAX(itorigin.REFERENCEID) AS SalesOrder, 
        CAST(MAX(itrans.MODIFIEDDATETIME) AS DATE) AS LastSOTransaction
    FROM INVENTTRANS itrans 
    JOIN INVENTTRANSORIGIN itorigin  WITH (NOLOCK)  ON itrans.INVENTTRANSORIGIN   = itorigin.RECID              
        AND itorigin.[PARTITION] = itrans.[PARTITION]     AND itorigin.DATAAREAID  = itrans.DATAAREAID
    WHERE 
        itrans.INVENTDIMID = idim.INVENTDIMID AND itrans.ITEMID = isum.ITEMID
        --AND itrans.DATAAREAID = idim.DATAAREAID 
        --AND itrans.[PARTITION] = idim.[PARTITION]
        AND itrans.STATUSISSUE = 1 -- Sold
        --AND itorigin.REFERENCECATEGORY = 0 -- Sales Order 
    --GROUP BY itorigin.REFERENCEID
) AS LastSO   */
WHERE 
    idim.inventlocationid = '4010'
    AND isum.physicalinvent <> 0 
    AND idim.INVENTSITEID = 'HA USA' 
    AND loc.LOCPROFILEID IN ('W001', 'Picking', 'Picking A', 'Picking D', 'PalletPicking'/*, 'Bulk', 'Overflow'*/) 
    AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997')
    -- Testing
    --AND isum.ITEMID= '80222' AND idim.INVENTCOLORID = '78M' 
GROUP BY
    loc.WMSLOCATIONID, isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, isum.PHYSICALINVENT, isum.RESERVPHYSICAL
    , idim.INVENTSTATUSID, loc.ZONEID, loc.LOCPROFILEID, hagpft.AVAILABILITY--, LastSO.LastSOTransaction, LastSO.SALESORDER
)
SELECT  
  
    *
FROm 
OH
ORDER BY 
    SittingDays DESC;

--sp_columns whsworkline;