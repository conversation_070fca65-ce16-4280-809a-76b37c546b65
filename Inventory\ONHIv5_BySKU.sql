-- This query is used in ONHI_BySKU excel report to get the inventory by SKU in the Picking locations.
-- It retrieves the inventory details for items in the Picking locations, including quantity, reserved quantity, and last movement date.
-- Starting on this version on 5/30/2025

WITH OH AS(
SELECT 
    loc.zoneid              AS [ZoneId],
    loc.locprofileid        AS [LocProfile],
    isum.itemid             AS [Item],
    idim.inventcolorid      AS [Color],
    idim.inventsizeid       AS [Size],
    loc.wmslocationid       AS [Location],
    idim.INVENTSTATUSID     AS [Status],
    CONVERT(DECIMAL(20,0), isum.physicalinvent)                         AS [Qty],
    CONVERT(DECIMAL(20,0), isum.reservphysical)                         AS [Reserved],
    isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid    AS [SKU],
    CASE WHEN (hagpft.AVAILABILITY = 'in stock' OR isum.RESERVPHYSICAL > 0) THEN 'Yes' ELSE 'No' END AS [Online],
    CAST(isum.MODIFIEDDATETIME at time zone 'UTC' at time zone 'Eastern Standard Time' AS DATE) AS LastInventTransaction,
    --CAST(MAX(isum.MODIFIEDDATETIME) AS DATE)                 AS LastInventTransaction,
    DATEDIFF(DD, isum.MODIFIEDDATETIME, GETUTCDATE())      AS [SittingDays] 
     
    --, COALESCE(CAST(CAST(MAX(LastSold.LastTransactionDateTime) AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE) AS NVARCHAR(10)), 'N/A') AS LastTransactionDate
    --, LastSold.ReferenceCategoryId
   -- , COALESCE(LastSold.ReferenceCategory, 'N/A') AS LastReferenceCategory
   -- , COALESCE(LastSold.ReferenceId, 'N/A') AS LastReferenceId
    --idim.*
FROM inventdim idim
JOIN inventsum isum WITH (NOLOCK) ON isum.inventdimid = idim.inventdimid 
    AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION]
JOIN WMSLOCATION loc WITH (NOLOCK) ON loc.WMSLOCATIONID = idim.WMSLOCATIONID AND loc.DATAAREAID = 'ha' AND loc.[PARTITION] = isum.[PARTITION]
LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid
-- Trying to find the last Sales Order assiciated with an SKU
-- It might not be the last transaction, but it is the last Sales Order
-- It's possible that some SKUs haven't been sold yet, so we will not have a Sales Order for them
/*
OUTER APPLY 
(
    -- Select lasat sold transaction associated with the SKU
    SELECT --TOP 1
    /*
         CASE 
          WHEN itorigin.REFERENCECATEGORY = 0 THEN 'Sales Order'
          WHEN itorigin.REFERENCECATEGORY = 2 THEN 'Production Order'
          WHEN itorigin.REFERENCECATEGORY = 3 THEN 'Purchase Order'
          WHEN itorigin.REFERENCECATEGORY = 4 THEN 'Inventory Transaction'
          WHEN itorigin.REFERENCECATEGORY = 5 THEN 'Inventory Adjustment'
          WHEN itorigin.REFERENCECATEGORY = 6 THEN 'Transfer'
          WHEN itorigin.REFERENCECATEGORY = 13 THEN 'Inventory Counting'
          WHEN itorigin.REFERENCECATEGORY = 15 THEN 'Quarantine Order'
          WHEN itorigin.REFERENCECATEGORY = 21 THEN 'Transfer order shipment'
          WHEN itorigin.REFERENCECATEGORY = 22 THEN 'Transfer order receive'
          WHEN itorigin.REFERENCECATEGORY = 201 THEN 'Work'
          WHEN itorigin.REFERENCECATEGORY = 202 THEN 'Quarantine'
          ELSE 'Unknown'
     END AS [ReferenceCategory]
        , itorigin.REFERENCECATEGORY AS ReferenceCategoryId
        , itorigin.REFERENCEID AS ReferenceId
        --, CAST(itrans.MODIFIEDDATETIME at time zone 'UTC' at time zone 'Eastern Standard Time' AS DATE) AS LastTransactionDate
        */
         MAX(itrans.MODIFIEDDATETIME) AS LastTransactionDateTime

        --, MAX(itrans.RECID) AS itrans_RecId -- Last Transaction
    FROM INVENTTRANS itrans
    JOIN INVENTTRANSORIGIN itorigin  WITH (NOLOCK)  ON itrans.INVENTTRANSORIGIN   = itorigin.RECID
        AND itorigin.[PARTITION] = itrans.[PARTITION]     AND itorigin.DATAAREAID  = itrans.DATAAREAID
    -- This will make the query slower but it's the only way to get the last sales order
    -- Apparently the inventdimid from INVENTTRANS(outer query) is not unique
    JOIN inventdim id WITH (NOLOCK) ON id.INVENTCOLORID = idim.INVENTCOLORID AND id.INVENTSIZEID = idim.INVENTSIZEID
            AND id.DATAAREAID = itrans.DATAAREAID AND id.[PARTITION] = itrans.[PARTITION]
    WHERE       
        itrans.ITEMID = isum.ITEMID
        AND itrans.DATAAREAID = idim.DATAAREAID
        AND itrans.[PARTITION] = idim.[PARTITION]
        AND itrans.STATUSISSUE = 1 -- Sold
        --AND itrans.INVENTDIMID = idim.INVENTDIMID -- this is faster but doesn't pull all transactions
        --AND itorigin.REFERENCECATEGORY = 0 -- Sales Order
    --GROUP BY itorigin.REFERENCECATEGORY, itorigin.REFERENCEID, itrans.MODIFIEDDATETIME, itorigin.REFERENCECATEGORY
    --ORDER BY itrans.MODIFIEDDATETIME DESC, itrans.RECID DESC
    --ORDER BY itrans.RECID DESC
) AS LastSold   
*/
WHERE
    idim.inventlocationid = '4010'
    AND isum.physicalinvent > 0
    AND idim.INVENTSITEID = 'HA USA'
    AND loc.LOCPROFILEID IN ('W001', 'Picking', 'Picking A', 'Picking D', 'PalletPicking'/*, 'Bulk', 'Overflow'*/) 
    AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997')
    -- Testing
   -- AND isum.ITEMID = '48552' --AND idim.INVENTCOLORID = '78M' 
GROUP BY
    loc.WMSLOCATIONID, isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, isum.PHYSICALINVENT, isum.RESERVPHYSICAL
    , idim.INVENTSTATUSID, loc.ZONEID, loc.LOCPROFILEID, hagpft.AVAILABILITY
    , isum.MODIFIEDDATETIME
    --, LastSold.ReferenceId, LastSold.ReferenceCategory, LastSold.ReferenceCategoryId
)
SELECT  
  
    *
FROM 
OH
ORDER BY 
    SKU ASC,
    SittingDays DESC;

--sp_columns inventdim;