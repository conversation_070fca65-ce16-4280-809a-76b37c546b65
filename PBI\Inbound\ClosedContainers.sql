/*
02/27/2024 - LA

No more temp tables, some comments, removing not needed columns, Left Joins are now Joins

*/
/*Closed Containers 7pm-7am*/ -- <PERSON>'s comments

DECLARE @EST_TODAYCURRENTTIME AS DATETIME = GETUTCDATE() AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time';

WITH cntinfo AS(
SELECT  
    cnttbl.ContainerID
    , cnttbl.CONTAINERTYPECODE
    --, cnttbl.MODIFIEDDATETIME           AS UTC_ContainerClosedDATETIME ---UTC TIME container got closed
	, shptbl.ordernum
    --, cnttbl.MASTERTRACKINGNUM
    --, cnttbl.SHIPCARRIERTRACKINGNUM
    , SUM( cntln.QTY )                  AS Qty
    -- SQL Server 2016 and after
    , cnttbl.MODIFIEDDATETIME   AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS EST_ContModifiedDate
    --, GETUTCDATE()              AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS EST_TODAYCURRENTTIME
FROM 
    WHSCONTAINERTABLE cnttbl          
    ------THIS IS FOR SHIPMENT STATUS = 5 AKA---SHIPPED CONTAINER
    JOIN WHSSHIPMENTTABLE shptbl ON cnttbl.SHIPMENTID = shptbl.SHIPMENTID AND cnttbl.DATAAREAID = shptbl.DATAAREAID AND cnttbl.[PARTITION] = shptbl.[PARTITION]
    ----THIS IS TO GET QTY IN A CONTAINER
    JOIN WHSCONTAINERLINE cntln ON cnttbl.CONTAINERID = cntln.CONTAINERID AND cnttbl.DATAAREAID = cntln.DATAAREAID AND cnttbl.[PARTITION] = cntln.[PARTITION]
WHERE 
    cnttbl.MODIFIEDDATETIME > getutcdate() -3 -- Container info from the last three days
    AND shptbl.SHIPMENTSTATUS = 5 
    AND SHIPCARRIERTRACKINGNUM NOT LIKE ''
GROUP BY 
    cnttbl.CONTAINERID, cnttbl.MODIFIEDDATETIME, shptbl.ORDERNUM, cnttbl.CONTAINERTYPECODE--, cnttbl.MASTERTRACKINGNUM, cnttbl.SHIPCARRIERTRACKINGNUM
)
SELECT 
    * 
    , CONVERT(VARCHAR(20), EST_ContModifiedDate, 100)   AS 'AM/PM'
    , DATEPART(HOUR, EST_ContModifiedDate)              AS CloseContainerHOUR
    , CONVERT(VARCHAR(20), EST_ContModifiedDate, 111)   AS CLOSEDCONTAINERDATE		/*this will be used to compare if the close date is today or not*/
    --, CONVERT(VARCHAR(20), @EST_TODAYCURRENTTIME, 111)   AS ESTCURRENTDATE			/*this will be used to compare if the close date is today or not*/
    --, DATEPART(HOUR, @EST_TODAYCURRENTTIME)              AS EST_CURRENTHOUR
    , 
    CASE 
        WHEN CONVERT(VARCHAR(20), EST_ContModifiedDate, 111) = CONVERT(VARCHAR(20), @EST_TODAYCURRENTTIME, 111) THEN 0 ELSE 1
        --WHEN CLOSEDCONTAINERDATE < ESTCURRENTDATE THEN 1
    END AS TODAY0orYesterday1
    ,
    CASE 
        WHEN DATEPART(HOUR, EST_ContModifiedDate) BETWEEN 6 AND 15 THEN 1 ELSE 2
        --WHEN DATEPART(HOUR, EST_ContModifiedDate) NOT BETWEEN 4 AND 15 THEN 2
    END AS SHIFT
FROM
    cntinfo
WHERE 
    --EST_ContModifiedDate > getdate() - 6  --tHIS IS GETTING THE LAST TWO DAYS IN EASTERN TIME
    EST_ContModifiedDate > getdate() - 3 -- Only three days were pulled in the previous query 
