
-- Finding out what was received as offsite, but wasn't marked as that on the load

SELECT
    uoms.CREATEDWORKID
    , DATEADD( hh, -4, wktbl.CREATEDDATETIME ) AS CreatedDateTime
    , uoms.LICENSEPLATEID, 
    uoms.LOADID,
    CASE WHEN wktbl.WORKTEMPLATECODE = 'PO Offsite Putaway' THEN 'No' ELSE 'Yes' END AS Correct
FROM
    WHSUOMSTRUCTURE uoms
    LEFT JOIN WHSWORKTABLE wktbl ON uoms.CREATEDWORKID = wktbl.WORKID AND uoms.SHIPMENTID = wktbl.SHIPMENTID AND uoms.[PARTITION] = wktbl.[PARTITION]
WHERE   
    uoms.MOD<PERSON>IEDDATETIME BETWEEN GETUTCDATE() - 30 AND GETUTCDATE() --09/20/2022' AND '09/30/2022'
    AND uoms.HAOFFSITERECEIVING = 0 -- Not marked as offsite
    AND ISNULL( uoms.CREATEDWORKID, '' ) <> '' -- Already received
    --AND wktbl.WORKTEMPLATECODE = 'PO Offsite Putaway'