

WITH ShpData AS
(
SELECT
    shptbl.SHIPMENTID
    , CAST( SUM( ll.QTY ) AS INT) AS ShipmentUnits
FROM 
    [prodsql02].[DAX_Archive].[arc].[WHSSHIPMENTTABLE] shptbl
    LEFT JOIN [prodsql02].[DAX_Archive].[arc].[WHSLOADLINE] ll ON shptbl.LOADID = ll.LOADID AND shptbl.DATAAREAID = 'ha' AND ll.[PARTITION] = shptbl.[PARTITION]
WHERE
    1 = 1
    AND shptbl.INVENTSITEID = 'HA USA'
    AND shptbl.INVENTLOCATIONID = '4010'
    AND shptbl.LOADDIRECTION = 2
    AND shptbl.SHIPMENTSTATUS = 5
    AND shptbl.WORKTRANSTYPE = 2
    AND shptbl.modecode IN ('1D','2D','3D')
    AND shptbl.MODIFIEDDATETIME BETWEEN '12/18/2022 05:00:00.000' AND '12/24/2022 05:00:00.000'
GROUP BY
    shptbl.SHIPMENTID
)
SELECT
    COUNT( SHIPMENTID )     AS [TotalShipments]
    , SUM( ShipmentUnits)   AS [TotalUnits]
FROM
    ShpData