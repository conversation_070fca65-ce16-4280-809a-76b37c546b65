
-- Deadlocks on prodsql01. <PERSON>'s queries.
-- Voice related

SELECT 
    cast(exception.createddatetime AS DATE) AS exception_date,
    count(map.exceptionid)                  AS deadlock_count
FROM   
    dax_prod.dbo.sysexceptiontable exception
    INNER JOIN dax_prod.dbo.aifexceptionmap map ON map.exceptionid = exception.recid
WHERE  
    exception.description LIKE '%deadlock%'
    --AND map.portname LIKE '%voice%'
    AND cast(exception.createddatetime AS DATE) >= GETUTCDATE() - 12
GROUP  BY cast(exception.createddatetime AS DATE)
ORDER  BY exception_date DESC;

 
-- Same results like in SysAdmin/Exceptions

SELECT 
    map.exceptionid,
    map.portname,
    exception.exception,
    replace(replace(exception.description, char(10), ''), char(13), '') as description,
    mlog.actionid,
    exception.createddatetime,
    exception.createdby
FROM   
    dax_prod.dbo.sysexceptiontable exception
    INNER JOIN dax_prod.dbo.aifexceptionmap map ON map.exceptionid = exception.recid
    INNER JOIN dax_prod.dbo.aifmessagelog mlog ON mlog.messageid = map.messageid
WHERE  
    exception.description LIKE '%deadlock%'
    -- AND map.portname LIKE '%voice%'
    AND cast(exception.createddatetime AS DATE) = '2024-09-24'
ORDER  BY 
    exception.createddatetime DESC;
 