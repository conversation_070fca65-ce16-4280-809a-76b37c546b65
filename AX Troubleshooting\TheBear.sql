
-- The bear

SELECT 
    T1.<PERSON>EC<PERSON><PERSON><PERSON><PERSON>,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID
    ,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU
    ,SUM(T2.POSTEDQTY),<PERSON>UM(T2.POSTEDVALUE),<PERSON>UM(T2.PHYSICALVALUE),<PERSON>UM(T2.DEDUCTED),<PERSON>UM(T2.RECEIVED),SUM(T2.RESERVPHYSICAL),SUM(T2.RESERVORDERED)
    ,<PERSON>UM(T2.REGISTERED),<PERSON><PERSON>(T2.<PERSON>ICKE<PERSON>),<PERSON><PERSON>(T2.ONORDE<PERSON>),<PERSON><PERSON>(T2.ORDERED),<PERSON><PERSON>(T2.ARRIVED),<PERSON><PERSON>(T2.QUOTATIONRECEIPT),<PERSON>UM(T2.QUOTATIONISSUE),<PERSON>UM(T2.AVAILPHYSICAL)
    ,SUM(T2.AVAILORDERED),SUM(T2.PHYSICALINVENT),SUM(T2.POSTEDVALUESECCUR_RU),SUM(T2.PHYSICALVALUESECCUR_RU) 
FROM 
    INVENTSUMDELTADIM T1 CROSS JOIN INVENTSUM T2 CROSS JOIN INVENTDIM T3 
WHERE ((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND (((T2.PARTITION=@P3) AND (T2.DATAAREAID=@P4)) AND ((T2.ITEMID=T1.ITEMID) 
    AND (T2.CLOSED=@P5))) AND (((T3.PARTITION=@P6) AND (T3.DATAAREAID=@P7)) AND (((((((((((T2.INVENTDIMID=T3.INVENTDIMID) AND (T1.TTSID=@P8)) 
    AND (T1.AREALLACTIVEDIMENSIONSSPECIFIED=@P9)) AND (T1.CHECKTYPE<@P10)) AND ((T1.INVENTSIZEID=T3.INVENTSIZEID) OR (T1.INVENTSIZEIDFLAG=@P11))) 
    AND ((T1.INVENTCOLORID=T3.INVENTCOLORID) OR (T1.INVENTCOLORIDFLAG=@P12))) AND ((T1.INVENTSITEID=T3.INVENTSITEID) OR (T1.INVENTSITEIDFLAG=@P13))) 
    AND ((T1.INVENTLOCATIONID=T3.INVENTLOCATIONID) OR (T1.INVENTLOCATIONIDFLAG=@P14))) AND ((T1.LICENSEPLATEID=T3.LICENSEPLATEID) OR (T1.LICENSEPLATEFLAG=@P15))) 
    AND ((T1.INVENTSTATUSID=T3.INVENTSTATUSID) OR (T1.INVENTSTATUSFLAG=@P16))) AND ((T1.INVENTSERIALID=T3.INVENTSERIALID) OR (T1.INVENTSERIALIDFLAG=@P17)))) 
GROUP BY 
    T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID,
    T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU 
ORDER BY 
    T1.CHECKTYPE,T1.ITEMID,T1.CONFIGID,T1.INVENTSIZEID,T1.INVENTCOLORID,T1.INVENTSTYLEID,T1.INVENTSITEID,T1.INVENTLOCATIONID,T1.INVENTSTATUSID
    ,T1.LICENSEPLATEID,T1.INVENTBATCHID,T1.WMSLOCATIONID,T1.INVENTSERIALID,T1.INVENTGTDID_RU,T1.INVENTPROFILEID_RU,T1.INVENTOWNERID_RU 
OPTION(FORCE ORDER)

/*
SELECT T1.CHECKTYPE

,T1.ITEMID

,T1.CONFIGID

,T1.INVENTSIZEID

,T1.INVENTCOLORID

,T1.INVENTSTYLEID

,T1.INVENTSITEID

,T1.INVENTLOCATIONID

,T1.INVENTSTATUSID

,T1.LICENSEPLATEID

,T1.INVENTBATCHID

,T1.WMSLOCATIONID

,T1.INVENTSERIALID

,T1.INVENTGTDID_RU

,T1.INVENTPROFILEID_RU

,T1.INVENTOWNERID_RU

,SUM(T2.POSTEDQTY)

,SUM(T2.POSTEDVALUE)

,SUM(T2.PHYSICALVALUE)

,SUM(T2.DEDUCTED)

,SUM(T2.RECEIVED)

,SUM(T2.RESERVPHYSICAL)

,SUM(T2.RESERVORDERED)

,SUM(T2.REGISTERED)

,SUM(T2.PICKED)

,SUM(T2.ONORDER)

,SUM(T2.ORDERED)

,SUM(T2.ARRIVED)

,SUM(T2.QUOTATIONRECEIPT)

,SUM(T2.QUOTATIONISSUE)

,SUM(T2.AVAILPHYSICAL)

,SUM(T2.AVAILORDERED)

,SUM(T2.PHYSICALINVENT)

,SUM(T2.POSTEDVALUESECCUR_RU)

,SUM(T2.PHYSICALVALUESECCUR_RU)

FROM INVENTSUMDELTADIM T1

CROSS JOIN INVENTSUM T2

CROSS JOIN INVENTDIM T3

WHERE ((T1.PARTITION=@P1)

AND (T1.DATAAREAID=@P2))

AND (((T2.PARTITION=@P3)

AND (T2.DATAAREAID=@P4))

AND ((T2.ITEMID=T1.ITEMID)

AND (T2.CLOSED=@P5)))

AND (((T3.PARTITION=@P6)

AND (T3.DATAAREAID=@P7))

AND (((((((((((T2.INVENTDIMID=T3.INVENTDIMID)

AND (T1.TTSID=@P8))

AND (T1.AREALLACTIVEDIMENSIONSSPECIFIED=@P9))

AND (T1.CHECKTYPE<@P10))

AND ((T1.INVENTSIZEID=T3.INVENTSIZEID) OR (T1.INVENTSIZEIDFLAG=@P11)))

AND ((T1.INVENTCOLORID=T3.INVENTCOLORID) OR (T1.INVENTCOLORIDFLAG=@P12)))

AND ((T1.INVENTSITEID=T3.INVENTSITEID) OR (T1.INVENTSITEIDFLAG=@P13)))

AND ((T1.INVENTLOCATIONID=T3.INVENTLOCATIONID) OR (T1.INVENTLOCATIONIDFLAG=@P14)))

AND ((T1.LICENSEPLATEID=T3.LICENSEPLATEID) OR (T1.LICENSEPLATEFLAG=@P15)))

AND ((T1.INVENTSTATUSID=T3.INVENTSTATUSID) OR (T1.INVENTSTATUSFLAG=@P16)))

AND ((T1.INVENTSERIALID=T3.INVENTSERIALID) OR (T1.INVENTSERIALIDFLAG=@P17))))

GROUP BY T1.CHECKTYPE

,T1.ITEMID

,T1.CONFIGID

,T1.INVENTSIZEID

,T1.INVENTCOLORID

,T1.INVENTSTYLEID

,T1.INVENTSITEID

,T1.INVENTLOCATIONID

,T1.INVENTSTATUSID

,T1.LICENSEPLATEID

,T1.INVENTBATCHID

,T1.WMSLOCATIONID

,T1.INVENTSERIALID

,T1.INVENTGTDID_RU

,T1.INVENTPROFILEID_RU

,T1.INVENTOWNERID_RU

ORDER BY T1.CHECKTYPE

,T1.ITEMID

,T1.CONFIGID

,T1.INVENTSIZEID

,T1.INVENTCOLORID

,T1.INVENTSTYLEID

,T1.INVENTSITEID

,T1.INVENTLOCATIONID

,T1.INVENTSTATUSID

,T1.LICENSEPLATEID

,T1.INVENTBATCHID

,T1.WMSLOCATIONID

,T1.INVENTSERIALID

,T1.INVENTGTDID_RU

,T1.INVENTPROFILEID_RU

,T1.INVENTOWNERID_RU

OPTION(FORCE ORDER)
*/