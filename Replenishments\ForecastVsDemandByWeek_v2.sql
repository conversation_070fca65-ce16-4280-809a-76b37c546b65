
USE DAX_PROD
/*
Summary of replenishments created in the last <x> days, grouped by week.
This query calculates the number of cartons replenished by week, distinguishing between MinMax and Demand replenishments.
While more Demand replenishments are created, it means MinMax didn't cover the demand.

*/

DECLARE @ReportWeeks AS INT = 10; -- Number of weeks to report, including the current week

WITH RepWk AS(
SELECT
    wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
    , wktbl.WORKID
    , CAST( wktbl.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE ) AS CreatedDate
    , wktbl.WORK<PERSON><PERSON>LATECODE        AS WorkTemplate
    , CASE WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 'MinMax' ELSE 'Demand' END AS ReplenType
    , CASE WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 1 ELSE 0 END  AS MinMaxCarton
    , CASE WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 0 ELSE 1 END  AS DemandCarton
    , CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )     AS 'CartonQty'
FROM
    WHSWORKTABLE wktbl
    INNER JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    INNER JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
    INNER JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
WHERE
    DATEDIFF( wk, wktbl.CREATEDDATETIME, GETUTCDATE() ) <= 9--@ReportWeeks - 1 -- Only this week and previous weeks
    AND wktbl.WORKTRANSTYPE = 11  -- Replenishment
    AND wkln.WORKTYPE = 2 -- Put. To get only one line
    AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
    AND loc.locprofileid LIKE '%Picking%' -- Only Picking locations
    AND wktbl.WORKSTATUS < 5 -- Not cancelled
),
RepWkCartons AS (
SELECT
    CreatedDate
    , CONVERT( nvarchar, DATEADD(dd, -(DATEPART(dw, RepWk.CreatedDate) - 1), RepWk.CreatedDate), 101 ) AS WeekStart
    , CONVERT( nvarchar,DATEADD(dd, 7 -(DATEPART(dw, RepWk.CreatedDate)), RepWk.CreatedDate), 101 )  AS WeekEnd
    , DATEPART( wk, RepWk.CreatedDate)        AS WeekOfTheYear
    , RepWk.DemandCarton
    , RepWk.MinMaxCarton
FROM
    RepWk   
)
SELECT
    
     WeekStart
    , WeekEnd
    , WeekOfTheYear
    , SUM( MinMaxCarton ) AS MinMaxCartons
    , SUM( DemandCarton ) AS DemandCartons
    --,CreatedDate
FROM
    RepWkCartons
GROUP BY
    --CreatedDate, 
    WeekOfTheYear, WeekStart, WeekEnd
ORDER BY
    WeekOfTheYear

/*
SELECT
    DATEADD(dd, -(DATEPART(dw, GETDATE()) - 1), GETDATE()) AS WeekStart
    , DATEADD(dd, 7 -(DATEPART(dw, GETDATE())), GETDATE())  AS WeekEnd
*/