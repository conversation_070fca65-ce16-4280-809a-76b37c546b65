--Cubiscan related tables
-- Finding items that won't fit in the clog box

SELECT
    ONHI.SKU
    , ONHI.[Location]
    , ONHI.ZoneId
    , ONHI.Qty
    , ONHI.Reserved
    , CGNoGo.Width
    , CGNoGo.Depth
    , CGNoGo.Height
FROM
(
SELECT 
    pd.ITEMID + '-' + pd.ECORESITEMCOLORNAME + '-' + pd.ECORESITEMSIZENAME AS SKU
    , CONVERT( DECIMAL( 10, 2 ), pd.DEPTH ) AS Depth
    , CONVERT( DECIMAL( 10, 2 ), pd.WIDTH ) As Width
    , CONVERT( DECIMAL( 10, 2 ), pd.HEIGHT ) AS Height
FROM 
    WHSPHYSDIMUOM pd
WHERE   
     pd.HEIGHT > 6.25
) AS CGNoGo
INNER JOIN
(
SELECT  wmslocation.zoneid			AS ZoneId, 
/*
		wmslocation.locprofileid	AS LocProfile, 
		OH.itemid					AS Item, 
		OH.inventcolorid			AS Color, 
		OH.inventsizeid				AS Size, 
*/        
		wmslocation.wmslocationid	AS Location, 
		CONVERT( DECIMAL( 20, 0 ), OH.physicalinvent )			AS Qty, 
		CONVERT( DECIMAL( 20, 0 ), OH.reservphysical )			AS Reserved,
        OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid AS SKU
  --      CASE WHEN CAST( hagpft.DATETIMESTAMP AS DATE ) = CAST( GETDATE() AS DATE ) THEN 'Yes' ELSE 'No' END AS Online
FROM wmslocation
LEFT OUTER JOIN 
( SELECT inventsum.itemid, inventsum.physicalinvent, inventsum.reservphysical, inventdim.inventcolorid, inventdim.inventsizeid, inventdim.wmslocationid
  FROM inventsum
  LEFT OUTER JOIN inventdim
  ON inventsum.inventdimid = inventdim.inventdimid AND inventsum.[PARTITION] = INVENTDIM.[PARTITION] AND INVENTSUM.DATAAREAID = 'ha'
  WHERE inventsum.physicalinvent > 0 AND inventdim.inventlocationid = '4010'
) as OH -- Pulls location with inventory. Avoiding duplicate records.
ON wmslocation.wmslocationID = OH.wmslocationid
--LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid
WHERE 
    wmslocation.inventlocationid = '4010' 
    AND wmslocation.locprofileid LIKE '%Picking%'
    AND wmslocation.zoneid NOT IN ( 'Current Pick')
    AND OH.physicalinvent > 0 -- Only locations with inventory to be considered as candidates
--ORDER BY Item, Color, Size
) AS ONHI
ON CGNoGo.SKU = ONHI.SKU
ORDER BY
    ONHI.[Location]

/*
SELECT *
FROM HA_CUBISCAN_ITEM_INFO_IMPORT
WHERE Cast( TIME_STAMP AS Date ) BETWEEN '11/01/2022' AND '11/03/2022'
--AND ( NET_LENGTH > 13 AND NET_WIDTH > 13 )
--AND OPT_INFO_1 LIKE 'Cha%'


SELECT TOP 5 *
FROM HAINVENTITEMGTIN--InventItemGTIN


SELECT TOP 5 *
FROM InventItemGTIN

SELECT TOP 5 *
FROM ECORESPRODUCTTRANSLATION
*/