
-- Troubleshooting misssorts

SELECT 
    TOP 2000 
    hascnt.CARTONID
    , hascnt.TRACKINGNUMBER
    , DATEADD( hh, -5, hascnt.CREATEDDATETIME )        AS cs_CreatedDateTime
    , hascnt.SHIPMETHOD                 AS cs_ShipMethod
    , hasmt.SHIPMETHODDESCRIPTION       AS hasmt_ShipMethodDescription
    , hascnt.CARRIERTYPE                AS cs_CarrierType
    , oq.MHXDATA2                       AS oq_ShipMethodDescription
    --, hascnt.SHIPPERID
    --, hascnt.TRACKINGNUMBER
FROM HASHIPPEDCARTONSTAGING hascnt 
LEFT JOIN MHXOutboundQueue oq ON oq.MHXDATA1 = hascnt.CARTONID
LEFT JOIN HASHIPMETHODTRANSLATION hasmt ON hasmt.HAWHSSHIPMETHOD = hascnt.SHIPMETHOD
WHERE
    1 = 1 
   -- AND hascnt.CARTONID IN ( 'CN013865779', 'CN013865778', 'CN013865776', 'CN013865773', 'CN013865769' )
    AND ISNULL( oq.MHXDATA2, '' ) != ''
    AND CAST( hascnt.MODIFIEDDATETIME AS DATE) BETWEEN '3/29/2023' AND '3/31/2023'
    AND oq.MHXDATA2 = 'FedEx Ground'
ORDER BY
    hascnt.CREATEDDATETIME ASC

/*


SELECT 
    TOP 2 *
FROM WHSCONTAINERTABLE cnttbl
WHERE
    1 = 1 
    AND cast(  cnttbl.MODIFIEDDATETIME AS DATE ) = '11/11/2022'
    AND cnttbl.HAWHSSHIPPERID = 'MHX'
    AND cnttbl.MASTERTRACKINGNUM LIKE '1Z%'
ORDER BY
    cnttbl.MODIFIEDDATETIME ASC

--sp_columns WHSWORKLINE

SELECT *
FROM
    HASHIPMETHODTRANSLATION

SELECT 
    TOP 20 *
FROM  
    MHXOutboundQueue
WHERE
    MHXDATA1 = 'CN013828441'
*/