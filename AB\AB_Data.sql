-- Sam request: 2021 data

SELECT wt.<PERSON><PERSON><PERSON><PERSON>ERID AS Container, 
CAST( 
CASE    WHEN DATEPART( mm, wt.WORKCLOSEDUTCDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wt.W<PERSON><PERSON><PERSON><PERSON>EDUTCDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wt.WOR<PERSON><PERSON>OSEDUTCDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wt.WORKCLOSEDUTCDATETIME ) -- No DST
        WHEN DATEPART( mm, wt.WORKCLOSEDUTCDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, wt.W<PERSON><PERSON><PERSON><PERSON>EDUTCDATETIME ) - DATEPART( w, wt.WOR<PERSON>CLOSEDUTCDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wt.WORKCLOSEDUTCDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wt.<PERSON><PERSON><PERSON><PERSON><PERSON>EDUTCDATETIME )
             END
        WHEN DATEPART( dd, wt.WOR<PERSON>CL<PERSON>EDUTCDATETIME ) - DATEPART( w, wt.WORKCLOSEDUTCDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wt.WORKCLOSEDUTCDATETIME )
        ELSE DATEADD( hh, - 4, wt.WORKCLOSEDUTCDATETIME )
END   AS DATETIME )  AS CompletedOn,
--CASE WHEN wt.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' )	THEN 'AB_CNT' ELSE 'No_AB_CNT' END AS CNT_Type,
--CASE WHEN wt.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' )	THEN 1 ELSE 0 END AS AB_CNT,
--CASE WHEN wt.WORKTEMPLATECODE IN ( '4010 Direct', '4010 CartonShip Sing', '4010 CartonShip Mult' )	THEN 1 ELSE 0 END AS No_AB_CNT,
CASE WHEN ct.HAWHSSHIPPERID = 'MHX' THEN 'AutoBagger' ELSE 'Logistyx' END AS ShippedBy
FROM WHSWORKTABLE wt
--INNER JOIN WHSSHIPMENTTABLE st ON wt.SHIPMENTID = st.SHIPMENTID
INNER JOIN WHSCONTAINERTABLE ct ON wt.CONTAINERID = ct.CONTAINERID
--INNER JOIN WHSWAVETABLE wavt ON wavt.WAVEID = wt.WAVEID
WHERE	wt.WORKTRANSTYPE = 2 AND wt.WORKSTATUS = 4 AND 
		wt.WORKTEMPLATECODE IN ('4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult', '4010 Direct', '4010 CartonShip Sing', '4010 CartonShip Mult'  ) AND  
		wt.WORKCLOSEDUTCDATETIME BETWEEN '09/01/2021 05:00:00:00' AND '12/31/2021 04:00:00:000' 
ORDER BY wt.WORKID