
USE DAX_PROD;

DECLARE @TableName NVARCHAR(128) = 'INVENTSUM'; -- Specify the table name

-- Get information about the specified index and generate CREATE INDEX SQL
SELECT 
    i.name AS IndexName,
    i.type_desc AS IndexType,
    i.is_unique AS IsUnique,
    i.is_primary_key AS IsPrimaryKey,
    i.is_unique_constraint AS IsUniqueConstraint,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id
        ORDER BY ic.key_ordinal
        FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') AS IndexedColumns
        /*,
    'CREATE ' +
        CASE WHEN i.is_unique = 1 THEN 'UNIQUE ' ELSE '' END +
        CASE WHEN i.type_desc LIKE 'CLUSTERED%' THEN 'CLUSTERED ' ELSE 'NONCLUSTERED ' END +
        'INDEX ' + i.name +
        ' ON ' + t.name + ' (' +
        STUFF((
            SELECT ', ' + c.name +
            CASE WHEN ic.is_descending_key = 1 THEN ' DESC' ELSE ' ASC' END
            FROM sys.index_columns ic
            JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
            WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id
            ORDER BY ic.key_ordinal
            FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 2, '') +
        ');' AS CreateIndexSQL*/
FROM sys.indexes i
JOIN sys.tables t ON i.object_id = t.object_id
WHERE t.name = @TableName
  --AND i.name = @IndexName;

