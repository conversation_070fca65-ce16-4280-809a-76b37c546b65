# SQL Query Performance Analysis - ONHIv5_BySKU.sql

## 🚨 **Critical Performance Issues Identified**

### 1. **OUTER APPLY Performance Killer (Lines 34-49)**
**Problem**: The OUTER APPLY executes a complex subquery for EVERY row in the main result set.
- Joins INVENTTRANS (typically millions of records) with INVENTTRANSORIGIN
- Uses aggregation functions (MAX) and GROUP BY
- No early filtering on the subquery

**Impact**: If main query returns 10,000 rows, the subquery executes 10,000 times!

**Solution**: Replace with pre-aggregated CTE and LEFT JOIN (implemented in optimized version)

### 2. **Late Filter Application**
**Problem**: Filters are applied after expensive joins
- Tables are joined first, then filtered
- INVENTSUM and INVENTTRANS are massive in AX 2012

**Solution**: Move filters closer to table scans in WHERE clauses

### 3. **String Concatenation in JOIN**
**Problem**: `hagpft.MPN = isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid`
- Prevents index usage
- Forces table scan on hagoogleproductfeedtable

**Solution**: Consider computed column or separate lookup

### 4. **Missing Indexes**
**Problem**: Likely missing optimal indexes for this query pattern
**Solution**: See recommended indexes below

## 📊 **Performance Improvement Strategies**

### **Strategy 1: Optimized Query (Recommended)**
- File: `ONHIv5_BySKU_Optimized.sql`
- Replaces OUTER APPLY with pre-aggregated CTE
- Moves filters earlier in execution
- Maintains same result set

### **Strategy 2: Split Query Approach**
- File: `ONHIv5_BySKU_Alternative.sql`
- Separates inventory data from sales order lookup
- Use when Strategy 1 still performs poorly
- May require application-level joining

## 🔧 **Recommended Database Changes**

### **Critical Indexes to Create:**

```sql
-- 1. Primary inventory filter index
CREATE INDEX IX_INVENTSUM_DATAAREAID_PHYSICALINVENT_ITEMID 
ON INVENTSUM (DATAAREAID, PHYSICALINVENT, ITEMID) 
INCLUDE (INVENTDIMID, RESERVPHYSICAL, MODIFIEDDATETIME, RECID, PARTITION);

-- 2. Inventory dimension filter index
CREATE INDEX IX_INVENTDIM_DATAAREAID_INVENTLOCATIONID_INVENTSITEID 
ON INVENTDIM (DATAAREAID, INVENTLOCATIONID, INVENTSITEID, WMSLOCATIONID) 
INCLUDE (INVENTDIMID, INVENTCOLORID, INVENTSIZEID, INVENTSTATUSID, PARTITION);

-- 3. Location profile filter index
CREATE INDEX IX_WMSLOCATION_DATAAREAID_LOCPROFILEID 
ON WMSLOCATION (DATAAREAID, LOCPROFILEID, WMSLOCATIONID) 
INCLUDE (ZONEID, PARTITION);

-- 4. Sales transaction lookup index
CREATE INDEX IX_INVENTTRANS_STATUSISSUE_DATAAREAID_INVENTDIMID 
ON INVENTTRANS (STATUSISSUE, DATAAREAID, INVENTDIMID, ITEMID) 
INCLUDE (INVENTTRANSORIGIN, MODIFIEDDATETIME, PARTITION);
```

### **Consider Computed Column for SKU:**
```sql
ALTER TABLE INVENTDIM ADD SKU AS (ITEMID + '-' + INVENTCOLORID + '-' + INVENTSIZEID);
CREATE INDEX IX_INVENTDIM_SKU ON INVENTDIM (SKU);
```

## 📈 **Expected Performance Improvements**

### **Before Optimization:**
- Estimated execution time: 2-5+ minutes
- High CPU usage from OUTER APPLY
- Excessive logical reads on INVENTTRANS

### **After Optimization:**
- Estimated execution time: 15-60 seconds
- Reduced CPU usage
- Better index utilization
- Improved memory usage

## 🧪 **Testing Recommendations**

### **1. Performance Testing:**
```sql
-- Enable statistics
SET STATISTICS IO ON;
SET STATISTICS TIME ON;

-- Run original query and note:
-- - Logical reads
-- - CPU time
-- - Elapsed time

-- Run optimized query and compare
```

### **2. Execution Plan Analysis:**
- Compare execution plans
- Look for table scans vs index seeks
- Check for parallelism issues
- Verify join algorithms

### **3. Index Usage Monitoring:**
```sql
-- Check index usage after running optimized query
SELECT * FROM sys.dm_db_index_usage_stats 
WHERE database_id = DB_ID() 
AND object_id IN (
    OBJECT_ID('INVENTSUM'),
    OBJECT_ID('INVENTDIM'),
    OBJECT_ID('WMSLOCATION'),
    OBJECT_ID('INVENTTRANS')
);
```

## ⚠️ **Important Considerations**

### **1. NOLOCK Hints:**
- Current query uses NOLOCK for performance
- Consider if dirty reads are acceptable
- Alternative: Use READ UNCOMMITTED isolation level

### **2. Data Consistency:**
- Sales order data might be slightly out of sync with inventory
- Consider if real-time accuracy is required

### **3. Maintenance:**
- New indexes require maintenance overhead
- Monitor index fragmentation
- Consider maintenance windows for index creation

## 🎯 **Implementation Plan**

### **Phase 1: Quick Wins (Immediate)**
1. Deploy optimized query
2. Test with small data subset
3. Monitor performance metrics

### **Phase 2: Index Creation (Scheduled)**
1. Create recommended indexes during maintenance window
2. Update statistics after index creation
3. Re-test query performance

### **Phase 3: Advanced Optimization (If Needed)**
1. Consider computed columns
2. Evaluate partitioning strategies
3. Implement alternative query approach if needed

## 📞 **Next Steps**
1. Test the optimized query in development environment
2. Compare execution plans and performance metrics
3. Schedule index creation during maintenance window
4. Monitor production performance after deployment
