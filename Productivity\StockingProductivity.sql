--Detailed list of work ids
/*
SELECT TOP 20 USERID, W<PERSON><PERSON><PERSON><PERSON>SID, <PERSON>TY<PERSON><PERSON><PERSON>, WOR<PERSON><PERSON>TUS, <PERSON>OR<PERSON><PERSON>, WOR<PERSON><PERSON><PERSON>EDUTCDATE
FROM HASTOCKINGPRODUCTIVITYDATEVIEW
WHERE WORKCLOSEDUTCDATE = '10-13-2021' AND WORKTYPE = 2  AND WORKCLASSID = '<PERSON><PERSON><PERSON>On<PERSON>'

SELECT TOP 20 *
	FROM HASTOCKINGPRODUCTIVITYVIEW

SELECT TOP 20*
	FROM HASTOCKINGPRODUCTIVITYDATEVIEW	
*/
--Totals for stocking
SELECT 
	haspv.USERID 		AS UserID
	, whswu.USERNAME 	AS AXName
	, CASE WHEN ISNULL( haspv.WORKCLASSID, '' ) = '' THEN 'InvMov' ELSE haspv.WORKCLASSID END 				AS WorkClassID
	, CASE WHEN ISNULL( haspv.DESCRIPTION, '' ) = '' THEN 'Inventory Movement' ELSE haspv.DESCRIPTION END 	AS WorkClassDescription
	--whsct.WORKTRANSTYPE,
	, CAST(haspv.WOR<PERSON>CL<PERSON>EDUTCDATE AS DATE) AS ClosedUTCDate
	, haspv.COUNTWORKID AS TotalContainers
	, CONVERT( DECIMAL,haspv.SUMOFQTYWORK, 10 ) AS TotalQty
FROM 
	HASTOCKINGPRODUCTIVITYVIEW haspv
	INNER JOIN WHSWORKUSER whswu ON haspv.USERID = whswu.USERID
--LEFT JOIN WHSWORKCLASSTABLE whsct ON whsct.WORKCLASSID = haspv.WORKCLASSID
WHERE 
	--WORKCLOSEDUTCDATE BETWEEN CAST( DATEADD( dd, -1, GETUTCDATE() ) AS DATE ) AND CAST( GETUTCDATE() AS DATE) 
	WORKCLOSEDUTCDATE BETWEEN '1/1/2024' AND CAST( GETUTCDATE() AS DATE) 
	AND haspv.DATAAREAID = 'ha' 
	--AND haspv.WORKCLASSID NOT IN ( 'DirectPick', 'AutoBMult', 'AutoBSngl', 'BulkPick', 'W001' ) 
	AND haspv.WORKTYPE = 2 -- Only the put

/*
SELECT TOP 2*
FROM WHSWORKCLASSTABLE

SELECT 
	TOP 2 *
FROM
	WHSWORKUSER

 SELECT convert(varchar(20), GetDate(), 0)

SELECT TOP 20 *
FROM WHSWORKLINE
WHERE USER ='admin' AND CAST( WORKCLOSEDUTCDATETIME AS DATE) = '10-20-21' AND WORKCLASSID = 'BulkPick' 


*/
