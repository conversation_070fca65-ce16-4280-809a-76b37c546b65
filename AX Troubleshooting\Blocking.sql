
/*
exec sp_columns WHSWOR<PERSON><PERSON>BLE 

Select * From INFORMATION_SCHEMA.COLUMNS Where TABLE_NAME = 'WHSW<PERSON><PERSON><PERSON>BLE'
SELECT TOP 0 * FROM WHSWORKTABLE  -- Just field names
*/
exec sp_blocking;

exec sp_running;

sp_who2;
--kill 175 with statusonly
--SELECT @@VERSION

/*

-- v2
/*
This query uses a common table expression (CTE) to identify the lead blockers and the sessions they are blocking. It then uses a recursive CTE to find 
all sessions that are part of the blocking chain. The final SELECT statement filters out any sessions that are not blocking or being blocked, ensuring that 
no rows are returned if there is no blocking situation.
*/
/*
A Common Table Expression (CTE) is a temporary result set that you can reference within a SELECT, INSERT, UPDATE, or DELETE statement. 
A recursive CTE is a special type of CTE that can reference itself, allowing you to perform recursive operations. This is particularly useful for dealing 
with hierarchical data or for tasks that require iteration, such as finding all levels of management above an employee or calculating the factorial of a number.

A recursive CTE consists of two parts:

Anchor Member: This is the initial query that returns the base result set. It serves as the starting point of the recursion.
Recursive Member: This is the query that references the CTE itself and is combined with the anchor member using a UNION ALL operator.
The recursive process continues until no more rows are returned in the recursive member.

In the context of the blocking processes query, the recursive CTE is used to find all sessions that are involved in a blocking chain. 
The anchor member finds the initial blockers, and the recursive member finds all sessions that are blocked by those blockers, continuing down the chain.

The line CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t in a SQL query is used to join each row from sys.dm_exec_requests (aliased as r) with the corresponding SQL text that is associated with the request’s sql_handle.

Here’s a breakdown of the components:

CROSS APPLY: This is a type of join that allows you to invoke a table-valued function for each row returned by an outer table expression. In this case, sys.dm_exec_sql_text is the table-valued function.
sys.dm_exec_sql_text: This is a dynamic management function that takes a sql_handle as a parameter and returns the text of the SQL batch or stored procedure that is identified by the handle.
r.sql_handle: This is a column in sys.dm_exec_requests that uniquely identifies the SQL text of the command associated with each request.
t: This is the alias given to the result set returned by sys.dm_exec_sql_text.
So, the CROSS APPLY operation allows you to retrieve the actual SQL query text for each session that is involved in the blocking and blocked processes. This information is crucial for understanding 
what each process is executing and can help in troubleshooting blocking issues.

The wait_resource column in SQL Server provides insights into the specific resources that a query is waiting on when it is being blocked. This information can help identify 
the cause of the blocking and assist in troubleshooting performance issues. The wait_resource can refer to various types of resources, such as:

Database_Id:FileId:PageNumber: This format indicates that the query is waiting on a specific page within a database file. For example, ‘60:1:1971533’ would refer to 
database ID 60, file ID 1, and page number 19715331.

Key: This refers to a specific row identified by a key in an index or table. The key wait resource includes the database ID and the Heap or B-Tree (HoBT) ID, which can be mapped to a specific index and table2.
Object: This refers to a specific object, such as a table or index, that the query is waiting on. The object wait resource includes the database ID and the object ID2.
Understanding the wait_resource can help determine if the wait is due to contention on a particular row, page, or table object, and can guide the necessary actions to resolve the blocking, 
such as optimizing queries, adding indexes, or adjusting application logic to reduce contention.
*/


WITH Blockers AS (
    SELECT 
        blocking_session_id AS LeadBlockerSessionID,
        session_id AS BlockedSessionID
    FROM sys.dm_exec_requests
    WHERE blocking_session_id <> 0
),
RecursiveBlockers AS (
    SELECT 
        LeadBlockerSessionID,
        BlockedSessionID
    FROM Blockers
    UNION ALL
    SELECT 
        r.blocking_session_id,
        r.session_id
    FROM sys.dm_exec_requests r
    INNER JOIN RecursiveBlockers rb ON r.blocking_session_id = rb.BlockedSessionID
    WHERE r.blocking_session_id <> 0
)
SELECT 
    r.session_id            AS SQLSessionID,
    r.blocking_session_id   AS BlockedBy,
    r.wait_type,
    r.wait_time / 1000      AS WaitTimeInSec, -- Convert milliseconds to seconds
    r.wait_resource,
    DB_NAME(r.database_id)  AS [Database_Name],
    t.text                  AS sql_text,
    --s.login_name           AS user_name,
    s.host_name,
    --LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) AS ax_info_trimmed,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) - 1)
    END AS AXUserName,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1))
    END                 
                        AS AXSessionID,
    s.status            AS SQLStatus,
    r.command           AS Command,
    qp.query_plan       AS QueryPlan,
    r.plan_handle       AS PlanHandle,
    r.query_hash        AS QueryHash,
    r.query_plan_hash   AS QueryPlanHash,
    CASE 
        WHEN r.session_id IN (SELECT LeadBlockerSessionID FROM Blockers) THEN 1
        ELSE 0
    END AS LeadBlocker
FROM sys.dm_exec_requests r
INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
CROSS APPLY sys.dm_exec_query_plan(r.plan_handle) qp
WHERE r.session_id IN (SELECT BlockedSessionID FROM RecursiveBlockers)
   OR r.session_id IN (SELECT LeadBlockerSessionID FROM Blockers)
ORDER BY WaitTimeInSec DESC, leadblocker DESC, r.session_id;

SELECT 
    r.session_id AS SQLSessionID,
    r.blocking_session_id AS BlockedBy,
    r.wait_type,
    r.wait_time / 1000 AS WaitTimeInSec, -- Convert milliseconds to seconds
    r.wait_resource,
    DB_NAME(r.database_id) AS [Database_Name],
    t.text AS sql_text,
    s.login_name AS user_name,
    s.host_name,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) - 1)
    END AS AXUserName,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1))
    END AS AXSessionID,
    s.status AS SQLStatus,
    r.command AS Command,
    qp.query_plan AS QueryPlan,
    r.plan_handle AS PlanHandle,
    r.query_hash AS QueryHash,
    r.query_plan_hash AS QueryPlanHash,
    CASE 
        WHEN r.session_id IN (
            SELECT blocking_session_id
            FROM sys.dm_exec_requests
            WHERE blocking_session_id <> 0
        ) THEN 1
        ELSE 0
    END AS LeadBlocker
FROM sys.dm_exec_requests r
INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
CROSS APPLY sys.dm_exec_query_plan(r.plan_handle) qp
WHERE r.session_id IN (
    SELECT blocking_session_id
    FROM sys.dm_exec_requests
    WHERE blocking_session_id <> 0
    UNION ALL
    SELECT r1.session_id
    FROM sys.dm_exec_requests r1
    INNER JOIN sys.dm_exec_requests r2 ON r1.blocking_session_id = r2.session_id
    WHERE r1.blocking_session_id <> 0
)
ORDER BY WaitTimeInSec DESC, LeadBlocker DESC, r.session_id;


-- Same query without CTEs

SELECT 
    r.session_id AS SQLSessionID,
    r.blocking_session_id AS BlockedBy,
  
    r.wait_time / 1000 AS WaitTimeInSec, -- Convert milliseconds to seconds
   
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) - 1)
    END AS AXUserName,
    CASE 
        WHEN LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))) = '' THEN NULL
        ELSE SUBSTRING(LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1, CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1) + 1) - CHARINDEX(' ', LTRIM(RTRIM(CAST(s.context_info AS VARCHAR(128)))), 1))
    END AS AXSessionID,
   
    s.host_name,
    r.command AS Command,
    s.status AS SQLStatus,
    DB_NAME(r.database_id) AS [Database_Name],
    t.text AS sql_text,
    r.wait_type,
    DB_NAME(r.database_id) AS [Database_Name],
    r.wait_resource,
    qp.query_plan AS QueryPlan,
    r.plan_handle AS PlanHandle,
    r.query_hash AS QueryHash,
    r.query_plan_hash AS QueryPlanHash,
    --s.login_name AS user_name,
    CASE 
        WHEN r.session_id IN (
            SELECT blocking_session_id
            FROM sys.dm_exec_requests
            WHERE blocking_session_id <> 0
        ) THEN 1
        ELSE 0
    END AS LeadBlocker
FROM sys.dm_exec_requests r
INNER JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
CROSS APPLY sys.dm_exec_query_plan(r.plan_handle) qp
WHERE r.session_id IN (
    SELECT session_id
    FROM sys.dm_exec_requests
    WHERE blocking_session_id <> 0
    UNION ALL
    SELECT r1.session_id
    FROM sys.dm_exec_requests r1
    INNER JOIN sys.dm_exec_requests r2 ON r1.blocking_session_id = r2.session_id
    WHERE r1.blocking_session_id <> 0
)
ORDER BY  WaitTimeInSec DESC, LeadBlocker DESC, r.session_id;



-- Non-microsoft stored procedures

SELECT name
FROM sys.procedures
WHERE type = 'P' AND is_ms_shipped = 0
ORDER BY name;

-- SQLMaestros

-- https://www.sqlservergeeks.com/sql-server-how-to-find-the-lead-blocker-culprit-spid-for-all-blocking/

-- Identifies the lead blocker

select 
    loginame
    , cpu
    , memusage, physical_io
    , * 
from  
    master..sysprocesses a
where  
    exists ( select b.*
            from master..sysprocesses b
            where b.blocked > 0 and b.blocked = a.spid ) 
    and not exists ( select b.*
            from master..sysprocesses b
            where b.blocked > 0 and b.spid = a.spid ) 
order by spid

-- Identifies who is blocked

select * from sys.sysprocesses where  spid >= 50 and blocked <> 0
/*
This line is a **subquery** that retrieves the actual SQL statement being executed by the blocked session. It uses the `sys.dm_exec_sql_text` dynamic management function, 
which takes a `sql_handle` as an argument. The `sql_handle` is a token that uniquely identifies the batch or stored procedure that the request is running.

The subquery is selecting the `TEXT` column from the `sys.dm_exec_sql_text` function, which contains the text of the SQL batch or stored procedure. This `TEXT` is 
then aliased as `'SQL Text'` to be more readable in the output of the main query. Essentially, this line is used to show what SQL code is being run by the session that is currently being blocked.
*/
SELECT 
    blocking_session_id AS 'Blocking Session ID',
    session_id AS 'Blocked Session ID',
    wait_type,
    wait_time,
    wait_resource,
    DB_NAME(database_id) AS 'Database Name',
    (SELECT TEXT FROM sys.dm_exec_sql_text(sql_handle)) AS 'SQL Text'
FROM sys.dm_exec_requests
WHERE blocking_session_id <> 0;
 

-- All Locks
SELECT * 
FROM 
    sys.dm_os_waiting_tasks
WHERE
    wait_type LIKE 'LCK%'

-- Stop the query store
--ALTER DATABASE xDAX_PROD xSETx xxxxxxQUERY_STORExxx = OFF (forced)

-- query store
SELECT
    *
    , current_storage_size_mb 
FROM sys.database_query_store_options;

-- Non user sessions, not killable
SELECT *
FROM sys.dm_exec_sessions
WHERE
    is_user_process = 0 -- 1 user sessions
    AND session_id IN (138, 305);

-- Blocked processes
SELECT
    spid,
    status,
    --loginame=SUBSTRING(loginame,1,12),
    RTRIM(nt_username)       AS User_Name,
    hostname=SUBSTRING(hostname,1, 12),
    RTRIM(program_name)     AS Progran_Name,
    cmd,
    blk = CONVERT(char(3), blocked),
    dbname=SUBSTRING(DB_NAME(dbid),1, 10),
    cmd,
    waittype
FROM 
    master.dbo.sysprocesses
WHERE 
    spid IN (SELECT blocked FROM master.dbo.sysprocesses)
    
SELECT 
    spid
    , kpid
    , blocked
    , waittype
    , lastwaittype
    --, waitresource
    , cpu
    ,physical_io
    , memusage
    , [status]
    , cmd
FROM sys.sysprocesses
WHERE
    hostprocess = ''  -- > '' users
    AND spid IN (138, 305);


kill 265 WITH STATUSONLY

SELECT * 
FROM sys.dm_os_waiting_tasks
WHERE session_id IN (138, 305);

SELECT
    [owt].[session_id],
    [owt].[exec_context_id],
    [ot].[scheduler_id],
    [owt].[wait_duration_ms],
    [owt].[wait_type],
    [owt].[blocking_session_id],
    [owt].[resource_description],
    CASE [owt].[wait_type]
        WHEN N'CXPACKET' THEN
            RIGHT ([owt].[resource_description],
                CHARINDEX (N'=', REVERSE ([owt].[resource_description])) - 1)
        ELSE NULL
    END AS [Node ID],
    --[es].[program_name],
    [est].text,
    [er].[database_id],
    [eqp].[query_plan],
    [er].[cpu_time]
FROM sys.dm_os_waiting_tasks [owt]
INNER JOIN sys.dm_os_tasks [ot] ON
    [owt].[waiting_task_address] = [ot].[task_address]
INNER JOIN sys.dm_exec_sessions [es] ON
    [owt].[session_id] = [es].[session_id]
INNER JOIN sys.dm_exec_requests [er] ON
    [es].[session_id] = [er].[session_id]
OUTER APPLY sys.dm_exec_sql_text ([er].[sql_handle]) [est]
OUTER APPLY sys.dm_exec_query_plan ([er].[plan_handle]) [eqp]
WHERE
    [es].[is_user_process] = 1
ORDER BY
    [owt].[session_id],
    [owt].[exec_context_id];
GO

--The following query can help you identify the session_id that you want to kill:

SELECT conn.session_id, host_name, program_name,
    nt_domain, login_name, connect_time, last_request_end_time 
FROM sys.dm_exec_sessions AS sess
JOIN sys.dm_exec_connections AS conn
   ON sess.session_id = conn.session_id
WHERE
    1 = 1
    --AND conn.session_id IN (138,305);



*/