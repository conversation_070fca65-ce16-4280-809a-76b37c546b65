/*
AUTHOR: <PERSON>
Date 2/8/2022
Goal: Pull SKU cubic scan volumes based on what's ON HAND
Description: I will query the on hand and then left join it with the sku WHSPHYSDIMUOM. 
			Be aware that we will use the onhand and the physical invt. Physical int is everything we have data on
MODIFICATIONS: NA
*/

DROP TABLE IF EXISTS #HOLDER;

SELECT    --idim.INVENTLOCATIONID AS 'Warehouse'
INV.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID,
		(inv.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID )	AS 'ON_HAND_SKU'
		--, inv.ITEMID AS 'Style'
		--, idim.INVENTCOLORID AS 'Color'
		--, idim.INVENTSIZEID	AS 'Size'
		, itran.DESCRIPTION AS 'Description'
		, idim.[INVENTSTATUSID] AS 'Status'
		, CONVERT(DECIMAL(10,0),AVG(isum.PHYSICALINVENT),0) AS 'Qty'
		, CONVERT(DECIMAL(10,0),AVG(isum.RESERVPHYSICAL),0) AS 'Reserve'
		, CONVERT(DECIMAL(10,0),AVG(isum.ONORDER),0) AS 'OnOrder'
		-- , CONVERT(DECIMAL(10,0),SUM(isum.PHYSICALINVENT),0)
		-- 	-CONVERT(DECIMAL(10,0),SUM(isum.RESERVPHYSICAL),0)
		-- 	-CONVERT(DECIMAL(10,0),SUM(isum.ONORDER),0) AS 'Available'
		, idim.[LICENSEPLATEID] AS 'LicensePlate'
		, idim.[WMSLOCATIONID] AS 'Bin'
		, wmsloc.LOCPROFILEID AS 'Bin Type'
		--, ISNULL(fixloc.WMSLOCATIONID,'') AS 'FixedBin'
		, convert(decimal(10,2),prod.COST,0) AS 'Cost'
		,(CONVERT(DECIMAL(10,0),SUM(isum.PHYSICALINVENT),0)
			-CONVERT(DECIMAL(10,0),SUM(isum.RESERVPHYSICAL),0)
			-CONVERT(DECIMAL(10,0),SUM(isum.ONORDER),0)) * convert(decimal(10,2),prod.COST,0) AS 'ExtCost'

INTO #HOLDER
FROM         DAX_PROD.dbo.INVENTDIM idim 
				INNER JOIN DAX_PROD.dbo.INVENTSUM isum ON idim.INVENTDIMID = isum.INVENTDIMID 
				INNER JOIN DAX_PROD.dbo.INVENTTABLE inv ON isum.ITEMID = inv.ITEMID
				INNER JOIN DAX_PROD.dbo.WMSLOCATION wmsloc ON idim.WMSLOCATIONID = wmsloc.WMSLOCATIONID
				INNER JOIN DAX_PROD.dbo.ECORESPRODUCTTRANSLATION itran ON inv.PRODUCT = itran.PRODUCT
				LEFT JOIN DAX_PROD.dbo.[WHSPRODUCTVARIANTFIXEDLOCATION] fixloc ON idim.[WMSLOCATIONID] = fixloc.WMSLOCATIONID
				left join [DAX_PROD].[dbo].[HARMSPRODUCT] prod ON (inv.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID )= prod.ITEMSKU

WHERE     isum.PHYSICALINVENT <> 0 AND isum.PHYSICALINVENT < 99999 
			AND idim.INVENTLOCATIONID = '4010' 
			AND idim.INVENTSITEID = 'HA USA'
			AND (wmsloc.LOCPROFILEID IN ('Bulk','Inbound','No LP Track') OR wmsloc.LOCPROFILEID LIKE 'Picking%')
			AND inv.ITEMID NOT IN ('30991','3333','9999','9997')
			AND idim.INVENTSIZEID <> '' AND idim.INVENTCOLORID <> ''

GROUP BY idim.INVENTLOCATIONID, inv.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, itran.DESCRIPTION, idim.[INVENTSTATUSID], idim.[LICENSEPLATEID], idim.[WMSLOCATIONID], wmsloc.LOCPROFILEID, fixloc.WMSLOCATIONID, prod.COST

UNION

SELECT    --idim.INVENTLOCATIONID AS 'Warehouse'
INV.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID,
		(inv.ITEMID + '-' + idim.INVENTCOLORID)	AS 'SKU'
		--, inv.ITEMID AS 'Style'
		--, idim.INVENTCOLORID AS 'Color'
		--, idim.INVENTSIZEID	AS 'Size'
		, itran.DESCRIPTION AS 'Description'
		, idim.[INVENTSTATUSID] AS 'Status'
		, CONVERT(DECIMAL(10,0),AVG(isum.PHYSICALINVENT),0) AS 'Qty'
		, CONVERT(DECIMAL(10,0),AVG(isum.RESERVPHYSICAL),0) AS 'Reserve'
		, CONVERT(DECIMAL(10,0),AVG(isum.ONORDER),0) AS 'OnOrder'
		-- , CONVERT(DECIMAL(10,0),SUM(isum.PHYSICALINVENT),0)
		-- 	-CONVERT(DECIMAL(10,0),SUM(isum.RESERVPHYSICAL),0)
		-- 	-CONVERT(DECIMAL(10,0),SUM(isum.ONORDER),0) AS 'Available'
		, idim.[LICENSEPLATEID] AS 'LicensePlate'
		, idim.[WMSLOCATIONID] AS 'Bin'
		, wmsloc.LOCPROFILEID AS 'Bin Type'
		--, ISNULL(fixloc.WMSLOCATIONID,'') AS 'FixedBin'
		, convert(decimal(10,2),prod.COST,0) AS 'Cost'
		,(CONVERT(DECIMAL(10,0),SUM(isum.PHYSICALINVENT),0)
			-CONVERT(DECIMAL(10,0),SUM(isum.RESERVPHYSICAL),0)
			-CONVERT(DECIMAL(10,0),SUM(isum.ONORDER),0)) * convert(decimal(10,2),prod.COST,0) AS 'ExtCost'
FROM         DAX_PROD.dbo.INVENTDIM idim 
				INNER JOIN DAX_PROD.dbo.INVENTSUM isum ON idim.INVENTDIMID = isum.INVENTDIMID 
				INNER JOIN DAX_PROD.dbo.INVENTTABLE inv ON isum.ITEMID = inv.ITEMID
				INNER JOIN DAX_PROD.dbo.WMSLOCATION wmsloc ON idim.WMSLOCATIONID = wmsloc.WMSLOCATIONID
				INNER JOIN DAX_PROD.dbo.ECORESPRODUCTTRANSLATION itran ON inv.PRODUCT = itran.PRODUCT
				LEFT JOIN DAX_PROD.dbo.[WHSPRODUCTVARIANTFIXEDLOCATION] fixloc ON idim.[WMSLOCATIONID] = fixloc.WMSLOCATIONID
				left join [DAX_PROD].[dbo].[HARMSPRODUCT] prod ON (inv.ITEMID + '-' + idim.INVENTCOLORID) = prod.ITEMSKU

WHERE     isum.PHYSICALINVENT <> 0 AND isum.PHYSICALINVENT < 99999 
			AND idim.INVENTLOCATIONID = '4010' 
			AND idim.INVENTSITEID = 'HA USA'
			AND (wmsloc.LOCPROFILEID IN ('Bulk','Inbound','No LP Track') OR wmsloc.LOCPROFILEID LIKE 'Picking%')
			AND inv.ITEMID NOT IN ('30991','3333','9999','9997')
			AND idim.INVENTSIZEID = '' AND idim.INVENTCOLORID <> ''

GROUP BY idim.INVENTLOCATIONID, inv.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, itran.DESCRIPTION, idim.[INVENTSTATUSID], idim.[LICENSEPLATEID], idim.[WMSLOCATIONID], wmsloc.LOCPROFILEID, fixloc.WMSLOCATIONID, prod.COST

UNION

SELECT    --idim.INVENTLOCATIONID AS 'Warehouse'
INV.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID,
		inv.ITEMID AS 'SKU'
		--, inv.ITEMID AS 'Style'
		--, idim.INVENTCOLORID AS 'Color'
		--, idim.INVENTSIZEID	AS 'Size'
		, itran.DESCRIPTION AS 'Description'
		, idim.[INVENTSTATUSID] AS 'Status'
		, CONVERT(DECIMAL(10,0),AVG(isum.PHYSICALINVENT),0) AS 'Qty'
		, CONVERT(DECIMAL(10,0),AVG(isum.RESERVPHYSICAL),0) AS 'Reserve'
		, CONVERT(DECIMAL(10,0),AVG(isum.ONORDER),0) AS 'OnOrder'
		-- , CONVERT(DECIMAL(10,0),SUM(isum.PHYSICALINVENT),0)
		-- 	-CONVERT(DECIMAL(10,0),SUM(isum.RESERVPHYSICAL),0)
		-- 	-CONVERT(DECIMAL(10,0),SUM(isum.ONORDER),0) AS 'Available'
		, idim.[LICENSEPLATEID] AS 'LicensePlate'
		, idim.[WMSLOCATIONID] AS 'Bin'
		, wmsloc.LOCPROFILEID AS 'Bin Type'
		--, ISNULL(fixloc.WMSLOCATIONID,'') AS 'FixedBin'
		, convert(decimal(10,2),prod.COST,0) AS 'Cost'
		,(CONVERT(DECIMAL(10,0),SUM(isum.PHYSICALINVENT),0)
			-CONVERT(DECIMAL(10,0),SUM(isum.RESERVPHYSICAL),0)
			-CONVERT(DECIMAL(10,0),SUM(isum.ONORDER),0)) * convert(decimal(10,2),prod.COST,0) AS 'ExtCost'
FROM         DAX_PROD.dbo.INVENTDIM idim 
				INNER JOIN DAX_PROD.dbo.INVENTSUM isum ON idim.INVENTDIMID = isum.INVENTDIMID 
				INNER JOIN DAX_PROD.dbo.INVENTTABLE inv ON isum.ITEMID = inv.ITEMID
				INNER JOIN DAX_PROD.dbo.WMSLOCATION wmsloc ON idim.WMSLOCATIONID = wmsloc.WMSLOCATIONID
				INNER JOIN DAX_PROD.dbo.ECORESPRODUCTTRANSLATION itran ON inv.PRODUCT = itran.PRODUCT
				LEFT JOIN DAX_PROD.dbo.[WHSPRODUCTVARIANTFIXEDLOCATION] fixloc ON idim.[WMSLOCATIONID] = fixloc.WMSLOCATIONID
				left join [DAX_PROD].[dbo].[HARMSPRODUCT] prod ON inv.ITEMID = prod.ITEMSKU

WHERE     isum.PHYSICALINVENT <> 0 AND isum.PHYSICALINVENT < 99999
			AND idim.INVENTLOCATIONID = '4010' 
			AND idim.INVENTSITEID = 'HA USA'
			AND (wmsloc.LOCPROFILEID IN ('Bulk','Inbound','No LP Track') OR wmsloc.LOCPROFILEID LIKE 'Picking%')
			AND inv.ITEMID NOT IN ('30991','3333','9999','9997')
			AND idim.INVENTSIZEID = '' AND idim.INVENTCOLORID = ''

GROUP BY idim.INVENTLOCATIONID, inv.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, itran.DESCRIPTION, idim.[INVENTSTATUSID], idim.[LICENSEPLATEID], idim.[WMSLOCATIONID], wmsloc.LOCPROFILEID, fixloc.WMSLOCATIONID, prod.COST
ORDER BY inv.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, wmsloc.LOCPROFILEID 


DROP TABLE IF EXISTS #DIMENSION;

SELECT #HOLDER.*,
case
		when isnull([ECORESITEMCOLORNAME],'') = '' THEN	WHSPHYSDIMUOM.ITEMID 
		when isnull([ECORESITEMSIZENAME],'') = '' THEN	WHSPHYSDIMUOM.ITEMID + '-' + [ECORESITEMCOLORNAME]
		ELSE WHSPHYSDIMUOM.ITEMID + '-' + [ECORESITEMCOLORNAME] + '-' + [ECORESITEMSIZENAME]	
	end AS 'ALL_SKU',
case
		when isnull([ECORESITEMCOLORNAME],'') = '' THEN	WHSPHYSDIMUOM.ITEMID 
		when isnull([ECORESITEMSIZENAME],'') = '' THEN	WHSPHYSDIMUOM.ITEMID + '-' + [ECORESITEMCOLORNAME]
		ELSE WHSPHYSDIMUOM.ITEMID +'-' + [ECORESITEMSIZENAME]	
	end AS 'ALL_INVT_ITEM-SIZE',
    CONVERT(DECIMAL(10,2),WHSPHYSDIMUOM.[WEIGHT],0) AS 'Weight'
    ,CONVERT(DECIMAL(10,2),WHSPHYSDIMUOM.[DEPTH],0) AS 'Depth'
    ,CONVERT(DECIMAL(10,2),WHSPHYSDIMUOM.[HEIGHT],0) AS 'Height'
    ,CONVERT(DECIMAL(10,2),WHSPHYSDIMUOM.[WIDTH],0) AS 'Width'
	,CONVERT(DECIMAL(10,2),ISNULL(WHSPHYSDIMUOM.[DEPTH],0) * ISNULL(WHSPHYSDIMUOM.[WIDTH],0) * ISNULL(WHSPHYSDIMUOM.[HEIGHT],0),0) AS 'Volume' 
INTO #DIMENSION
FROM #HOLDER
LEFT JOIN WHSPHYSDIMUOM ON #HOLDER.ITEMID = WHSPHYSDIMUOM.ITEMID AND #HOLDER.INVENTCOLORID = WHSPHYSDIMUOM.ECORESITEMCOLORNAME AND #HOLDER.INVENTSIZEID = ECORESITEMSIZENAME

DROP TABLE IF EXISTS #DIMENSION2;

SELECT *,
    --AVG(Volume) OVER(ORDER BY [ALL_INVT_ITEM-SIZE]) AS AVG_VOLUME_ITEMSIZE
    AVG(Volume) OVER(PARTITION BY [ALL_INVT_ITEM-SIZE]) AS AVG_VOLUME_ITEMSIZE
INTO #DIMENSION2
FROM #DIMENSION
WHERE 
    [bin type] NOT IN ('No LP Track', 'Inbound')

SELECT *,
    CASE WHEN Volume <> 0 THEN CAST( (Volume/AVG_VOLUME_ITEMSIZE) AS DECIMAL(12,2) )  ELSE 0 END AS '%OFFSET'
FROM #DIMENSION2