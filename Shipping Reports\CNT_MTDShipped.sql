--Month to Date - By day of 24 hours

/*SELECT TT.Container, TT.KYShippedDate
FROM
(*/
SELECT  CNTs.ContainerID                            AS Container, 
CASE    WHEN DATEPART( mm, CNTs.modifieddatetime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, CNTs.modifieddatetime ) -- Daylight Savings Months 
        WHEN DATEPART( mm, CNTs.modifieddatetime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, CNTs.modifieddatetime ) -- No DST
        WHEN DATEPART( mm, CNTs.modifieddatetime ) = 3 
        THEN CASE   WHEN DATEPART( dd, CNTs.modifieddatetime ) < 8 OR DATEPART( dd, CNTs.modifieddatetime ) > 14 THEN  DATEADD( hh, - 5, CNTs.modifieddatetime ) -- No DST
                    WHEN DATEPART( dd, CNTs.modifieddatetime ) - DATEPART( w, CNTs.modifieddatetime ) + 1  >= 8 THEN  DATEADD(hh, - 4, CNTs.modifieddatetime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, CNTs.modifieddatetime )
             END
        WHEN DATEPART( dd, CNTs.modifieddatetime ) - DATEPART( w, CNTs.modifieddatetime ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, CNTs.modifieddatetime )
        ELSE DATEADD( hh, - 4, CNTs.modifieddatetime )
END                             AS KYShippedDate, 
WTbl.WaveTemplateName           AS WaveTemplate, 
ShipTbl.ordernum                AS OrderNum,
Sum ( CNTLine.Qty )             AS Qty
FROM WHSContainerTable          AS CNTs
LEFT JOIN WHSShipmentTable      AS ShipTbl
ON CNTs.ShipmentId          = ShipTbl.ShipmentId
LEFT JOIN WHSWaveTable          AS WTbl
ON ShipTbl.WaveId          = WTbl.waveid
LEFT JOIN WHSContainerLine      AS CNTLine
ON CNTS.ContainerID     = CNTLine.ContainerID
WHERE CNTs.ModifiedDateTime BETWEEN  DATEADD( hh, 4, DATEADD( ss, -DATEPART( hh, GETUTCDATE() ) * 3600 - DATEPART( mi, GETUTCDATE() ) * 60  - DATEPART( ss, GETUTCDATE() ), DATEADD(dd, -DATEPART(dd, GETUTCDATE() ), GETUTCDATE() )  + 1 ) )  AND GETUTCDATE() -- Accurate for the last 15 days
AND ISNULL( CNTs.ShipCarrierTrackingNum, '' ) <> '' -- Shipped
--AND    ShipTbl.ShipmentStatus = 5 --Shipped
GROUP BY CNTs.ContainerID, CNTs.modifieddatetime, WTbl.WaveTemplateName, ShipTbl.ordernum
--ORDER BY CNTs.modifieddatetime
/*) AS TT
WHERE TT.KYShippedDate BETWEEN '11/26/2021 07:00:00 pm' AND '11/26/2021 07:59:59 pm'
*/

-- Brad report 2/10/2022

SELECT  CNTs.ContainerID                            AS Container, 
CASE    WHEN DATEPART( mm, CNTs.modifieddatetime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, CNTs.modifieddatetime ) -- Daylight Savings Months 
        WHEN DATEPART( mm, CNTs.modifieddatetime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, CNTs.modifieddatetime ) -- No DST
        WHEN DATEPART( mm, CNTs.modifieddatetime ) = 3 
        THEN CASE   WHEN DATEPART( dd, CNTs.modifieddatetime ) < 8 OR DATEPART( dd, CNTs.modifieddatetime ) > 14 THEN  DATEADD( hh, - 5, CNTs.modifieddatetime ) -- No DST
                    WHEN DATEPART( dd, CNTs.modifieddatetime ) - DATEPART( w, CNTs.modifieddatetime ) + 1  >= 8 THEN  DATEADD(hh, - 4, CNTs.modifieddatetime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, CNTs.modifieddatetime )
             END
        WHEN DATEPART( dd, CNTs.modifieddatetime ) - DATEPART( w, CNTs.modifieddatetime ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, CNTs.modifieddatetime )
        ELSE DATEADD( hh, - 4, CNTs.modifieddatetime )
END                             AS KYShippedDate, 
--WTbl.WaveTemplateName           AS WaveTemplate, 
ShipTbl.ordernum                AS OrderNum,
Sum ( CNTLine.Qty )             AS Qty
FROM WHSContainerTable          AS CNTs
LEFT JOIN WHSShipmentTable      AS ShipTbl
ON CNTs.ShipmentId          = ShipTbl.ShipmentId
/*LEFT JOIN WHSWaveTable          AS WTbl
ON ShipTbl.WaveId          = WTbl.waveid*/
LEFT JOIN WHSContainerLine      AS CNTLine
ON CNTS.ContainerID     = CNTLine.ContainerID
WHERE CNTs.ModifiedDateTime BETWEEN  '3/31/2021 05:00:00:000 AM'  AND GETUTCDATE() -- Accurate for the last 15 days
AND ISNULL( CNTs.ShipCarrierTrackingNum, '' ) <> '' -- Shipped
AND    CNTs.HAWHSSHIPPERID = 'MHX' --Shipped by the AB
GROUP BY CNTs.ContainerID, CNTs.modifieddatetime, ShipTbl.ordernum
ORDER BY CNTs.MODIFIEDDATETIME