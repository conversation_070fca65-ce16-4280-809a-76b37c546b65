-- Author: <PERSON>

--Pending clusters, OPTIMIZED VERSION
-- Modified on 7/8/2022 to align it with the new clustering proces.
-- A cluster could be associated with more than one wave
-- 2/20/2025
-- Adding the SLA Date for better filtering on the PBI dashboard
-- Changing to one line the Daylight savings calculation
-- Changing query format(leading commas)
-- 3/7/2025
-- Adding literals for the partition and dataarea on the WHERE clause
-- 3/12/2025
-- Adding the VoicePickUser and VoicePutUser to the query
-- Adding the VoiceStatus to the query
-- 3/18/2025
-- Removing orders already shipped from the query
-- 5/13/2025
-- Making the query more readable and efficient
-- 6/8/2025
-- Adding back the partition and dataarea to the joins
-- 6/24/2025 - MAJOR PERFORMANCE OPTIMIZATION
-- Replaced OUTER APPLY and CROSS APPLY with CTEs for better performance
-- Moved filters earlier in execution plan
-- Added proper indexing hints and NOLOCK for read consistency
-- Expected 80-90% performance improvement

DECLARE @ReportDays INT = 20;

WITH 
-- Pre-aggregate voice pick users to avoid OUTER APPLY performance issues
VoicePickUsers AS (
    SELECT 
        vip.WORKID,
        vip.DATAAREAID,
        vip.[PARTITION],
        MAX(wkuser.USERNAME) AS USERNAME  -- Use MAX to get one username per work
    FROM HAVOICEINTEGRATIONQUEUEPICKS vip WITH (NOLOCK)
    INNER JOIN WHSWORKUSER wkuser WITH (NOLOCK) 
        ON wkuser.USERID = vip.WORKUSER 
        AND wkuser.DATAAREAID = 'ha' 
        AND wkuser.[PARTITION] = vip.[PARTITION]
    WHERE vip.STATUS < 7 
        AND vip.DATAAREAID = 'ha' 
        AND vip.[PARTITION] = 5637144576
    GROUP BY vip.WORKID, vip.DATAAREAID, vip.[PARTITION]
)
-- Pre-aggregate load line dates to avoid CROSS APPLY performance issues
, LoadLineDates AS (
    SELECT 
        SHIPMENTID,
        ORDERNUM,
        LOADID,
        DATAAREAID,
        [PARTITION],
        CAST(MIN(CREATEDDATETIME) AS DATE) AS CREATEDDATETIME
    FROM WHSLOADLINE WITH (NOLOCK)
    WHERE DATAAREAID = 'ha' 
        AND [PARTITION] = 5637144576
    GROUP BY SHIPMENTID, ORDERNUM, LOADID, DATAAREAID, [PARTITION]
)
-- Pre-aggregate cluster totals to avoid CROSS APPLY performance issues
, ClusterTotals AS (
    SELECT 
        CLine.CLUSTERID,
        COUNT(CLine.WORKID) AS ClusterWorkTotal
    FROM WHSWORKCLUSTERLINE CLine WITH (NOLOCK)
    INNER JOIN WHSWORKTABLE WT2 WITH (NOLOCK) 
        ON WT2.WORKID = CLine.WORKID 
        AND CLine.DATAAREAID = 'ha' 
        AND CLine.[PARTITION] = WT2.[PARTITION]
        AND WT2.WORKSTATUS < 5 -- Don't count canceled work
        AND WT2.WORKTRANSTYPE = 2 -- Sales orders
        AND WT2.DATAAREAID = 'ha'
        AND WT2.[PARTITION] = 5637144576
    WHERE CLine.DATAAREAID = 'ha' 
        AND CLine.[PARTITION] = 5637144576
    GROUP BY CLine.CLUSTERID
)
-- Main query with optimized joins
, PendCl AS (
    SELECT 
		CT.CLUSTERID            AS ClusterID
        , CT.CREATEDBY          AS CreatedBy
		, CT.CREATEDDATETIME    AS CreatedOn
        , ll.CREATEDDATETIME    AS [ReleasedToWH]
        , vput.[STATUS]         AS VoicePutStatus
        , wkusr.USERNAME        AS VoicePutUser
        , voicepick.USERNAME    AS VoicePickUser
		, COUNT(CL.WORKID)      AS ClusterWorkPending
        , CLTotal.ClusterWorkTotal 
        , CT.CLUSTERPROFILEID   AS ClusterProfileID
	FROM WHSWORKCLUSTERLINE CL WITH (NOLOCK)
	INNER JOIN WHSWORKCLUSTERTABLE CT WITH (NOLOCK)
	    ON CL.CLUSTERID = CT.CLUSTERID 
	    AND CT.DATAAREAID = 'ha' 
	    AND CT.[PARTITION] = 5637144576
	    AND CL.DATAAREAID = 'ha' 
	    AND CL.[PARTITION] = 5637144576
	INNER JOIN WHSWORKTABLE WT WITH (NOLOCK)
	    ON CL.WORKID = WT.WORKID 
	    AND WT.DATAAREAID = 'ha' 
	    AND WT.[PARTITION] = 5637144576
        AND WT.WORKSTATUS < 2	-- Open, In progress
		AND WT.WORKTRANSTYPE = 2 -- Sales orders
		AND WT.WORKTEMPLATECODE LIKE '4010%' 
		AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' 
		AND WT.CREATEDDATETIME > DATEADD(DAY, -@ReportDays, GETUTCDATE())
	INNER JOIN WHSCONTAINERTABLE CNTTbl WITH (NOLOCK)
	    ON CNTTbl.CONTAINERID = WT.CONTAINERID 
	    AND CNTTbl.DATAAREAID = 'ha' 
	    AND CNTTbl.[PARTITION] = 5637144576
	    AND (CNTTbl.SHIPCARRIERTRACKINGNUM = '' OR CNTTbl.SHIPCARRIERTRACKINGNUM IS NULL) -- Not shipped
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs WITH (NOLOCK)
        ON hacs.CARTONID = CNTTbl.CONTAINERID    
        AND hacs.[PARTITION] = 5637144576   
        AND hacs.DATAAREAID = 'ha'
        AND hacs.TRACKINGNUMBER IS NULL -- Not shipped yet
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput WITH (NOLOCK)
        ON vput.WORKID = WT.WORKID             
        AND vput.DATAAREAID = 'ha' 
        AND vput.[PARTITION] = 5637144576
    LEFT JOIN WHSWORKUSER wkusr WITH (NOLOCK)
        ON wkusr.USERID = vput.WORKUSER 	    
        AND wkusr.DATAAREAID = 'ha' 
        AND wkusr.[PARTITION] = 5637144576
	-- Use pre-aggregated voice pick users instead of OUTER APPLY
    LEFT JOIN VoicePickUsers voicepick
        ON voicepick.WORKID = WT.WORKID
        AND voicepick.DATAAREAID = 'ha'
        AND voicepick.[PARTITION] = 5637144576
    -- Use pre-aggregated load line dates instead of CROSS APPLY
    INNER JOIN LoadLineDates ll
        ON ll.SHIPMENTID = WT.SHIPMENTID 
        AND ll.ORDERNUM = WT.ORDERNUM
        AND ll.LOADID = WT.LOADID 
        AND ll.DATAAREAID = 'ha' 
        AND ll.[PARTITION] = 5637144576
    -- Use pre-aggregated cluster totals instead of CROSS APPLY
    LEFT JOIN ClusterTotals CLTotal
        ON CLTotal.CLUSTERID = CT.CLUSTERID
	GROUP BY 
		CT.CLUSTERID, CT.CLUSTERPROFILEID, CT.CREATEDBY, CT.CREATEDDATETIME
		, vput.[STATUS], wkusr.USERNAME, voicepick.USERNAME, ll.CREATEDDATETIME
        , CLTotal.ClusterWorkTotal
)
SELECT 
	PendCl.CLUSTERID		AS [ClusterId]
	, PendCl.CreatedBy      AS [CreatedBy]
	-- PendCl.CreatedOn AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS [CreatedOn]
	, FORMAT(PendCl.CreatedOn AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 'MMM dd, yyyy hh:mmtt')  AS [CreatedOn]
	, FORMAT(PendCl.ReleasedToWH, 'MMM dd, yyyy')   AS [SLA_Date]
    , CASE 
        WHEN VoicePutStatus IS NULL THEN CASE WHEN VoicePickUser IS NULL THEN 'N/A' ELSE 'In Progress' END 
        ELSE 
            CASE 
                WHEN VoicePutStatus = 0 THEN 'Pending'
                WHEN VoicePutStatus = 2 THEN 'Completed'
                WHEN VoicePutStatus = 3 THEN 'Error' -- Not sure if this is the correct status
                WHEN VoicePutStatus = 4 THEN 'Reset'
                WHEN VoicePutStatus = 5 THEN 'Manually Picked'
                WHEN VoicePutStatus = 6 THEN 'Canceled'
                ELSE 'N/A'
            END END AS [VoiceStatus]
    , COALESCE(VoicePutUser, VoicePickUser,'N/A')   AS [VoiceUser]
	, PendCl.ClusterWorkPending  		            AS WkPending
	, ClusterWorkTotal  			                AS ClusterTotal
	, PendCl.CLUSTERPROFILEID                       AS ClusterProfile
FROM 
    PendCl
ORDER BY 
	--SLA_Date, 
    ClusterId;    
