
-- Troubleshoot cluster import

USE VoiceLink

SELECT [importID]
      ,[assignmentNumber]
      ,[deliveryDate]
      ,[deliveryLocation]
      ,[route]
      ,[customerNumber]
      ,[customerName]
      ,[customerAddress]
      ,[goalTime]
      ,[pickSequenceNumber]
      ,[hostPickID]
      ,[quantityToPick]
      ,[itemNumber]
      ,[locationIdentifier]
      ,[region]
      ,[UOM]
      ,[workID]
      ,[pickMessage]
      ,[batchPosition]
      ,[orderType]
      ,[siteName]
      ,[shortDeliveryLocation]
      ,[cartonID]
      ,[importStatus]
      ,[importMessage]
      ,[notifiedFlag]
      ,[notifiedOn]
      ,[dateTimeInserted]
      ,[dateTimeProcessingStart]
      ,[dateTimeProcessingEnd]
  FROM [TKPRODVOICE01].[VoiceLink].[dbo].[import_sel_assignments]
  WHERE
	[workid] = 'CL001352337' -- cluster to check
ORDER BY [batchposition], [locationidentifier], [datetimeinserted]

GO

/*
  Keep the following line as a comment most of the time 
  It will delete the specified cluster on the WHERE clause
*/
--sp_columns import_sel_assignments
--DELETE FROM [TKPRODVOICE01].[VoiceLink].[dbo].[import_sel_assignments] WHERE [workid] = 'CL001352337'

/*
SELECT TOP 20 *
FROM [TKPRODVOICE01].[VoiceLink].[dbo].sel_assignments
WHERE [workIdentifierValue] = 'CL001299685'

*/