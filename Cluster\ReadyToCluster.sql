
-- Counting units
/*
SELECT	WVI.WAVEID, WVI.PendWkCount, WVI.Ready, WVI.Blocked, UNC.Units AS PendingUnits, UNC.ReleasedDate
FROM
(
SELECT WT.WAVEID, COUNT( WT.WORKID ) AS PendWkCount, SUM( CASE WHEN WT.FROZEN = 0 THEN 1 ELSE 0 END) AS Ready, SUM( CASE WHEN WT.FROZEN = 1 THEN 1 ELSE 0 END ) AS Blocked
FROM WHSWORKTABLE WT
LEFT JOIN WHSWORKCLUSTERLINE CL ON CL.WORKID = WT.WORKID AND CL.DATAAREAID = WT.DATAAREAID AND CL.PARTITION = WT.PARTITION
WHERE WT.WORKSTATUS < 2 AND WT.WORKTEMPLATECODE = '4010 Direct' AND WT.CREATEDDATETIME > ( GETDATE() - 15 ) AND CL.CLUSTERID IS NULL
GROUP BY WAVEID
) AS WVI
LEFT JOIN
(
SELECT wt.WAVEID, CAST( ll.CREATEDDATETIME AS Date ) AS ReleasedDate, SUM( CONVERT( DECIMAL, ll.WORKCREATEDQTY, 10 ) ) AS Units
	FROM WHSWORKTABLE wt 
	LEFT JOIN WHSLOADLINE ll ON wt.LOADID = ll.LOADID AND ll.DATAAREAID = wt.DATAAREAID AND ll.PARTITION = wt.PARTITION
	WHERE WT.WORKSTATUS < 2 AND WT.WORKTEMPLATECODE = '4010 Direct' AND WT.CREATEDDATETIME > ( GETDATE() - 15 )
	GROUP BY wt.WAVEID, CAST( ll.CREATEDDATETIME AS Date )
) AS UNC
ON WVI.WAVEID = UNC.WAVEID
ORDER BY WAVEID
*/

--Without units, way faster
/*
SELECT WT.WAVEID, WVT.WAVETEMPLATENAME AS Template, COUNT( WT.WORKID ) AS PendWkCount, SUM( CASE WHEN WT.FROZEN = 0 THEN 1 ELSE 0 END) AS Ready, SUM( CASE WHEN WT.FROZEN = 1 THEN 1 ELSE 0 END ) AS Blocked, CAST( WT.CREATEDDATETIME AS DATE ) AS CreatedDate
FROM WHSWORKTABLE WT
LEFT JOIN WHSWORKCLUSTERLINE CL ON CL.WORKID = WT.WORKID AND CL.DATAAREAID = WT.DATAAREAID AND CL.PARTITION = WT.PARTITION
INNER JOIN WHSWAVETABLE WVT ON WVT.WAVEID = WT.WAVEID AND WVT.DATAAREAID = WT.DATAAREAID AND WVT.PARTITION = WT.PARTITION
WHERE	WT.WORKSTATUS		< 2									AND -- Open, In progresss
		WT.WORKTRANSTYPE	= 2									AND -- Sales Order
		WT.WORKTEMPLATECODE NOT LIKE 'W%'						AND -- Excluding Wholesale
		WT.WORKTEMPLATECODE NOT IN ('4010 Wholesale', 'Saks' )	AND -- More exclusions
		WT.CREATEDDATETIME > ( GETDATE() - 15 )					AND -- Last two weeks
		CL.CLUSTERID IS NULL										-- Not clustered
GROUP BY WT.WAVEID, WVT.WAVETEMPLATENAME, CAST( WT.CREATEDDATETIME AS DATE )
ORDER BY WT.WAVEID
*/

-- Segregating by Wave template
SELECT	
  CLI.CreatedDate
  --, CLI.WAVEID
  , CLI.Template,
		COUNT( CLI.WORKID ) AS PendingWork, -- Segregating Work's ready by work template
    SUM( CASE WHEN FROZEN = 0 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 1 ELSE 0 END 
              END  
            ) AS AB_Ready, 
        SUM( CASE WHEN FROZEN = 0 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 0 ELSE 1 END 
              END  
            ) AS Direct_Ready,     
		SUM( CASE WHEN FROZEN = 1 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 1 ELSE 0 END 
              END  
            ) AS AB_Blocked, 
        SUM( CASE WHEN FROZEN = 1 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 0 ELSE 1 END 
              END  
            ) AS Direct_Blocked
FROM (    
SELECT 
CAST( 
  CASE  WHEN DATEPART( mm, wt.CreatedDateTime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wt.CreatedDateTime ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wt.CreatedDateTime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wt.CreatedDateTime ) -- No DST
        WHEN DATEPART( mm, wt.CreatedDateTime ) = 3 
        THEN CASE   WHEN DATEPART( dd, wt.CreatedDateTime ) < 8 OR DATEPART( dd, wt.CreatedDateTime ) > 14 THEN  DATEADD( hh, - 5, wt.CreatedDateTime ) -- No DST
                    WHEN DATEPART( dd, wt.CreatedDateTime ) - DATEPART( w, wt.CreatedDateTime ) + 1  >= 8 THEN  DATEADD(hh, - 4, wt.CreatedDateTime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wt.CreatedDateTime )
             END
        WHEN DATEPART( dd, wt.CreatedDateTime ) - DATEPART( w, wt.CreatedDateTime ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wt.CreatedDateTime )
        ELSE DATEADD( hh, - 4, wt.CreatedDateTime )
END   AS DATE )  AS CreatedDate,
WT.WAVEID, WVT.WAVETEMPLATENAME AS Template, 
WT.WORKID, WT.FROZEN,
CASE WHEN WT.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' ) THEN 'AB' ELSE 'Not_AB' END AS CNT_Type
FROM WHSWORKTABLE WT
LEFT JOIN WHSWORKCLUSTERLINE CL ON CL.WORKID = WT.WORKID AND CL.DATAAREAID = WT.DATAAREAID AND CL.PARTITION = WT.PARTITION
INNER JOIN WHSWAVETABLE WVT ON WVT.WAVEID = WT.WAVEID AND WVT.DATAAREAID = WT.DATAAREAID AND WVT.PARTITION = WT.PARTITION
WHERE WT.WORKSTATUS < 2																			AND -- Open, In progresss
                                WT.WORKTRANSTYPE    = 2											AND -- Sales Order
                                WT.WORKTEMPLATECODE NOT LIKE 'W%'								AND -- Excluding Wholesale
                                WT.WORKTEMPLATECODE NOT IN ('4010 Wholesale', 'Saks' )       	AND -- More exclusions
                                WT.CREATEDDATETIME > ( GETDATE() - 15 )                         AND -- Last two weeks
                                CL.CLUSTERID IS NULL                                                -- Not clustered
GROUP BY WT.WAVEID, WT.WORKTEMPLATECODE, WVT.WAVETEMPLATENAME, WT.CREATEDDATETIME, WT.WORKID, FROZEN
) AS CLI
GROUP BY /*WAVEID, */TEMPLATE, CAST( CreatedDate AS DATE ) 
