-- Trying to break, parend and child
-- Container breakdown, AB vs NonAB, completed
--10/14/2021

USE DAX_PROD

SELECT wt.ORDERNUM AS OrderNum, wt.CONTAINERID AS Container, wt.WORK<PERSON>, 
CAST( 
CASE    WHEN DATEPART( mm, wt.WOR<PERSON>CLOSEDUTCDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wt.WORKCLOSEDUTCDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wt.WORKCLOSEDUTCDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wt.WOR<PERSON>CLOSEDUTCDATETIME ) -- No DST
        WHEN DATEPART( mm, wt.WOR<PERSON><PERSON><PERSON>EDUTCDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, wt.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wt.WOR<PERSON>CLOSEDUTCDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wt.WORKCLOSEDUTCDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wt.W<PERSON><PERSON><PERSON><PERSON>EDUTCDATETIME )
             END
        WHEN DATEPART( dd, wt.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wt.WORKCLOSEDUTCDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wt.WORKCLOSEDUTCDATETIME )
        ELSE DATEADD( hh, - 4, wt.WORKCLOSEDUTCDATETIME )
END   AS DATETIME )  AS CompletedOn,
CASE	WHEN DATEPART( hh, wt.CREATEDDATETIME ) < 9 OR DATEPART( hh, wt.CREATEDDATETIME ) > 21 THEN '2nd' ELSE '1st' END AS CreatedBy,
CASE	WHEN DATEPART( hh, wt.WORKINPROCESSUTCDATETIME ) < 9 OR DATEPART( hh, wt.WORKINPROCESSUTCDATETIME ) > 21 THEN '2nd'	ELSE '1st' END AS PickedBy,
wt.WAVEID, wavt.WAVETEMPLATENAME,
CASE WHEN wt.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' )	THEN 'AB_CNT' ELSE 'No_AB_CNT' END AS CNT_Type,
CASE WHEN wt.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' )	THEN 1 ELSE 0 END AS AB_CNT,
CASE WHEN wt.WORKTEMPLATECODE IN ( '4010 Direct', '4010 CartonShip Sing', '4010 CartonShip Mult' )	THEN 1 ELSE 0 END AS No_AB_CNT,
CASE WHEN ct.HAWHSSHIPPERID = 'MHX' THEN 'AutoBagger' ELSE 'Logistyx' END AS ShippedBy
FROM WHSWORKTABLE wt
--INNER JOIN WHSSHIPMENTTABLE st ON wt.SHIPMENTID = st.SHIPMENTID
INNER JOIN WHSCONTAINERTABLE ct ON wt.CONTAINERID = ct.CONTAINERID
INNER JOIN WHSWAVETABLE wavt ON wavt.WAVEID = wt.WAVEID
WHERE	wt.WORKTRANSTYPE = 2 AND wt.WORKSTATUS = 4 AND 
		wt.WORKTEMPLATECODE IN ('4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult', '4010 Direct', '4010 CartonShip Sing', '4010 CartonShip Mult'  ) AND  
		wt.CREATEDDATETIME BETWEEN CAST( ( CONVERT(nvarchar, GETUTCDATE() - 6, 1 ) +' 04:00:00' ) AS DateTime ) AND GETUTCDATE() 
ORDER BY wt.WORKID

--Pending containers breakdown
SELECT wt.ORDERNUM AS OrderNum, wt.CONTAINERID AS Container, wt.WORKID, 
CASE	WHEN wt.WORKSTATUS = 0 THEN 'Open' ELSE 'In process' END AS Status, 
CAST( 
CASE    WHEN DATEPART( mm, wt.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wt.CREATEDDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wt.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wt.CREATEDDATETIME ) -- No DST
        WHEN DATEPART( mm, wt.CREATEDDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, wt.CREATEDDATETIME ) < 8 OR DATEPART( dd, wt.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, wt.CREATEDDATETIME ) -- No DST
                    WHEN DATEPART( dd, wt.CREATEDDATETIME ) - DATEPART( w, wt.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wt.CREATEDDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wt.CREATEDDATETIME )
             END
        WHEN DATEPART( dd, wt.CREATEDDATETIME ) - DATEPART( w, wt.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wt.CREATEDDATETIME )
        ELSE DATEADD( hh, - 4, wt.CREATEDDATETIME )
END   AS DATETIME )  AS CreatedOn,
wt.WAVEID, wavt.WAVETEMPLATENAME,
	CASE WHEN wt.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' )	THEN 'AB_CNT' ELSE 'No_AB_CNT' END AS CNT_Type
FROM WHSWORKTABLE wt
INNER JOIN WHSWAVETABLE wavt ON wavt.WAVEID = wt.WAVEID
WHERE	wt.WORKTRANSTYPE = 2 AND wt.WORKSTATUS < 3 AND 
		wt.WORKTEMPLATECODE IN ('4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult', '4010 Direct', '4010 CartonShip Sing', '4010 CartonShip Mult'  ) AND  
		wt.CREATEDDATETIME BETWEEN CAST( ( CONVERT(nvarchar, GETUTCDATE() - 6, 1 ) +' 04:00:00' ) AS DateTime ) AND GETUTCDATE() 
ORDER BY wt.WORKID

--Pending containers breakdown
SELECT 
   wt.ORDERNUM AS OrderNum
   , wt.CONTAINERID AS Container
   , wt.WORKID, 
        CASE	WHEN wt.WORKSTATUS = 0 THEN 'Open' ELSE 'In process' END AS Status, 
        CAST( 
        CASE    WHEN DATEPART( mm, wt.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wt.CREATEDDATETIME ) -- Daylight Savings Months 
                WHEN DATEPART( mm, wt.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wt.CREATEDDATETIME ) -- No DST
                WHEN DATEPART( mm, wt.CREATEDDATETIME ) = 3 
                THEN 
                  CASE   
                     WHEN DATEPART( dd, wt.CREATEDDATETIME ) < 8 OR DATEPART( dd, wt.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, wt.CREATEDDATETIME ) -- No DST
                     WHEN DATEPART( dd, wt.CREATEDDATETIME ) - DATEPART( w, wt.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wt.CREATEDDATETIME ) -- Last Sunday after March 8
                     ELSE DATEADD(hh, - 5, wt.CREATEDDATETIME )
                  END
                WHEN DATEPART( dd, wt.CREATEDDATETIME ) - DATEPART( w, wt.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
                THEN DATEADD( hh, - 5, wt.CREATEDDATETIME )
                ELSE DATEADD( hh, - 4, wt.CREATEDDATETIME )
        END   AS DATETIME )  AS CreatedOn
        , wt.WAVEID
        , wavt.WAVETEMPLATENAME
        , CASE WHEN wt.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' )	THEN 'AB_CNT' ELSE 'No_AB_CNT' END AS CNT_Type
FROM WHSWORKTABLE wt
INNER JOIN WHSWAVETABLE wavt ON wavt.WAVEID = wt.WAVEID
WHERE	wt.WORKTRANSTYPE = 2 AND wt.WORKSTATUS < 3 AND 
		wt.WORKTEMPLATECODE IN ('4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult', '4010 Direct', '4010 CartonShip Sing', '4010 CartonShip Mult'  ) AND  
		wt.CREATEDDATETIME BETWEEN CAST( ( CONVERT(nvarchar, GETUTCDATE() - 6, 1 ) +' 04:00:00' ) AS DateTime ) AND GETUTCDATE() 
ORDER BY wt.WORKID
