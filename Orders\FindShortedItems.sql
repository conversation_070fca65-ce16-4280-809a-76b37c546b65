
-- Trying to find shorted items in voice
-- Not working properly(12/20/2024)
SELECT
    TOP 20*
FROM 
    WHS<PERSON>OADLINE ll
    JOIN WHSWORKLINE wkln ON wkln.SHIPMENTID = ll.SHIPMENTID AND wkln.INVENTTRANSID = ll.INVENTTRANSID AND ll.DATAAREAID = 'ha' AND ll.[PARTITION] = wkln.[PARTITION]
        AND wkln.ITEMID = ll.ITEMID AND wkln.INVENTDIMID = ll.INVENTDIMID AND wkln.WORKTYPE = 1
WHERE
    1 = 1
    AND ll.WORKCREATEDQTY   <> ll.PICKEDQTY
    AND ll.WORKCREATEDQTY   > 0
    AND ll.LOADDIRECTION    = 2 -- Outbound
    AND ll.INVENTTRANSTYPE  = 0 -- Sales Order
    AND wkln.WORKSTATUS     = 4 -- Line picked
    AND ll.MODIFIEDDATETIME > GETUTCDATE() - 10

--sp_columns whsworkline
--sp_columns whsloadline