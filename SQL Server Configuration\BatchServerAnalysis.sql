-- <PERSON>

USE DAX_PROD

-- Taslk Detail

select    bjh.CAPTION						AS 'BatchName'
		, bjh.RECID							AS 'BatchID'
		, bh.CAPTION						AS 'TaskName'
		, bjh.BATCHCREATEDBY						AS 'BatchOwner'
		, ISNULL(b.<PERSON><PERSON><PERSON><PERSON>,'')				AS 'Group'
		, bh.SERVERID						AS 'Server'

		, CAST(bjh.STARTDATETIME as datetime)	AS 'Batch Start'
		, CAST(bjh.ENDDATETIME as datetime)		AS 'Batch End'
		, datediff(s,bjh.STARTDATETIME, bjh.ENDDATETIME) AS 'TotalBatchSec'
		, CONVERT(varchar, datediff(s,bjh.STARTDATETIME, bjh.ENDDATETIME) / 86400 ) + ':' + CONVERT(varchar, DATEADD(ms, ( datediff(s,bjh.STARTDATETIME, bjh.ENDDATETIME) % 86400 ) * 1000, 0), 108)	as "TotalBatch D:HH:MM:SS"

		, CAST(bh.STARTDATETIME as datetime)	AS 'Task Start'
		, CAST(bh.<PERSON>NDDATETIME as datetime)		AS 'Task End'
		, datediff(s,bh.STARTDATETIME, bh.ENDDATETIME) AS 'TotalTaskSec'
		, CONVERT(varchar, datediff(s,bh.STARTDATETIME, bh.ENDDATETIME) / 86400 ) + ':' + CONVERT(varchar, DATEADD(ms, ( datediff(s,bh.STARTDATETIME, bh.ENDDATETIME) % 86400 ) * 1000, 0), 108)	as "TotalTask D:HH:MM:SS"

from BATCHJOBHISTORY bjh
left join BATCHJOB bj on bjh.BATCHJOBID = bj.RECID
left join BATCH b on bj.recid = b.BATCHJOBID
right join BATCHHISTORY bh on bjh.recid = bh.BATCHJOBHISTORYID AND CAST(bh.STARTDATETIME as datetime) <> CAST(bh.ENDDATETIME as datetime)

where bjh.STARTDATETIME <= bjh.ENDDATETIME AND bh.STARTDATETIME <= bh.ENDDATETIME  

--where bjh.RECID = '5643035796'

ORDER BY bjh.CAPTION, bjh.RECID


-- Batch Summary

select    bjh.CAPTION						AS 'BatchName'
		, bjh.RECID							AS 'BatchID'
--		, bh.CAPTION						AS 'TaskName'
		, bjh.BATCHCREATEDBY						AS 'BatchOwner'
		, ISNULL(b.GROUPID,'')				AS 'Group'
		, bh.SERVERID						AS 'Server'

		, CAST(bjh.STARTDATETIME as datetime)	AS 'Batch Start'
		, CAST(bjh.ENDDATETIME as datetime)		AS 'Batch End'
		, AVG(datediff(s,bjh.STARTDATETIME, bjh.ENDDATETIME)) AS 'TotalBatchSec'
		, CONVERT(varchar, AVG(datediff(s,bjh.STARTDATETIME, bjh.ENDDATETIME)) / 86400 ) + ':' + CONVERT(varchar, DATEADD(ms, ( AVG(datediff(s,bjh.STARTDATETIME, bjh.ENDDATETIME)) % 86400 ) * 1000, 0), 108)	as "TotalBatch D:HH:MM:SS"

--		, CAST(bh.STARTDATETIME as datetime)	AS 'Task Start'
--		, CAST(bh.ENDDATETIME as datetime)		AS 'Task End'
		, sum(datediff(s,bh.STARTDATETIME, bh.ENDDATETIME)) AS 'TotalTaskSec'
		, CONVERT(varchar, sum(datediff(s,bh.STARTDATETIME, bh.ENDDATETIME)) / 86400 ) + ':' + CONVERT(varchar, DATEADD(ms, ( sum(datediff(s,bh.STARTDATETIME, bh.ENDDATETIME)) % 86400 ) * 1000, 0), 108)	as "TotalTask D:HH:MM:SS"

from BATCHJOBHISTORY bjh
left join BATCHJOB bj on bjh.BATCHJOBID = bj.RECID
left join BATCH b on bj.recid = b.BATCHJOBID
left join BATCHHISTORY bh on bjh.recid = bh.BATCHJOBHISTORYID AND CAST(bh.STARTDATETIME as datetime) <> CAST(bh.ENDDATETIME as datetime)

where bjh.STARTDATETIME <= bjh.ENDDATETIME AND bh.STARTDATETIME <= bh.ENDDATETIME

group by  bjh.CAPTION
		, bjh.RECID	
--		, bh.CAPTION
		, bjh.BATCHCREATEDBY
		, ISNULL(b.GROUPID,'')	
		, bh.SERVERID	

		, CAST(bjh.STARTDATETIME as datetime)
		, CAST(bjh.ENDDATETIME as datetime)


ORDER BY bjh.CAPTION, bjh.RECID
