
USE DAX_PROD

SELECT 
	CASE WHEN OQ.CREATEDDATETIME < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, OQ.CREATEDDATETIME ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
		DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, OQ.CREATEDDATETIME ) AS nvarchar(4)) AS datetime) ) ) 
		THEN    DATEADD(hh, - 5, OQ.CREATEDDATETIME ) 
		ELSE 
		CASE    WHEN OQ.CREATEDDATETIME < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, OQ.CREATEDDATETIME ) AS nvarchar(4)) AS datetime ) + 1 -
		DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, OQ.CREATEDDATETIME ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, OQ.CREATEDDATETIME ) 
				ELSE dateadd(hh, - 5, OQ.CREATEDDATETIME ) 
		END 
	END AS CreatedDate
	, OQ.MHXDATA5 AS WaveID
	, wvt.WAVETEMPLATENAME AS WaveTemplate
	, CASE WHEN ISNULL( wkcl.CLUSTERID, '' ) = '' THEN 'Not Clustered' ELSE wkcl.CLUSTERID END AS CLusterID
	, CASE WHEN ISNULL( wkct.CLUSTERPROFILEID, '' ) = '' THEN 'Not Clustered' ELSE wkct.CLUSTERPROFILEID END AS CLusterProfile
	, OQ.MHXDATA1 AS ContainerID
	, OQ.MHXOUTBOUNDSTATUS
	, OQ.MHXDATA4 AS SalesID
	, OQ.MHXDATA7 AS Weight
FROM MHXOutboundQueue OQ
	INNER JOIN WHSWORKTABLE wkt ON wkt.CONTAINERID = OQ.MHXDATA1 AND wkt.[PARTITION] = OQ.[PARTITION] AND wkt.DATAAREAID = OQ.DATAAREAID
	INNER JOIN WHSWAVETABLE wvt ON wvt.WAVEID = OQ.MHXDATA5 AND wvt.[PARTITION] = OQ.[PARTITION] AND wvt.DATAAREAID = OQ.DATAAREAID
	LEFT JOIN WHSWORKCLUSTERLINE wkcl ON wkcl.WORKID = wkt.WORKID AND wkt.[PARTITION] = wkcl.[PARTITION] AND wkt.DATAAREAID = wkcl.DATAAREAID
	LEFT JOIN WHSWORKCLUSTERTABLE wkct ON wkct.CLUSTERID = wkcl.CLUSTERID AND wkct.[PARTITION] = wkcl.[PARTITION] AND wkct.DATAAREAID = wkcl.DATAAREAID
WHERE 
	OQ.CREATEDDATETIME > ( GETUTCDATE() - 5 ) -- Last five days
	AND OQ.MHXOUTBOUNDTRANSTYPE = 10  -- Work Creation
	AND ISNULL( OQ.MHXDATA5, '' ) <> '' -- Avoiding duplicate containers and getting the wave number
	AND OQ.DATAAREAID = 'ha'


