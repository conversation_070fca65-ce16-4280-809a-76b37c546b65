
-- <PERSON> ?

SELECT wt.worktranstype, 
       wt.<PERSON><PERSON><PERSON><PERSON>, 
       wt.<PERSON><PERSON><PERSON><PERSON>TU<PERSON>, 
       wt.CREATEDDATETIME, 
       wt.WORKCREATEDBY, 
       wt.WHSZONEID, 
       wt.TARGETLICENSEPLATEID, 
       wl.<PERSON><PERSON><PERSON><PERSON>ASSI<PERSON>, 
       wl.WORKSTATUS,
	   wl.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
       wl.USERID, 
       wu.USERNAME, 
       wl.itemid, 
       id.INVENTCOLORID, 
       id.INVENTSIZEID, 
       wl.WMSLOCATIONID, 
       wl.QTYWORK, 
       wl.linen<PERSON>, 
       wl.MODIFIEDBY, 
       wl.<PERSON><PERSON><PERSON>IEDDATETIME,
       wt_wu.USERNAME as WorkCreatedBy_Name
FROM WHSWORKTABLE wt
     JOIN WHSWORKLINE wl ON wt.workid = wl.WORKID
                            AND wt.DATAAREAId = wl.DATAAREAID
                            AND wt.PARTITION = wl.PARTITION
     JOIN INVENTDIM id ON wl.INVENTDIMID = id.INVENTDIMID
                          AND wl.DATAAREAID = id.DATAAREAID
                          AND wl.PARTITION = id.PARTITION
     LEFT JOIN WHSWORKUSER wu ON wl.USERID = wu.USERID
                                 AND wl.DATAAREAID = wu.DATAAREAID
                                 AND wl.PARTITION = wu.PARTITION
	 LEFT JOIN WHSWORKUSER wt_wu on wt.WORKCREATEDBY = wt_wu.USERID
                                 AND wt.DATAAREAID = wt_wu.DATAAREAID
                                 AND wt.PARTITION = wt_wu.PARTITION
where 
	--purchase work
		(wt.worktranstype = 1 and wt.WORKSTATUS < 4 and wl.WORKSTATUS < 4 and  wl.WORKTYPE = 2)
		OR
	-- replen work
		(wt.worktranstype = 11 and wt.WORKSTATUS < 4 and wl.workstatus < 4 and wl.WORKTYPE = 2)

order by wt.worktranstype asc, wt.WORKSTATUS asc, wt.workid, wl.LINENUM