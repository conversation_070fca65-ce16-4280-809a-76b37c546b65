--1st step is grab all the shipment ids from containertable where the tracking number is missing
select 
* 
from 
<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>TABLE a left join HASHIPPEDCARTONSTAGING b
on 
a.CONTAINERID = b.CARTONID
WHERE 
a.CLOSECONTAINERUTCDATETIME between '2018-12-31 00:00:00.000' and '2019-03-04 00:00:00.000' and
a.SHIPCARRIERTRACKINGNUM = '' 
order by a.CLOSECONTAINERUTCDATETIME desc

--2nd step is grab all the orderids from shipment table; exclude the Transfer orders
select * from WHSSHIPMENTTABLE where shipmentid in ('SH03552918',
'SH03569982',
'SH03569432',
'SH03569402',
'SH03554272',
'SH03555032',
'SH03553569',
'SH03553519',
'SH03550497',
'SH03553334',
'SH03551963',
'SH03549206',
'SH03549069',
'SH03546998',
'SH03544813',
'SH03535021',
'SH03520389',
'SH03532835',
'SH03522822',
'SH03522822',
'SH03520389',
'SH03520389',
'SH03520389',
'SH03520389',
'SH03520389',
'SH03520389',
'SH03520389',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03522298',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03518438',
'SH03506858',
'SH03493897',
'SH03502739',
'SH03499941',
'SH03495041',
'SH03495041',
'SH03495041',
'SH03495041',
'SH03495041',
'SH03495041',
'SH03495041',
'SH03495041',
'SH03495041',
'SH03495580',
'SH03494708',
'SH03494002',
'SH03481159',
'SH03492497',
'SH03475910',
'SH03475957',
'SH03476233',
'SH03473619',
'SH03471299',
'SH03450857',
'SH03460647',
'SH03444888',
'SH03445007',
'SH03436671',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03428434',
'SH03337146',
'SH03403663',
'SH03389493') and
ordernum not like '%TO%'


--last step is use the order ids and look in the logistyx_shipping database for the tracking numbers
SELECT Tracking_Number, * FROM Shipments s INNER JOIN Packages p 
ON s.ShipmentGUID = p.ShipmentGUID where s.Consignee_Reference in 
('34398569',
'34490538',
'34498186',
'34498328',
'34497477',
'34290397',
'34416616',
'34425799',
'34416053',
'34383032',
'34386063',
'34398159',
'34442724',
'34444628',
'34448742',
'34451395',
'34472445',
'34481184',
'34495969',
'34501395',
'34502152',
'34503024',
'34503805',
'34504645',
'34519444',
'34510047',
'34519931')

