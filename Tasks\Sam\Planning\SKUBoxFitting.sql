
-- For planning team

DECLARE @AB_Length  AS DECIMAL(5,2) = 17.90
DECLARE @AB_Width   AS DECIMAL(5,2) = 12
DECLARE @AB_Heigth  AS DECIMAL(5,2) = 6

DECLARE @CSM_Length  AS DECIMAL(5,2) = 18
DECLARE @CSM_Width   AS DECIMAL(5,2) = 13

DECLARE @CG_Heigth  AS DECIMAL(5,2) = 6.25
DECLARE @SM_Heigth  AS DECIMAL(5,2) = 8.88
DECLARE @MD_Heigth  AS DECIMAL(5,2) = 11.38

DECLARE @LG_Length  AS DECIMAL(5,2) = 25
DECLARE @LG_Width   AS DECIMAL(5,2) = 18
DECLARE @LG_Heigth  AS DECIMAL(5,2) = 16.75

SELECT /*
    uom.ITEMID                  AS [Item]
    , ECORESITEMCOLORNAME   AS [Color]
    , ECORESITEMSIZENAME    AS [Size]
    , CASE 
        WHEN isnull(uom.ECORESITEMCOLORNAME, '') = '' THEN uom.ITEMID
        ELSE uom.ITEMID + '-' + uom.ECORESITEMCOLORNAME
    END                                                 AS [Offer]
    ,*/ CASE 
        WHEN isnull(uom.ECORESITEMCOLORNAME, '') = '' THEN uom.ITEMID
        WHEN isnull(uom.ECORESITEMSIZENAME, '') = '' THEN uom.ITEMID + '-' + uom.ECORESITEMCOLORNAME
        ELSE uom.ITEMID + '-' + uom.ECORESITEMCOLORNAME + '-' + uom.ECORESITEMSIZENAME
    END                                                 AS [SKU]
    , prodtrans.DESCRIPTION AS 'Description'
    , CAST(uom.DEPTH AS DECIMAL(10, 2))    AS [Length]
    , CAST(uom.WIDTH AS DECIMAL(10, 2))    AS [Width]
    , CAST(uom.HEIGHT AS DECIMAL(10, 2))   AS [Height]
    /*
    , CASE WHEN ((uom.DEPTH <= @AB_Length AND uom.WIDTH <= @AB_Width) OR (uom.WIDTH <= @AB_Length AND uom.DEPTH <= @AB_Width)) AND uom.HEIGHT <= @AB_Heigth THEN 'Yes' ELSE 'No' END AS [AB_Bag]
    , CASE WHEN ((uom.DEPTH <= @CSM_Length AND uom.WIDTH <= @CSM_Width) OR (uom.WIDTH <= @CSM_Length AND uom.DEPTH <= @CSM_Width)) 
        THEN
            CASE 
                WHEN uom.HEIGHT <= @CG_Heigth THEN 'Clog' 
                WHEN uom.HEIGHT <= @SM_Heigth THEN 'Small' 
                WHEN uom.HEIGHT <= @MD_Heigth THEN 'Medium'  
            END
        ELSE
            CASE WHEN ((uom.DEPTH <= @LG_Length AND uom.WIDTH <= @LG_Width) OR (uom.WIDTH <= @LG_Length AND uom.DEPTH <= @LG_Width)) AND uom.HEIGHT <= @LG_Heigth THEN 'Large' ELSE 'NoFit' END
    END AS [Box]*/
FROM 
    WHSPHYSDIMUOM uom
    INNER JOIN INVENTTABLE itbl ON uom.ITEMID = itbl.ITEMID AND uom.DATAAREAID = itbl.DATAAREAID AND uom.[PARTITION] = itbl.[PARTITION]
    INNER JOIN ECORESPRODUCTTRANSLATION prodtrans ON itbl.PRODUCT = prodtrans.PRODUCT AND prodtrans.[PARTITION] = itbl.[PARTITION]
WHERE
    1 = 1 
    AND uom.DEPTH > 0.1
    --AND (WIDTH > 25 OR DEPTH > 25)
ORDER BY
    [SKU] --Item, Color, Size

--sp_columns ECORESPRODUCTTRANSLATION