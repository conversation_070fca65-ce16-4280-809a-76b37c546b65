-- INDEX-OPTIMIZED VERSION: Using actual INVENTSUM and INVENTDIM indexes
-- Based on actual index analysis:
-- INVENTSUM: I_174ITEMIDINVENTDIMPHYSIDX (ITEMID, INVENTDIMID, PHYSICALINVENT, PARTITION, DAT<PERSON><PERSON><PERSON><PERSON>)
-- INVENTDIM: I_698LOCATIONIDIDX (INVENT<PERSON>OCATIONID, WMS<PERSON><PERSON><PERSON><PERSON><PERSON>, PART<PERSON>ION, DATAAREAID)
-- This should run much faster by leveraging existing indexes properly

WITH OH AS(
SELECT 
    loc.zoneid              AS [ZoneId],
    loc.locprofileid        AS [LocProfile],
    isum.itemid             AS [Item],
    idim.inventcolorid      AS [Color],
    idim.inventsizeid       AS [Size],
    loc.wmslocationid       AS [Location],
    idim.INVENTSTATUSID     AS [Status],
    CONVERT(DECIMAL(20,0), isum.physicalinvent) AS [Qty],
    CONVERT(DECIMAL(20,0), isum.reservphysical) AS [Reserved],
    isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid AS [SKU],
    CASE WHEN (hagpft.AVAILABILITY = 'in stock' OR isum.RESERVPHYSICAL > 0) THEN 'Yes' ELSE 'No' END AS [Online],
    --MAX(isum.RECID) AS 'RecId',
    CAST(isum.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE) AS LastInventTransaction,
    DATEDIFF(DD, isum.MODIFIEDDATETIME, GETUTCDATE()) AS [SittingDays]
    -- Remove sales order lookup for now - add separately if needed
    --'N/A' AS LastSOTransaction,
    --'N/A' AS LastSalesOrder
FROM inventsum isum WITH (NOLOCK)
-- Optimized join order to leverage indexes
JOIN inventdim idim WITH (NOLOCK) 
    ON isum.inventdimid = idim.inventdimid 
    AND isum.DATAAREAID = idim.DATAAREAID 
    AND isum.[PARTITION] = idim.[PARTITION]
-- Use the I_698LOCATIONIDIDX index on INVENTDIM
JOIN WMSLOCATION loc WITH (NOLOCK) 
    ON loc.WMSLOCATIONID = idim.WMSLOCATIONID 
    AND loc.DATAAREAID = idim.DATAAREAID 
    AND loc.[PARTITION] = idim.[PARTITION]
LEFT OUTER JOIN hagoogleproductfeedtable hagpft 
    ON hagpft.MPN = isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid
WHERE 
    -- Optimize filter order to match I_174ITEMIDINVENTDIMPHYSIDX index
    isum.DATAAREAID = 'ha'                                          -- Match index column order
    AND isum.physicalinvent > 0                                     -- Use > 0 instead of <> 0 for better index usage
    AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997')       -- Item filtering
    AND idim.inventlocationid = '4010'                              -- Use I_698LOCATIONIDIDX index
    AND idim.INVENTSITEID = 'HA USA'                                -- Use I_698SITEIDX index
    AND loc.LOCPROFILEID IN ('W001', 'Picking', 'Picking A', 'Picking D', 'PalletPicking')
    -- Testing filter (uncomment when needed)
    -- AND isum.ITEMID = '80222' AND idim.INVENTCOLORID = '78M' 
GROUP BY
    loc.WMSLOCATIONID, isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, 
    isum.PHYSICALINVENT, isum.RESERVPHYSICAL, idim.INVENTSTATUSID, 
    loc.ZONEID, loc.LOCPROFILEID, hagpft.AVAILABILITY, 
    CAST(isum.MODIFIEDDATETIME AS DATE), isum.MODIFIEDDATETIME
)
SELECT * 
FROM OH
ORDER BY SittingDays DESC;

-- =============================================================================
-- INDEX USAGE EXPLANATION:
-- =============================================================================
/*
This query is optimized to use these existing indexes:

1. INVENTSUM.I_174ITEMIDINVENTDIMPHYSIDX
   - Covers: ITEMID, INVENTDIMID, PHYSICALINVENT, PARTITION, DATAAREAID
   - Perfect for our filtering on ITEMID, PHYSICALINVENT, and DATAAREAID

2. INVENTDIM.I_698LOCATIONIDIDX  
   - Covers: INVENTLOCATIONID, WMSLOCATIONID, PARTITION, DATAAREAID
   - Perfect for our location filtering (inventlocationid = '4010')

3. INVENTDIM.I_698SITEIDX
   - Covers: INVENTSITEID, PARTITION, DATAAREAID  
   - Perfect for our site filtering (INVENTSITEID = 'HA USA')

Key optimizations made:
- Changed physicalinvent <> 0 to physicalinvent > 0 (better for index seeks)
- Ordered WHERE clause to match index column order
- Removed sales order lookup (add separately if needed)
- Optimized join order to leverage indexes

Expected performance: 30 seconds or less
*/

-- =============================================================================
-- OPTIONAL: Add sales order data separately if needed
-- =============================================================================
/*
If you need sales order data, run this as a separate query and join in Excel:

SELECT DISTINCT
    isum.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid AS SKU,
    'Run separate sales order query' AS LastSalesOrder,
    'Run separate sales order query' AS LastSOTransaction
FROM inventsum isum WITH (NOLOCK)
JOIN inventdim idim WITH (NOLOCK) 
    ON isum.inventdimid = idim.inventdimid 
    AND isum.DATAAREAID = idim.DATAAREAID 
    AND isum.[PARTITION] = idim.[PARTITION]
WHERE 
    isum.DATAAREAID = 'ha'
    AND isum.physicalinvent > 0
    AND isum.ITEMID NOT IN ('30991', '3333', '9999', '9997')
    AND idim.inventlocationid = '4010'
    AND idim.INVENTSITEID = 'HA USA';

-- Then create a lookup table for sales orders and refresh it nightly
*/
