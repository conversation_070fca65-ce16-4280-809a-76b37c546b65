	/*
	Title: AX stocking Productivity
	Author: <PERSON> T
	Description: Productivity Overview
	Modifications: 1/18/2022  JT - Adding the wavetemplate to the productivity to be able to assign region_1 vs region 2
	Modifications: 2/3/2022  JT - disrigarding the rags, quality and wash wmslocationnid for the returns
	Modifications: 2/3/2022  JT - Pickers numbers should count based on their picks not their puts. However there are picks appearing for bannder and autobg. I am exluding those because their are not itemlocationid
	*/
	--DROP TABLE #TEMP
	--DROP TABLE #TEMP2
	SELECT 
	WHSWORKLINE.USERID, WHSWORKUSER.USERNAME, WHSWORKLINE.WORKID, WHSWORKLINE.WORKTYPE, WHSWORKLINE.WMSLOCATIONID, WHSWORKLINE.QTYWORK, WHSWORKLINE.WORKCLASSID, WHSSHIPMENTTABLE.SHIPMENTID, 
	WHSWAVETABLE.WAVETEMPLATENAME, WHSWAVETABLE.WAVEID, --Modifications: 1/18/2022  JT - Adding the wavetemplate to the productivity to be able to assign region_1 vs region 2
	CASE
			/*2027*/
			WHEN DATEPART(YEA<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>LINE.MODIFIEDDATETIME) = 2027 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2027-03-14 02:00:00.000' AND '2027-11-07 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME) 
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2027 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2027-03-14 02:00:00.000' AND '2027-11-07 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2026*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2026 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2026-03-08 02:00:00.000' AND '2026-11-01 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2026 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2026-03-08 02:00:00.000' AND '2026-11-01 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2025*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2025 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2025-03-09 02:00:00.000' AND '2025-11-02 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2025 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2025-03-09 02:00:00.000' AND '2025-11-02 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2024*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2024 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2024-03-10 02:00:00.000' AND '2024-11-03 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2024 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2024-03-10 02:00:00.000' AND '2024-11-03 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2023*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2023 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2023-03-12 02:00:00.000' AND '2023-11-05 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2023 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2023-03-12 02:00:00.000' AND '2023-11-05 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2022*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2022 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2022-03-13 02:00:00.000' AND '2022-11-06 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2022 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2022-03-13 02:00:00.000' AND '2022-11-06 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2021*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2021 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2021-03-14 02:00:00.000' AND '2021-11-07 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2021 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2021-03-14 02:00:00.000' AND '2021-11-07 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2020*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2020 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2020-03-08 02:00:00.000' AND '2020-11-01 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2020 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2020-03-08 02:00:00.000' AND '2020-11-01 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2019*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2019 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2019-03-10 02:00:00.000' AND '2019-11-03 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2019 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2019-03-10 02:00:00.000' AND '2019-11-03 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2018*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2018 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2018-03-11 02:00:00.000' AND '2018-11-04 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2018 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2018-03-11 02:00:00.000' AND '2018-11-04 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2017*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2017 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2017-03-12 02:00:00.000' AND '2017-11-05 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2017 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2017-03-12 02:00:00.000' AND '2017-11-05 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2016*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2016 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2016-03-13 02:00:00.000' AND '2016-11-06 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2016 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2016-03-13 02:00:00.000' AND '2016-11-06 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
			/*2015*/
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2015 AND WHSWORKLINE.MODIFIEDDATETIME BETWEEN '2015-03-08 02:00:00.000' AND '2015-11-01 02:00:00.000' THEN DATEADD(hh, -4, WHSWORKLINE.MODIFIEDDATETIME)
			WHEN DATEPART(YEAR, WHSWORKLINE.MODIFIEDDATETIME) = 2015 AND WHSWORKLINE.MODIFIEDDATETIME NOT BETWEEN '2015-03-08 02:00:00.000' AND '2015-11-01 02:00:00.000' THEN DATEADD(hh, -5, WHSWORKLINE.MODIFIEDDATETIME) 
		END AS EST_MODIFIEDDATETIME,
	CASE
		WHEN WORKTYPE = 1 THEN 'Pick'
		WHEN WORKTYPE = 2 THEN 'Put'
		WHEN WORKTYPE = 3 THEN 'Counting'
		WHEN WORKTYPE = 4 THEN 'Adjustments'
		--WHEN WORKTYPE = 8 THEN 'Print' Not needed
	END AS TYPEWORK
	INTO #TEMP
	FROM  WHSWORKLINE 
	LEFT JOIN WHSWORKUSER ON WHSWORKLINE.USERID = WHSWORKUSER.USERID
	LEFT JOIN WHSSHIPMENTTABLE ON WHSSHIPMENTTABLE.SHIPMENTID = WHSWORKLINE.SHIPMENTID --Modifications: 1/18/2022  JT - Adding the wavetemplate to the productivity to be able to assign region_1 vs region 2
	LEFT JOIN WHSWAVETABLE ON WHSSHIPMENTTABLE.WAVEID = WHSWAVETABLE.WAVEID			   --Modifications: 1/18/2022  JT - Adding the wavetemplate to the productivity to be able to assign region_1 vs region 2
	WHERE WORKTYPE IN ( 1, 2 ) 
		AND WMSLOCATIONID NOT IN ( 'Washed', 'Quality', 'Rags', 'Bander', 'AutoBagger', 'Direct1', 'Inbound1' )--Modifications: 2/3/2022  JT - dropping the rags, quality and wash wmslocationnid for the returns, continue LINE BELLOW
	-- Modifications: 2/3/2022  JT : Pickers numbers should count based on their picks not their puts. However there are picks appearing for bannder and autobg. I am exluding those because IT IS NOT A itemlocation
		AND DATEDIFF( DAY, WHSWORKLINE.MODIFIEDDATETIME, GETUTCDATE()) BETWEEN 0 AND 7
		AND WHSWORKLINE.WorkStatus = 4  -- Closed work - 4/12/2022


	-- SELECT 
	--     *
	--     -- WORKID, 
	--     -- WORKSTATUS,
	--     -- WORKTYPE,
	--     -- WMSLOCATIONID,
	--     FROM #TEMP
	--     WHERE WORKID = 'WK0020400292'
/*
SELECT TOP 20 *
FROM WHSWORKLINE
*/
		SELECT 
		EST_MODIFIEDDATETIME,
		DATEPART(HOUR, EST_MODIFIEDDATETIME) AS HOURofTASK,
		CONVERT(VARCHAR(20), EST_MODIFIEDDATETIME, 100) AS 'AM/PM',
		WORKID,
		WORKTYPE,
		WMSLOCATIONID,
		TYPEWORK,
		QTYWORK,
		WORKCLASSID,
		DATEPART(YEAR, EST_MODIFIEDDATETIME) AS 'YEAR',
		DATEPART(MONTH, EST_MODIFIEDDATETIME) AS 'MONTH',
		DATEPART(DAY, EST_MODIFIEDDATETIME) AS 'DAY',
		#TEMP.USERID,
		#TEMP.USERNAME,
		#TEMP.WAVETEMPLATENAME, #TEMP.WAVEID, WHSWAVETEMPLATETABLE.HAVOICEREGION, --Modifications: 1/18/2022  JT - Adding the wavetemplate to the productivity to be able to assign region_1 vs region 2
		MAX(EST_MODIFIEDDATETIME) AS Max_ModifDT -- ??
		INTO #TEMP2     
		FROM #TEMP 
		LEFT JOIN WHSWAVETEMPLATETABLE ON WHSWAVETEMPLATETABLE.WAVETEMPLATENAME = #TEMP.WAVETEMPLATENAME  	--Modifications: 1/18/2022  JT - Adding the wavetemplate to the productivity to be able to assign region_1 vs region 2
		--LEFT JOIN WHSWORKUSER ON WHSWORKUSER.USERID = #TEMP.USERID ---IF LINKED HERE SOME PEOPLE WILL NOT SHOW UP IF THEY DONT HAVE A USERID IN THAT TABLE
	-- WHERE --WORKTYPE = 2 --------COUNTING FOR THE PULLS
		--#TEMP.USERID = 'ack'
		--AND EST_MODIFIEDDATETIME BETWEEN '2021-09-07 00:00:00.000' AND '2021-09-07 23:59:00.000'
		GROUP BY EST_MODIFIEDDATETIME, WORKID, #TEMP.TYPEWORK, WORKCLASSID, QTYWORK, #TEMP.WORKTYPE, #TEMP.WMSLOCATIONID, #TEMP.USERID, #TEMP.WAVETEMPLATENAME, #TEMP.WAVEID, WHSWAVETEMPLATETABLE.HAVOICEREGION, #TEMP.USERNAME
		ORDER BY WORKID,YEAR, USERID DESC


		SELECT *, 
		CASE 
			WHEN HOURofTASK BETWEEN 6 AND 15 THEN '1ST' 
			ELSE '2ND'
		END AS SHIFT,
		CASE 
			WHEN HOURofTASK between 13 and 23 THEN HOURofTASK-12
			WHEN HOURofTASK between 1 and 12 THEN HOURofTASK
			WHEN HOURofTASK = 0 THEN 12
		END AS TASKHOUR,
		CASE 
			WHEN HOURofTASK between 12 and 23 THEN 'PM'
			WHEN HOURofTASK between 0 and 11 THEN 'AM'
		END AS [AM/PM]
		FROM #TEMP2