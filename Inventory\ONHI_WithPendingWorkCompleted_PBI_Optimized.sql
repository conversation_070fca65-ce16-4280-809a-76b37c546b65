-- OPTIMIZED VERSION: Floor capacity calculation with pending work
-- Author: <PERSON>, optimized on 6/24/2025
-- Major performance improvements:
-- 1. Pre-filter tables before joins to reduce dataset size
-- 2. Move filters into JOIN conditions for better index usage
-- 3. Separate inventory and work queries for better performance
-- 4. Add NOLOCK hints for read consistency
-- 5. Optimize join order based on table sizes

WITH 
-- Pre-filter inventory data to reduce dataset size
FilteredInventory AS (
    SELECT 
        idim.WMSLOCATIONID,
        isum.ITEMID,
        idim.INVENTCOLORID,
        idim.INVENTSIZEID,
        isum.PHYSICALINVENT,
        isum.RESERVPHYSICAL
    FROM INVENTSUM isum WITH (NOLOCK)
    INNER JOIN INVENTDIM idim WITH (NOLOCK) 
        ON isum.INVENTDIMID = idim.INVENTDIMID 
        AND isum.DATAAREAID = 'ha' 
        AND isum.[PARTITION] = 5637144576
        AND idim.DATAAREAID = 'ha'
        AND idim.[PARTITION] = 5637144576
        AND idim.INVENTLOCATIONID = '4010' -- Filter early for better performance
    WHERE isum.PHYSICALINVENT > 0 -- Only locations with inventory
),
-- Pre-filter work lines to reduce dataset size
FilteredWorkLines AS (
    SELECT 
        wkln.WMSLOCATIONID,
        wkln.ITEMID,
        idim.INVENTCOLORID,
        idim.INVENTSIZEID,
        wkln.QTYWORK
    FROM WHSWORKLINE wkln WITH (NOLOCK)
    INNER JOIN INVENTDIM idim WITH (NOLOCK) 
        ON idim.INVENTDIMID = wkln.INVENTDIMID 
        AND wkln.DATAAREAID = 'ha' 
        AND wkln.[PARTITION] = 5637144576
        AND idim.DATAAREAID = 'ha'
        AND idim.[PARTITION] = 5637144576
    WHERE wkln.WORKTYPE = 2 -- Put
        AND wkln.WORKSTATUS < 4 -- Not completed
        AND wkln.QTYWORK >= 1 -- Only meaningful work quantities
),
-- Pre-filter locations to reduce dataset size
FilteredLocations AS (
    SELECT 
        loc.WMSLOCATIONID,
        loc.ZONEID,
        loc.LOCPROFILEID
    FROM WMSLOCATION loc WITH (NOLOCK)
    WHERE loc.INVENTLOCATIONID = '4010' 
        AND loc.LOCPROFILEID IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking')
        AND loc.ZONEID NOT IN ('Current Pick') -- Exclude GCs, 3333s
        AND loc.DATAAREAID = 'ha'
        AND loc.[PARTITION] = 5637144576
),
-- Combine inventory and work data efficiently
LocationData AS (
    SELECT  
        loc.WMSLOCATIONID AS [Location],
        loc.ZONEID AS ZoneId,
        loc.LOCPROFILEID AS LocProfile,
        COALESCE(
            CASE WHEN inv.PHYSICALINVENT > 0 
                THEN inv.ITEMID + '-' + inv.INVENTCOLORID + '-' + inv.INVENTSIZEID
                ELSE NULL END,
            CASE WHEN wk.QTYWORK >= 1 
                THEN wk.ITEMID + '-' + wk.INVENTCOLORID + '-' + wk.INVENTSIZEID
                ELSE NULL END
        ) AS SKU,
        COALESCE(inv.RESERVPHYSICAL, 0) AS Reserved,
        CASE WHEN inv.PHYSICALINVENT >= 1 THEN 1 ELSE 0 END AS Occupied,
        CASE WHEN wk.QTYWORK >= 1 THEN 1 ELSE 0 END AS IncomingWork,
        COALESCE(inv.RESERVPHYSICAL, 0) AS RESERVPHYSICAL
    FROM FilteredLocations loc
    LEFT JOIN FilteredInventory inv 
        ON loc.WMSLOCATIONID = inv.WMSLOCATIONID
    LEFT JOIN FilteredWorkLines wk 
        ON loc.WMSLOCATIONID = wk.WMSLOCATIONID
    WHERE (inv.WMSLOCATIONID IS NOT NULL OR wk.WMSLOCATIONID IS NOT NULL) -- Only locations with data
),
-- Calculate final occupancy using optimized formula
ONH_PWC AS (
    SELECT
        [Location],
        ZoneId,
        SKU,
        -- Optimized MAX calculation: 0.5*(a + b + |a - b|) = MAX(a, b)
        CONVERT(DECIMAL(10, 0), 
            0.5 * (Occupied + IncomingWork + ABS(Occupied - IncomingWork))
        ) AS Occupied
    FROM LocationData
    WHERE SKU IS NOT NULL -- Only include locations with valid SKU data
)
SELECT 
    SUM(Occupied) AS [LocsAfterPendingWorkCompleted],
    COUNT(*) AS [TotalLoc],
    CASE 
        WHEN COUNT(*) > 0 
        THEN CAST(SUM(Occupied) AS DECIMAL(10,4)) / COUNT(*) 
        ELSE 0 
    END AS [PercentFilled]
FROM ONH_PWC;
