SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO


--ALTER PROCEDURE [dbo].[sp_backfillInsufficientPicksLog]

AS
BEGIN

	/*************************************************************************************************
		Name:					sp_backfillInsufficientPicksLog]
		Database:				DAX_PROD
		Author:					Dom Annuzzi
		Date:					2023-11-03
		Purpose:				Stopgap to prevent HACompleteInsufficientPicksService from churning over already completed work
	*************************************************************************************************/

	SET NOCOUNT ON;

	DECLARE @WorkId NVARCHAR(20)
	DECLARE @TableId INT
	DECLARE @NextRecId BIGINT
	DECLARE @UserId NVARCHAR(10)

	SET @UserID = 'dannuzzi'
	SELECT @tableId = TableId FROM SQLDictionary WHERE Name = 'HACompleteInsufficientPicksLog'


	DECLARE WorkIdCursor CURSOR
	FOR

		WITH aifLog 
		AS
		(
			SELECT CAST(SUBSTRING(dl.DocumentXML, PATINDEX('%<_workLineId>%', dl.DocumentXML)+13, 10) AS BIGINT) AS WorkLineRecId
			  FROM AifMessageLog ml WITH (NOLOCK)
			 INNER JOIN AifExceptionsView ae WITH (NOLOCK)
				ON ae.MessageId = ml.MessageId
			 INNER JOIN aifDocumentLog dl WITH (NOLOCK)
				ON ml.messageId = dl.messageId
			 WHERE 1=1 
			   AND ml.actionId LIKE 'HAWarehouseVoiceLinkService.ConfirmPick'
			   AND ae.description = 'Insufficient inventory transactions'
		)

		SELECT DISTINCT wt.workId
		  FROM WhsWorkTable wt WITH (NOLOCK)
		 INNER JOIN WhsWorkLine wl WITH (NOLOCK)
			ON wl.dataAreaId = wt.dataAreaId
		   AND wl.Partition = wt.Partition
		   AND wl.WorkId = wt.WorkId
		 INNER JOIN aifLog al
			ON al.WorkLineRecId = wl.RecId
		 WHERE 1=1
		   AND wt.DataAreaId = 'ha' 
		   AND wt.Partition = 5637144576	
		   AND wt.WorkStatus = 4
		   AND NOT EXISTS (SELECT 'x'
							 FROM HACompleteInsufficientPicksLog ip
							WHERE ip.WorkId = wt.WorkId
							  AND ip.DataAreaID = wt.DataAreaId
							  AND ip.Partition = wt.Partition)   					

 
	OPEN WorkIdCursor
	FETCH NEXT FROM WorkIdCursor INTO @WorkId
	WHILE @@FETCH_STATUS = 0
	BEGIN

		EXEC sp_GetNextRecId @tableId, @nextRecId OUTPUT
	
		INSERT INTO HACompleteInsufficientPicksLog
			(WorkCompleted, WorkId, CreatedDateTime, CreatedBy, DataAreaId, RecVersion, Partition, RecId)
		VALUES
			(1, @WorkId, GETUTCDATE(), @UserId, 'HA', 1, 5637144576, @nextRecId)


		FETCH NEXT FROM WorkIdCursor INTO @WorkId
	END

	CLOSE WorkIdCursor
	DEALLOCATE WorkIdCursor


END


GO
