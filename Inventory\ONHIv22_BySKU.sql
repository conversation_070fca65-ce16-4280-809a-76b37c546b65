

SELECT  
    wmslocation.zoneid			                                            AS [ZoneId]
    , wmslocation.locprofileid	                                            AS [LocProfile]
    , OH.itemid					                                            AS [Item]
    , OH.inventcolorid			                                            AS [Color]
    , OH.inventsizeid			                                            AS [Size]
    , wmslocation.wmslocationid	                                            AS [Location]
    , CONVERT( DECIMAL( 20,0), OH.physicalinvent )			                AS [Qty]
    , CONVERT( DECIMAL( 20,0), OH.reservphysical )			                AS [Reserved]
    , OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid            AS [SKU]
    , CASE WHEN ( hagpft.AVAILABILITY = 'in stock' OR  OH.RESERVPHYSICAL > 0 ) THEN 'Yes' ELSE 'No' END  AS [Online]
    , OH.LastPickWC                                                          AS [LastMovement]
    , DATEDIFF( DD, OH.LastPickWC, GETUTCDATE() )                            AS [SittingDays]
     --CASE WHEN CAST( hagpft.DATETIMESTAMP AS DATE ) = CAST( GETDATE() AS DATE ) THEN 'Yes' ELSE 'No' END AS Online
FROM wmslocation
JOIN 
    ( 
        SELECT 
            isum.itemid
            , idim.inventcolorid
            , idim.inventsizeid
            , isum.physicalinvent
            , isum.reservphysical
            , idim.wmslocationid
            , MAX( isum.RECID)  AS 'RecId' -- Last record
            , CAST(isum.MODIFIEDDATETIME AS DATE) AS LastPickWC
        FROM inventdim idim
            JOIN inventsum isum WITH (NOLOCK) ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION] AND idim.inventlocationid = '4010'
            JOIN WMSLOCATION loc WITH (NOLOCK)  ON loc.WMSLOCATIONID = idim.WMSLOCATIONID AND loc.DATAAREAID = 'ha' AND loc.[PARTITION] = isum.[PARTITION] AND isum.PHYSICALINVENT > 0
        WHERE 
            idim.INVENTSITEID = 'HA USA' 
            
            --AND isum.PHYSICALINVENT < 99999 
            --AND isum.physicalinvent <> 0 
            AND loc.LOCPROFILEID IN (  'W001', 'Picking', 'Picking A','Picking D', 'PalletPicking' ) 
            AND isum.ITEMID NOT IN ( '30991', '3333', '9999', '9997' ) 
        GROUP BY
            idim.WMSLOCATIONID, isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, isum.PHYSICALINVENT, isum.RESERVPHYSICAL, CAST(isum.MODIFIEDDATETIME AS DATE)
    ) AS OH -- Pulls location with inventory. Avoiding duplicate records.
ON wmslocation.wmslocationID = OH.wmslocationid
LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid
WHERE 
    wmslocation.inventlocationid = '4010' 
    AND wmslocation.locprofileid LIKE '%Picking%'
    AND wmslocation.zoneid NOT IN ( 'Current Pick')
    --AND OH.physicalinvent > 0 -- Only locations with inventory to be considered as candidates
ORDER BY 
    SKU ASC, SittingDays ASC
 --Item, Color, Size