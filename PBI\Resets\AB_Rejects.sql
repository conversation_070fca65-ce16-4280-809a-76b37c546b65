/*
Tittle: AB Rejects
Date: 11/18/2021
Description: Pulls the AB reject  singles/Multi
*/

/*
Date: 12/8/2023
Adding some modifications
For 10 days, 658556 rows, 1:38 s

*/

SELECT  
    wkln.ORDERNUM
    , wkln.WORKID
    , WHS<PERSON><PERSON><PERSON>CLUSTERLINE.CLUSTERID
    , WOR<PERSON><PERSON>P<PERSON>
    , W<PERSON>CONTAINERTABLE.CONTAINERID
    , HAINVENTITEMGTIN.GLOBALTRADEITEMNUMBER AS UPC
    , wkln.WORKSTATUS
    , wkln.WMSLOCATIONID
    , wkln.ITEMID
    , QTYREMAIN
    , WORKCLASSID
    , wkln.MODIFIEDDATETIME
    , SALESTABLE.CREATEDDATETIME
    , loc.LOCPROFILEID
    , CASE INVENTDIM.INVENTSIZEID  WHEN '' THEN (wkln.ITEMID + '-' + INVENTDIM.INVENTCOLORID)    ELSE (wkln.ITEMID + '-' + INVENTDIM.INVENTCOLORID + '-' + INVENTDIM.INVENTSIZEID ) 
        END AS 'SKU'
    , wkln.INVENTDIMID
    , CASE WHEN WHSWORKTABLE.WOR<PERSON><PERSON><PERSON>LATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' ) THEN 'AB_CNT' ELSE 'No_AB_CNT' END AS CNT_Type
    , WHSCONTAINERTABLE.SHIPCARRIERTRACKINGNUM

FROM
    WHSWORKLINE wkln

LEFT JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND wkln.DATAAREAID = loc.DATAAREAID AND wkln.[PARTITION] = loc.[PARTITION]
LEFT JOIN INVENTDIM idim ON INVENTDIM.INVENTDIMID = wkln.INVENTDIMID
LEFT JOIN SALESTABLE ON SALESTABLE.SALESID = wkln.ORDERNUM
LEFT JOIN WHSWORKCLUSTERLINE ON WHSWORKCLUSTERLINE.WORKID = wkln.WORKID
LEFT JOIN WHSWORKTABLE ON WHSWORKTABLE.WORKID = wkln.WORKID
LEFT JOIN WHSCONTAINERTABLE ON WHSWORKTABLE.CONTAINERID = WHSCONTAINERTABLE.CONTAINERID
LEFT JOIN HAINVENTITEMGTIN  ON wkln.ITEMID = HAINVENTITEMGTIN.ITEMID AND INVENTDIM.INVENTCOLORID = HAINVENTITEMGTIN.INVENTCOLORID AND INVENTDIM.INVENTSIZEID = HAINVENTITEMGTIN.INVENTSIZEID    ---- tO GET UPC

WHERE 
    SALESTABLE.CREATEDDATETIME > getdate() - 10