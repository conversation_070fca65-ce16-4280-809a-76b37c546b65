
-- No tracking on these orders. DSLs verified some movement using tracking by references
SELECT
    cntbl.CONTAINERID
    , shtbl.ORDERNUM
    , shtbl.ACCOUNTNUM
    , shtbl.DELIVERYNAME
    , shtbl.ADDRESS
    , shtbl.MODECODE
    , shtbl.CUSTOMERREF

FROM
    WHSCONTAINERTABLE cntbl
    JOIN WHSSHIPMENTTABLE shtbl ON shtbl.SHIPMENTID = cntbl.SHIPMENTID
/*
BEGIN TRAN;

UPDATE WHSCONTAINERTABLE 
SET SHIPCARRIERTRACKINGNUM = 'WithMovement', MODIFIEDBY = 'latmpadm', HAWHSAJILLUSWEIGHT = WEIGHT
*/
WHERE
    cntbl.HAWHSAJILLUSWEIGHT = cntbl.WEIGHT
    AND cntbl.MODIFIEDDATETIME BETWEEN '09/19/2024' AND '09/21/2024'
    AND cntbl.CONTAINERSTATUS = 2 -- Closed
    AND cntbl.MODIFIEDBY = 'latmpadm'

--COMMIT;