-- <PERSON><PERSON> check

WITH WkUPCs AS
(
    SELECT
        wkln.WORKID
        , CASE WHEN wktbl.WORKSTATUS = 0 THEN 'Open' ELSE 'InProcess' END AS [WorkStatus]
        --, wktbl.ORDERNUM        AS [Order]   
        --, wkln.WMSLOCATIONID    AS [Location] 
        --, loc.LOCPROFILEID
        --, wkln.ITEMID           AS [Item]
        --, id.INVENTCOLORID      AS [Color]
        --, id.INVENTSIZEID       AS [Size]
        , haig.GLOBALTRADEITEMNUMBER AS [UPC]
        -- wktbl.*
    FROM 
        WHSWORKTABLE wktbl
        INNER JOIN WHSWORKLINE wkln ON wkln.WORKID = wktbl.WORKID AND wktbl.DATAAREAID = 'ha' AND wktbl.[PARTITION] = wkln.[PARTITION]
        INNER JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.DATAAREAID = 'ha'
        INNER JOIN INVENTDIM id ON id.INVENTDIMID = wkln.INVENTDIMID AND wkln.DATAAREAID = 'ha' AND wkln.[PARTITION] = id.[PARTITION]
        INNER JOIN HAINVENTITEMGTIN haig ON wkln.ITEMID = haig.ITEMID AND id.INVENTCOLORID = haig.INVENTCOLORID AND id.INVENTSIZEID = haig.INVENTSIZEID 
            AND haig.[PARTITION] = wkln.[PARTITION] AND haig.DATAAREAID = 'ha'
    WHERE
        1 = 1
        AND wktbl.INVENTLOCATIONID = '4010'
        --AND haig.GLOBALTRADEITEMNUMBER IN ('196705332857', '196705332932')
        AND haig.GLOBALTRADEITEMNUMBER IN ('196705459417', '196705410517')
        AND wktbl.WORKSTATUS IN (0, 1) -- Open, In process
        AND wktbl.WORKTRANSTYPE = 2 -- Sales order
        AND wkln.WORKTYPE = 1 -- Pick
        AND loc.LOCPROFILEID IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking')
)
SELECT 
    WORKID
    , WorkStatus
FROM
    WkUPCs
GROUP BY WORKID, WorkStatus
--HAVING COUNT (* ) > 1
ORDER BY
    WORKID

--sp_columns HAINVENTITEMGTIN



