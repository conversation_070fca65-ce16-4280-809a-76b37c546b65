SELECT  
ct.ContainerID                             AS Container,
CASE WHEN ct.ModifiedDateTime < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, ct.ModifiedDateTime ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
		DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, ct.ModifiedDateTime ) AS nvarchar(4)) AS datetime) ) ) 
		THEN    DATEADD(hh, - 5, ct.ModifiedDateTime ) 
		ELSE 
		CASE    WHEN ct.ModifiedDateTime < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, ct.ModifiedDateTime ) AS nvarchar(4)) AS datetime ) + 1 - DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, ct.ModifiedDateTime ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, ct.ModifiedDateTime ) 
				ELSE dateadd(hh, - 5, ct.ModifiedDateTime ) 
		END 
END AS KYShipDate,
CASE WHEN ct.errorcontainer = 1 OR wt.CONTAINERID = '' THEN 1 ELSE 0 END AS CNTWithError
FROM WHSContainerTable ct     
INNER JOIN WHSWORKTABLE wt ON wt.CONTAINERID = ct.CONTAINERID
WHERE ct.ModifiedDateTime BETWEEN DATEADD( ss, -14401, DATEADD( dd, -7, GETUTCDATE() ) )  AND GETUTCDATE() 
ORDER BY KYShipDate  ASC 