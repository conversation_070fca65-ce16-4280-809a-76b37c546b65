--Clusters( with units )

SELECT CLP.CLUSTERID AS ClusterID, CLP.CREATEDBY AS CreatedBy, CLP.CreatedOn, 
CASE 
	WHEN DATEPART( hh, CLP.CreatedOn ) IN ( 00, 01, 02, 03, 16, 17, 18, 19, 20, 21, 22, 23 ) THEN '2nd'	ELSE '1st' END AS ClusteredOn,
UNC.ClusterTotal, CLP.ClusterProfile, UNC.Units AS TotalUnits, UNC.ReleasedDate
FROM 
(
	SELECT CL.CLUSTERID AS ClusterID, CT.CREATEDBY AS CreatedBy, 
	CASE    WHEN DATEPART( mm, CT.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, CT.CREATEDDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, CT.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, CT.CREATEDDATETIME ) -- No DST
        WHEN DATEPART( mm, CT.CREATEDDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, CT.CREATEDDATETIME ) < 8 OR DATEPART( dd, CT.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, CT.CREATEDDATETIME ) -- No DST
                    WHEN DATEPART( dd, CT.CREATEDDATETIME ) - DATEPART( w, CT.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, CT.CREATEDDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, CT.CREATEDDATETIME )
             END
        WHEN DATEPART( dd, CT.CREATEDDATETIME ) - DATEPART( w, CT.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, CT.CREATEDDATETIME )
        ELSE DATEADD( hh, - 4, CT.CREATEDDATETIME )
END                             AS CreatedOn, CT.CLUSTERPROFILEID AS ClusterProfile, COUNT( CL.WORKID ) AS WkPending
	FROM WHSWORKCLUSTERTABLE CT
	INNER JOIN WHSWORKCLUSTERLINE CL ON CL.CLUSTERID = CT.CLUSTERID
	LEFT JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID
	WHERE	WT.WORKSTATUS < 5 AND -- Not canceled
			WT.WORKTRANSTYPE = 2 AND -- Sales Orders
			WT.CREATEDDATETIME > ( GETUTCDATE() - 2 ) -- Last 15 days
	GROUP BY CL.CLUSTERID, CT.CREATEDBY, CT.CREATEDDATETIME, CT.CLUSTERPROFILEID, CAST( WT.CREATEDDATETIME AS DATE )
) AS CLP
LEFT JOIN 
( 
	SELECT ct.CLUSTERID, CAST( ll.CREATEDDATETIME AS Date ) AS ReleasedDate, COUNT( DISTINCT ll.loadid ) AS ClusterTotal, 
			SUM( CONVERT( DECIMAL, ll.WORKCREATEDQTY, 10 ) ) AS Units
	FROM WHSWORKCLUSTERTABLE ct
	LEFT JOIN WHSWORKCLUSTERLINE cl ON ct.CLUSTERID = cl.CLUSTERID
	LEFT JOIN WHSWORKTABLE wt ON cl.WORKID = wt.WORKID
	LEFT JOIN WHSLOADLINE ll ON wt.LOADID = ll.LOADID
	WHERE WT.WORKSTATUS < 5 AND WT.WORKTRANSTYPE = 2 AND WT.CREATEDDATETIME > ( GETUTCDATE() - 2 )
	GROUP BY ct.CLUSTERID, CAST( ll.CREATEDDATETIME AS Date )
) AS UNC
ON CLP.ClusterID = UNC.CLUSTERID
WHERE ISNULL( ClusterTotal, '' ) <> ''
ORDER BY CLP.CLUSTERID

-- No units
SELECT PendCL.CLUSTERID, PendCL.CreatedBy, PendCL.CreatedOn, PendCL.WkPending, WKC.WkCount AS ClusterTotal, PendCL.CLUSTERPROFILEID AS ClusterProfile, PendCL.ReleasedDate
FROM (
	SELECT CL.CLUSTERID AS ClusterID, CT.CLUSTERPROFILEID, CT.CREATEDBY AS CreatedBy, 
	CASE    WHEN DATEPART( mm, CT.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, CT.CREATEDDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, CT.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, CT.CREATEDDATETIME ) -- No DST
        WHEN DATEPART( mm, CT.CREATEDDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, CT.CREATEDDATETIME ) < 8 OR DATEPART( dd, CT.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, CT.CREATEDDATETIME ) -- No DST
                    WHEN DATEPART( dd, CT.CREATEDDATETIME ) - DATEPART( w, CT.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, CT.CREATEDDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, CT.CREATEDDATETIME )
             END
        WHEN DATEPART( dd, CT.CREATEDDATETIME ) - DATEPART( w, CT.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, CT.CREATEDDATETIME )
        ELSE DATEADD( hh, - 4, CT.CREATEDDATETIME )
END                             AS CreatedOn,
	COUNT( CL.WORKID ) AS WkPending, CAST( WT.CREATEDDATETIME AS DATE ) AS ReleasedDate
	FROM WHSWORKCLUSTERTABLE CT
	INNER JOIN WHSWORKCLUSTERLINE CL ON CL.CLUSTERID = CT.CLUSTERID
	LEFT JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID
	WHERE WT.WORKSTATUS < 2 AND WT.WORKTRANSTYPE = 2 AND WT.CREATEDDATETIME > ( GETDATE() - 2 )
	GROUP BY CL.CLUSTERID, CT.CLUSTERPROFILEID, CT.CREATEDBY, CT.CREATEDDATETIME, CAST( WT.CREATEDDATETIME AS DATE )
) AS PendCL
LEFT JOIN
(
	SELECT DISTINCT CLUSTERID, COUNT( CL.WORKID ) AS WkCount
	FROM WHSWORKCLUSTERLINE CL
	LEFT JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID
	WHERE WT.WORKTRANSTYPE = 2 AND WT.CREATEDDATETIME > ( GETDATE() - 2 )
	GROUP BY CLUSTERID
) AS WKC
ON PendCL.ClusterID = WKC.CLUSTERID
ORDER BY PendCL.CLUSTERID