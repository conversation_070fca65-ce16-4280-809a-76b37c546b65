-- Query to find missing records on WHS<PERSON>ork<PERSON>lusterTable
-- The clusters identified must be deleted from WHSWorkClusterLine

USE DAX_PROD

DECLARE @DaysToLookBack INT = 10
DECLARE @DateToCheck DATETIME = GETUTCDATE() - @DaysToLookBack

SELECT 
    wkcl.CLUSTERID
    , wkcl.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AT TIME ZONE 'UTC' AT TIME ZONE 'EASTERN STANDARD TIME' AS [KYTime]
    , wkcl.MODIFIEDBY
FROM 
    WHSWORKCLUSTERLINE wkcl
WHERE
    wkcl.CLUSTERID NOT IN (SELECT clusterID FROM WHSWOR<PERSON>CLUSTERTABLE WHERE MODIFIEDDATETIME> @DateToCheck)
    AND wkcl.MODIFIEDDATETIME > @DateToCheck
GROUP BY
    wkcl.CLUSTERID, wkcl.MODIFIEDBY, wkcl.MODIFIEDDATETIME
ORDER BY
    wkcl.CLUSTERID;

--sp_columns InventDimCombination

