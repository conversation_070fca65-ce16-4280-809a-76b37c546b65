
-- Find open lines in closed work
-- 11/14/2023
SELECT 
    wktbl.WORKID
    , wktbl.ORDERNUM        AS [SaleSId]
    , wktbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'     AS [CreatedDate] 
    , wkln.WMSLOCATIONID    AS [Location]
    , wkln.ITEMID           AS [Item]
    , idim.INVENTCOLORID    AS [Color]
    , idim.INVENTSIZEID     AS [Size]
FROM 
    WHSWORKLINE wkln
    JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID and wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION]
    JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.DATAAREAID = wkln.DATAAREAID AND loc.PARTITION = wkln.[PARTITION]
    JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
WHERE
    wktbl.WORKSTATUS = 4 -- closed
    AND wkln.WORKSTATUS = 0 -- open line
    AND wkln.WORKTYPE = 1 -- pick
    AND loc.LOCPROFILEID LIKE '%Picking%'
    --AND wktbl.CREATEDDATETIME > GETUTCDATE() - 7
ORDER BY
    wktbl.WORKID