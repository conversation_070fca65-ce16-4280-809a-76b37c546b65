-- Running queries

USE DAX_PROD

SELECT 
    T.[text]
    , <PERSON><PERSON>[query_plan]
    , S<PERSON>[program_name]
    , S.[host_name]
    , S.[client_interface_name]
    , <PERSON>.[login_name]
    , R.*
FROM 
    sys.dm_exec_requests R
INNER JOIN sys.dm_exec_sessions S ON S.session_id = R.session_id
CROSS APPLY sys.dm_exec_sql_text(sql_handle) AS T
CROSS APPLY sys.dm_exec_query_plan(plan_handle) As P
WHERE R.session_Id > 50           -- Consider spids for users only, no system spids. 
AND R.session_Id NOT IN (@@SPID)  -- Don't include request from current spid. 
GO

--SELECT @@version

--SELECT convert(datetime2, '2023-05-29 00:00:00')

--sp_columns WHSWORKTABLE