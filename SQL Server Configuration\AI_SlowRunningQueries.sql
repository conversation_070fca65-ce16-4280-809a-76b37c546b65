
-- Bing AI
-- Top 10 queries to be optimized
/*
This query retrieves the top 10 queries with the highest total logical reads, which often indicates potential areas for optimization. It includes the text of 
the query, execution count, logical reads and writes, worker time, elapsed time, and the last execution time. The query_plan is also retrieved, which can be 
useful for further analysis.
*/
SELECT TOP 10 
    SUBSTRING(qt.text, (qs.statement_start_offset/2) + 1, 
        (CASE 
            WHEN qs.statement_end_offset = -1 THEN LEN(CONVERT(nvarchar(max), qt.text)) * 2 
            ELSE qs.statement_end_offset 
        END - qs.statement_start_offset)/2) AS [Query Text],
    qs.execution_count,
    qs.total_logical_reads,
    qs.total_logical_writes,
    qs.total_worker_time,
    qs.total_elapsed_time/1000000 AS [Total Elapsed Time (s)],
    qs.last_execution_time,
    qp.query_plan
FROM sys.dm_exec_query_stats AS qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) AS qt
CROSS APPLY sys.dm_exec_query_plan(qs.plan_handle) AS qp
ORDER BY qs.total_logical_reads DESC;

