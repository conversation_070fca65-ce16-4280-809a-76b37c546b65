-- Inbound Data
-- <PERSON>'s request 11/3/2023

SELECT
    wktbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'           AS [KY_ReceivedDateTime]
    , wktbl.WORKCLOSEDUTCDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'   AS [KY_PutAwayDateTime]
    --, wktbl.WORKID
    , wktbl.ORDERNUM                                                                        AS [OrderNum]
    , wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID                      AS [SKU]
    , CAST(wkln.QTYWORK AS INT)                                                             AS [Qty]
FROM
    [DAX_Archive].[arc].WHSWORKTABLE wktbl
    JOIN [DAX_Archive].[arc].WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wkln.LINENUM = 1
    JOIN [DAX_Archive].[arc].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
WHERE
    wktbl.WORKTRANSTYPE = 1 -- Purchase orders
    AND wktbl.WORKSTATUS = 4 -- Closed
    AND wktbl.INVENTSITEID = 'HA USA'
    AND wktbl.INVENTLOCATIONID = '4010'
    AND wktbl.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 05:00:00 AM'
--ORDER BY wktbl.WORKID
/*
UNION
SELECT
    wktbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'           AS [KY_ReceivedDateTime]
    , wktbl.WORKCLOSEDUTCDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'   AS [KY_PutAwayDateTime]
    --, wktbl.WORKID
    , wktbl.ORDERNUM                                                                        AS [OrderNum]
    , wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID                      AS [SKU]
    , CAST(wkln.QTYWORK AS INT)                                                             AS [Qty]
FROM
    [DAX_PROD].[dbo].WHSWORKTABLE wktbl
    JOIN [DAX_PROD].[dbo].WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wkln.LINENUM = 1
    JOIN [DAX_PROD].[dbo].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
WHERE
    wktbl.WORKTRANSTYPE = 1
    AND wktbl.WORKSTATUS = 4 -- Closed
    AND wktbl.INVENTSITEID = 'HA USA'
    AND wktbl.INVENTLOCATIONID = '4010'
    AND wktbl.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 05:00:00 AM'
*/
ORDER BY
    KY_ReceivedDateTime