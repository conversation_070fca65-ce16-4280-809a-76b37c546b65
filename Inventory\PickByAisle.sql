/*
Getting total picks by aisle, or by Group Size (FcSlotTier).
This query is used to analyze the picking activity in a warehouse by grouping picks by aisle and optionally by group size (FcSlotTier).
*/

WITH PickD AS (
SELECT --TOP 20 
    wkln.WORKID
    --, loc.AISLEID
    , wkln.WMSLOCATIONID                AS [Location]
    , CASE 
        WHEN LEFT(wkln.WMSLOCATIONID,2) <> '17' 
            THEN LEFT(wkln.WMSLOCATIONID, 2 )
            ELSE
                CASE WHEN RIGHT(wkln.WMSLOCATIONID,1) < 'J' -- A-D, F-I
                    THEN '17A' ELSE '17Z'
                END
      END    AS [AisleId]
    , RIGHT( wkln.WMSLOCATIONID, 1 )    AS [Bin]
    , wkln.ITEMID                       AS [Item]  
    , idim.INVENTCOLORID                AS [Color]  
    , idim.INVENTSIZEID                 AS [Size]
    , CAST(wkln.QTYWORK AS INT)         AS [Qty]
    , loc.ZONEID                        AS [LocZoneId]
    , CASE WHEN fc.SLOTTIERVALUE  IS NOT NULL THEN fc.SLOTTIERVALUE ELSE 'N/A' END  AS [FcSlotTier]
   -- , COUNT(*)  OVER(PARTITION BY loc.AISLEID, loc.ZONEID) AS [AisleZonePicks]
    --, COUNT(*) OVER(PARTITION BY loc.AISLEID) AS [AislePicks]
    --, loc.LEVEL_
    --, wkln.WORKCLASSID
    --
FROM
    [prodsql02].[DAX_ARCHIVE].[arc].WHSWORKLINE wkln
    INNER JOIN [prodsql02].[DAX_PROD].[dbo].WMSLOCATION loc ON loc.wmslocationid = wkln.wmslocationid AND loc.DATAAREAID = 'ha' AND loc.PARTITION = 5637144576 AND loc.INVENTLOCATIONID = '4010'
    INNER JOIN [prodsql02].[DAX_PROD].[dbo].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
    LEFT JOIN [prodsql02].[DAX_PROD].[dbo].HAFORECASTREPLENISHMENTTABLE fc  ON fc.ITEM = wkln.ITEMID AND idim.INVENTCOLORID =fc.COLOR AND idim.INVENTSIZEID = fc.SIZE_ AND idim.DATAAREAID = fc.DATAAREAID
WHERE
    wkln.WORKSTATUS = 4
    AND wkln.WORKTYPE = 1
    AND loc.LOCPROFILEID IN ( 'Picking', 'Picking A','Picking D', 'PalletPicking' ) 
    AND loc.ZONEID NOT IN ('Current Pick')  -- No GCs
    AND wkln.ITEMID IS NOT NULL
    --AND wkln.MODIFIEDDATETIME BETWEEN '08/1/2023' AND '12/29/2023'
    AND wkln.MODIFIEDDATETIME > GETUTCDATE() - 180
    AND wkln.WORKCLASSID = 'DirectPick'
)
, PickD2 AS
(
    SELECT 
    /*
        WORKID
    --, loc.AISLEID
        , [Location]
        , [AisleId]
        , [Bin]
        , [Item]
        , [Qty]
        , [LocZoneId]*/
        *
        , CASE WHEN LEFT(LocZoneId,4) = LEFT(FcSlotTier, 4) THEN 'Yes' Else 'No' END AS [GroupSizeMatch]
    FROM
        PickD
)
/*
SELECT 
    LEFT(FcSlotTier, 4) AS [GrpSize]
    , COUNT(*)  AS [FcZonePicks]
   --*
FROM 
    PickD2
GROUP BY
    LEFT(FcSlotTier, 4)
ORDER BY
    COUNT(*) DESC
*/   
SELECT 
    AISLEID     AS [Aisle]
    , COUNT(*)  AS AislePicks
FROM 
    PickD2
GROUP BY 
    AISLEID --, ZONEID
ORDER BY
    AislePicks DESC
    --CAST(AISLEID AS INT) ASC

--sp_columns whsworkline

--SELECT * FROM wmslocation where aisleid = 'GI' AND locprofileid IN ( 'Picking', 'Picking A','Picking D', 'PalletPicking' ) 