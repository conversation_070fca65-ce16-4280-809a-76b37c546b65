/*
Picking Data for Ra<PERSON>
Sam's request 2/5/2024

Including "Order header data" and "Picking Data" in one request

*/
SELECT
     ll.CREATEDDATETIME /*AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'*/  AS [UTC_OrderDropDateTime]
    , wktbl.OrderNum          AS [OrderId]
    , CASE 
        WHEN isnull(idim.inventcolorid, '') = '' THEN wkln.itemid
        WHEN isnull(idim.inventsizeid, '') = '' THEN wkln.itemid + '-' + idim.inventcolorid
        ELSE wkln.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid    
    END                                                                 AS [SKU]  
    , CAST(slln.QTYORDERED AS INT)  AS [OrderQty]
    , cnttbl.MODIFIEDDATETIME /*AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' */ AS [UTC_ShipDateTime]

    --, wktbl.SHIPMENTID          AS [ShipmentVIA?]                          

    , CASE WHEN ISNULL(cnttbl.SHIPCARRIERTRACKINGNUM, '') <> '' THEN -- It's not null
                CASE WHEN cnttbl.SHIPCARRIERTRACKINGNUM  LIKE '1Z%' THEN 'UPS' ELSE 'FedEx' END
            ELSE  -- Container Manifest ID is null
                CASE WHEN ISNULL(cnttbl.SHIPCARRIERTRACKINGNUM, '') <> '' THEN -- It's not null
                    CASE WHEN cnttbl.MASTERTRACKINGNUM  LIKE '1Z%' THEN 'UPS' ELSE 'FedEx' END
                ELSE
                    'Unknown'
                END
        END AS [Carrier]
    , sltbl.INVOICEACCOUNT                      AS [CustomerId]
    , wktbl.ContainerID                         AS [CartonId]  
    , wkln.UNITID                               AS [OrderQtyFormFactor]
    , CAST(cnttbl.WEIGHT AS DECIMAL(10, 2))     AS [ParcelWeightInLb]
    --, cnttbl.WEIGHTUOM                          AS [WeightUOM]
    , CAST(cnttbl.WIDTH AS DECIMAL(10, 2))      AS [ParcelWidth_InInch]
    , CAST(cnttbl.LENGTH AS DECIMAL(10, 2))     AS [ParcelLength_InInch]
    , CAST(cnttbl.HEIGHT AS DECIMAL(10, 2))     AS [ParcelHeight_InInch]
    , CAST(wkln.QTYWORK AS INT)                 AS [QtyPicked]
    , wkln.WMSLOCATIONID                        AS [PickLocation]
    --, loc.ZONEID                                AS [PickZone] 
    , 'Active Pick'                           AS [PickZone]  -- Do they need the specific zone?
    , wkln.WORKID                               AS [PickId]

    --, sltbl.CUSTACCOUNT                       AS [FreightPayee]
    , wktbl.WORKCLOSEDUTCDATETIME               AS [UTC_PackDateTime] /*AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' */
   /*
    , 'Parcel'              AS [FreightType]
    , wktbl.WAVEID          AS [WaveNumber]
    , wktbl.SHIPMENTID      AS [ShipmentId]
    , CONVERT( DECIMAL( 10, 2 ), physd.[WEIGHT],    0)  AS 'WeightInPounds'
    , CONVERT( DECIMAL( 10, 2 ), physd.[DEPTH],     0 ) AS 'DepthInInches'
    , CONVERT( DECIMAL( 10, 2 ), physd.[WIDTH],     0 ) AS 'WidthInInches'
    , CONVERT( DECIMAL( 10, 2 ), physd.[HEIGHT],    0 ) AS 'HeightInInches'
    , wktbl.CONTAINERID             AS [ContainerId]
    , cnttbl.CONTAINERTYPECODE      AS [ContainerType]
    
    
    , wkln.ZONEID                   AS [PickZone]
    , 'Loose Pick'                  AS [PickType]*/
    
    
    
    
    
FROM
    [DAX_Archive].[arc].WHSWORKLINE wkln 
    INNER JOIN [DAX_Archive].[arc].WHSWORKTABLE wktbl       ON wktbl.WORKID         = wkln.WORKID       AND wktbl.DATAAREAID    = 'ha'  AND wktbl.[PARTITION]   = 5637144576
    INNER JOIN [DAX_Archive].[arc].INVENTDIM idim           ON idim.INVENTDIMID     = wkln.INVENTDIMID  AND idim.DATAAREAID     = 'ha'  AND idim.[PARTITION]    = wkln.[PARTITION]
    /*LEFT JOIN  [DAX_PROD].[dbo].WHSPHYSDIMUOM physd         ON physd.ITEMID         = wkln.ITEMID       AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID
                AND physd.DATAAREAID = wkln.DATAAREAID AND physd.[PARTITION] = wkln.[PARTITION]*/
    INNER JOIN [DAX_Archive].[arc].WHSCONTAINERTABLE cnttbl ON cnttbl.CONTAINERID   = wktbl.CONTAINERID AND cnttbl.DATAAREAID   = 'ha'  AND cnttbl.[PARTITION]  = wktbl.[PARTITION]
    INNER JOIN [DAX_PROD].[dbo].SALESTABLE sltbl            ON sltbl.SALESID        = wktbl.ORDERNUM    AND sltbl.DATAAREAID    = wktbl.DATAAREAID AND sltbl.PARTITION = wktbl.[PARTITION]
    INNER JOIN [DAX_PROD].[dbo].SALESLINE slln              ON sltbl.SALESID        = slln.SALESID      AND sltbl.DATAAREAID    = slln.DATAAREAID AND sltbl.PARTITION = slln.[PARTITION]
                AND slln.ITEMID = wkln.ITEMID AND slln.INVENTTRANSID  = wkln.INVENTTRANSID
    INNER JOIN [DAX_Archive].[arc].WHSLOADLINE ll           ON ll.LOADID            = wkln.LOADID       AND ll.DATAAREAID       = 'ha'  AND ll.[PARTITION]      = wkln.[PARTITION]
                                --  AND ll.ITEMID = wkln.ITEMID AND ll.INVENTDIMID = wkln.INVENTDIMID 
                                  AND wkln.INVENTTRANSID = ll.INVENTTRANSID
                                --    AND wkln.LOADLINEREFRECID = ll.RECID
    INNER JOIN [DAX_PROD].[dbo].WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.DATAAREAID = wkln.DATAAREAID AND loc.[PARTITION] = wkln.[PARTITION]
    
WHERE
    wkln.WORKCLASSID = 'DirectPick'
    AND wkln.WORKSTATUS = 4  -- Closed line
    AND wktbl.WORKSTATUS = 4 -- Closed header
    AND wkln.WorkType = 1 -- Pick lines only
    AND ISNULL(cnttbl.SHIPCARRIERTRACKINGNUM, '') <> '' -- Shipped
    AND wkln.ITEMID NOT IN ( '3333', '30991')
    AND ll.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 04:59:59 AM'
ORDER BY
    OrderId, wktbl.ContainerId
/*
SELECT *
FROM 
    WHSWORKLINE wkln
    LEFT JOIN salesline sl ON wkln.ORDERNUM = sl.SALESID AND sl.ITEMID = wkln.ITEMID AND sl.INVENTTRANSID = wkln.INVENTTRANSID AND sl.DATAAREAID = wkln.DATAAREAID AND sl.[PARTITION] = wkln.[PARTITION]   
WHERE 
    --wkln.WORKID = 'WK0030607962'
    sl.QTYORDERED <> wkln.QTYWORK
    AND wkln.WORKTYPE = 1
    AND wkln.WORKCLASSID = 'DirectPick'
    AND wkln.WORKSTATUS = 4
    AND wkln.MODIFIEDDATETIME BETWEEN '12/1/2023 05:00:00 AM'   AND '12/5/2023 05:00:00 AM'
ORDER BY
    sl.RECID

SELECT *
FROM SALESTABLE
WHERE 
    SALESID = '45698400'
    AND INVENTLOCATIONID = '4010'
    AND SALESTATUS = 3 -- Invoiced
    AND SALESTYPE = 3 -- Sales order
*/

--sp_columns whsloadline
--sp_columns whsworkline
--sp_columns whsworktable
--sp_columns whscontainertable
--sp_columns salestable
--sp_columns wmslocation