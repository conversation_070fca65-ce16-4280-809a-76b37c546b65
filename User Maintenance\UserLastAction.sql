-- Find the last action from the operators

SELECT 
    WL.USERID AS ID
    , CASE WHEN ISNULL( WU.USERNAME, '' ) = '' THEN 'No Name' ELSE WU.USERNAME END AS Name
    , wl.WORKCLASSID
    , MIN( WL.WORKCLOSEDUTCDATETIME ) AT TIME Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS FirstSeenOn 
    , MAX( WL.WORKCLOSEDUTCDATETIME ) AT TIME Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS LastSeenOn 
FROM 
    [DAX_Archive].[arc].WHSWORKLINE WL
    --[DAX_PROD].[dbo].WHS<PERSON>ORKLINE WL
LEFT JOIN  
    [DAX_PROD].[dbo].WHSWORKUSER WU ON WU.USERID = WL.USERID AND WL.MODIFIEDBY IN ( 'svc-vite', 'wfexc' )
WHERE
    wu.USERID IN('ajsa' ) 
GROUP BY 
    WL.USERID, WU.USERNAME, wl.WOR<PERSON>CLASSID --, WL.WORKCLOSEDUTCDATETIME
ORDER BY 
    ID, LastSeenOn ASC