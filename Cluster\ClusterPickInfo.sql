
--Clusters( with units )

SELECT CLP.CLUSTERID AS ClusterID, CLP.CREATEDBY AS CartBuilder, CLP.CreatedOn, 
CASE 
	WHEN DATEPART( hh, CLP.CreatedOn ) IN ( 00, 01, 02, 03, 16, 17, 18, 19, 20, 21, 22, 23 ) THEN '2nd'
	ELSE '1st'
END AS CreatedOn,
UNC.ClusterTotal, UNC.Units AS TotalUnits, CLP.ClusterProfile
FROM 
(
	SELECT CL.CLUSTERID AS ClusterID, CT.CREATEDBY AS CreatedBy, 
	CASE    WHEN DATEPART( mm, CT.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, CT.CREATEDDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, CT.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, CT.CREATEDDATETIME ) -- No DST
        WHEN DATEPART( mm, CT.CREATEDDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, CT.CREATEDDATETIME ) < 8 OR DATEPART( dd, CT.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, CT.CREATEDDATETIME ) -- No DST
                    WHEN DATEPART( dd, CT.CREATEDDATETIME ) - DATEPART( w, CT.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, CT.CREATEDDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, CT.CREATEDDATETIME )
             END
        WHEN DATEPART( dd, CT.CREATEDDATETIME ) - DATEPART( w, CT.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, CT.CREATEDDATETIME )
        ELSE DATEADD( hh, - 4, CT.CREATEDDATETIME )
END                             AS CreatedOn, CT.CLUSTERPROFILEID AS ClusterProfile, COUNT( CL.WORKID ) AS WkPending
	FROM WHSWORKCLUSTERTABLE CT
	INNER JOIN WHSWORKCLUSTERLINE CL ON CL.CLUSTERID = CT.CLUSTERID
	LEFT JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID
	WHERE	
		WT.WORKSTATUS < 5 		-- Not canceled
		AND	WT.WORKTRANSTYPE = 2   -- Sales Order 
		AND WT.WORKTEMPLATECODE NOT LIKE 'W0%'  -- Not wholesale
		AND CT.CREATEDDATETIME > ( GETUTCDATE() - 2 )		--Time frame
			--AND WT.WAVEID = 'WV000169172'
	GROUP BY CL.CLUSTERID, CT.CREATEDBY, CT.CREATEDDATETIME, CT.CLUSTERPROFILEID, CAST( WT.CREATEDDATETIME AS DATE )
) AS CLP
LEFT JOIN 
( 
	SELECT cl.CLUSTERID, COUNT( DISTINCT cl.WORKID ) AS ClusterTotal, SUM( CONVERT( DECIMAL, ll.WORKCREATEDQTY, 10 ) ) AS Units
	FROM WHSWORKCLUSTERLINE cl 
	LEFT JOIN WHSWORKTABLE wt ON cl.WORKID = wt.WORKID AND wt.[PARTITION] = cl.[PARTITION] AND wt.DATAAREAID = cl.DATAAREAID
	LEFT JOIN WHSWORKCLUSTERTABLE ct ON ct.CLUSTERID = cl.CLUSTERID AND ct.[PARTITION] = cl.[PARTITION] AND ct.DATAAREAID = cl.DATAAREAID
	LEFT JOIN WHSLOADLINE ll ON wt.LOADID = ll.LOADID AND ll.SHIPMENTID = wt.SHIPMENTID AND ll.ORDERNUM = wt.ORDERNUM AND wt.[PARTITION] = ll.[PARTITION] AND wt.DATAAREAID = ll.DATAAREAID
	WHERE 
		WT.WORKSTATUS < 5 		-- Not canceled
		AND	WT.WORKTRANSTYPE = 2   -- Sales Order 
		AND WT.WORKTEMPLATECODE NOT LIKE 'W0%'  -- Not wholesale
		AND ct.CREATEDDATETIME > ( GETUTCDATE() - 2 )
	GROUP BY cl.CLUSTERID
) AS UNC  -- Cluster info with units
ON CLP.ClusterID = UNC.CLUSTERID
/*WHERE
	UNC.ClusterID = 'CL001233634'*/
ORDER BY CLP.CLUSTERID


/*
SELECT TOP 20 *
FROM WHSWORKCLUSTERTABLE

SELECT TOP 20 *
FROM WHSWORKCLUSTERLINE

SELECT TOP 20 *
FROM WHSLOADLINE
*/
