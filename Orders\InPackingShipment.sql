
-- Shipments that remains "In packing" after the qty on the load is fixed

SELECT 
    SHIPMENTID
    , LOADI<PERSON>
    , OR<PERSON><PERSON>NUM
    , CASE 
        WHEN SHIPMENTSTATUS = 0 THEN 'Open'
        WHEN SHIPMENTSTATUS = 1 THEN 'Waved'
        WHEN SHIPMENTSTATUS = 2 THEN 'In process'
        WHEN SHIPMENTSTATUS = 3 THEN 'In packing'
        WHEN SHIPMENTSTATUS = 4 THEN 'Loaded'
        WHEN SHIPMENTSTATUS = 5 THEN 'Shipped'
        WHEN SHIPMENTSTATUS = 6 THEN 'Received'
    END AS [ShipmentStatus]
FROM
    WHSSHIPMENTTABLE 
WHERE
    SHIPMENTID = 'SH15034707'
    AND INVENTLOCATIONID = '4010'

/*    
BEGIN TRAN;
UPDATE [DAX_PROD].[dbo].[WHSSHIPMENTTABLE]
    SET SHIPMENTSTATUS  = 5 -- Shipped
WHERE 
    SHIPMENTID = 'SH15034707'
COMMIT;
*/