-- From <PERSON>
/*
Before
1008 rows, 10:59
After including partition and dataareaid in the joins
1034 rows, 3:14


*/
SELECT 
    ito.ITEMID              AS [Item]
    , id.INVENTCOLORID      AS [Color]
    , id.INVENTSIZEID       AS [Size]
    --, id.inventlocationid   AS [Warehouse]
    , id.wmslocationid      AS [Location]
    , id.LICENSEPLATEID
    , id.MODIFIEDBY
    , itp.VOUCHER
    , it.QTY
    , it.costamountposted
    , it.CostAmountAdjustment
    --, ito.REFERENCECATEGORY
    , it.DATEPHYSICAL 
    , CASE
        WHEN ito.REFERENCECATEGORY = 0 THEN 'Sales Order'
        WHEN ito.REFERENCECATEGORY = 2 THEN 'Production'
        WHEN ito.REFERENCECATEGORY = 3 THEN 'Purchase Order'
        WHEN ito.REFERENCECATEGORY = 4 THEN 'Transaction'
        WHEN ito.REFERENCECATEGORY = 5 THEN 'Inventory Adjustment'
        WHEN ito.REFERENCECATEGORY = 6 THEN 'Transfer'
        WHEN ito.REFERENCECATEGORY = 13 THEN 'Counting'
        WHEN ito.REFERENCECATEGORY = 15 THEN 'Quarantine order'
        WHEN ito.REFERENCECATEGORY = 21 THEN 'Transfer order shipment'
        WHEN ito.REFERENCECATEGORY = 22 THEN 'Transfer order receive'
        WHEN ito.REFERENCECATEGORY = 201 THEN 'Work'
        WHEN ito.REFERENCECATEGORY = 202 THEN 'Quarantine'
        ELSE 'Other'
    END AS 'Transaction Type'
FROM 
    INVENTTRANSPOSTING itp
    LEFT JOIN inventtrans it on it.voucher = itp.voucher AND it.[PARTITION] = itp.[PARTITION] AND it.DATAAREAID = itp.DATAAREAID
    LEFT JOIN inventtransorigin ito on ito.recid = it.INVENTTRANSORIGIN AND ito.[PARTITION] = it.[PARTITION] AND ito.DATAAREAID = it.DATAAREAID
    LEFT JOIN INVENTDIM id on it.INVENTDIMID = id.INVENTDIMID AND it.[PARTITION] = id.[PARTITION] AND id.DATAAREAID = id.DATAAREAID
WHERE 
    1 = 1
    --AND it.DATEPHYSICAL > '10/29/2023' --and it.DATEPHYSICAL <= '10/6/18'
    AND ito.REFERENCECATEGORY in (13,5)
    AND inventlocationid in ('4010') -- ,'4010q','z003', 'z004', 'z005'
    AND id.WMSLOCATIONID = '17-001X'
--GROUP BY 
--    ito.ITEMID, itp.VOUCHER, it.qty, it.costamountposted,it.CostAmountAdjustment,ito.REFERENCECATEGORY,it.DATEPHYSICAL,id.inventlocationid, id.wmslocationid, id.LICENSEPLATEID
ORDER BY
    it.qty ASC
/*
SELECT TOP 5 *
FROM INVENTTRANSPOSTING
WHERE
VOUCHER = 'ICNT00124003'
*/