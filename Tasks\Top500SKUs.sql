
/*
How difficult would it be for you to pull the following data: Top 500 skus (w/ qty sold) from 11/24-12/1 and how many locations we used for each sku... 
so essentially I'd like to see how many of those top 500 were in gaylords and for that week how many other pick locations were occupied just by those units. 
If you are able to pull it that way, the other part that will be hard to know but important to understand is of those with mulitiple locations, did the reset 
process cause it or just demand... Trying to limit to one week so resets doesn't skew the data too much.
*/
USE DAX_PROD

DECLARE @StartDate AS DATETIME = '05/14/2025'
DECLARE @EndDate AS DATETIME = '05/27/2025'
DECLARE @ReportDays AS INT = 2
DECLARE @MinSalesQty AS INT = 5;

-- Calculating what type of replenishments have been used for the most sold SKUs
-- Modifying it to gather requested data

WITH RegLocSales AS
(
    SELECT
        SKUSales.SKU
        , SKUSales.Qty              AS [RegLocUnitsSold]
        , SKUSales.RegLoc           AS [RegLocUsedToPickFrom]
        , RepBySKU.MinMaxCartons    AS [RegLocMinMaxCartons]
        , RepBySKU.DemandCartons    AS [RegLocDemandCartons]
        , RepBySKU.ResetCartons     AS [RegLocResetCartons]
        , RepBySKU.TotalUnits       AS [RegLocTotalUnitsReplenished]
        , ManualMoves.MMCount       AS [RegLocManualMoves]
        , ManualMoves.MMTotal       AS [RegLocTotalPiecesMoved]
    FROM
    (
    SELECT 
        wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
        --, wktbl.WORKID
        , CONVERT( DECIMAL( 10, 0 ), SUM( wkln.INVENTQTYWORK ) )     AS 'Qty'
        , COUNT( DISTINCT wkln.WMSLOCATIONID )                       AS [RegLoc]
    FROM
        WHSWORKTABLE wktbl
        LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
        LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
        LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
    WHERE
        wktbl.CREATEDDATETIME BETWEEN @StartDate AND @EndDate
        AND wktbl.WORKTRANSTYPE = 2  -- Sales Order
        AND wkln.WORKTYPE = 1 -- Pick. To get only one line
        AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
        AND loc.locprofileid LIKE 'Picking%' -- Only Picking locations, no gaylords
        AND wktbl.WORKSTATUS < 5 -- Not cancelled
    GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
    HAVING COUNT( * ) > @MinSalesQty 
    ) AS SKUSales
    LEFT JOIN
    (    
    SELECT
        SKU
        , SUM( CASE WHEN ReplenType = 'MinMax' THEN 1 ELSE 0 END)  AS [MinMaxCartons]
        , SUM( CASE WHEN ReplenType = 'Demand' THEN 1 ELSE 0 END)  AS [DemandCartons]
        , SUM( CASE WHEN ReplenType = 'Reset' THEN 1 ELSE 0 END)   AS [ResetCartons]
        , SUM( CartonQty )                                         AS [TotalUnits]
    FROM    
        (
            SELECT
                wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
                --, wkln.WORKID
                , CASE 
                    WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 'MinMax' 
                    WHEN wktbl.WORKTEMPLATECODE = 'Reset Replenishment' THEN 'Reset' 
                    ELSE 'Demand' END AS ReplenType
            , CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )     AS 'CartonQty'
            FROM
                WHSWORKTABLE wktbl
                LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
                LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
                LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
            WHERE
                wktbl.CREATEDDATETIME BETWEEN @StartDate - 1 AND @EndDate
                AND wktbl.WORKTRANSTYPE = 11  -- Replenishment
                AND wkln.WORKTYPE = 2 -- Put. To get only one line
                AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
                AND loc.locprofileid LIKE 'Picking%' -- Only Picking locations, no gaylords
                AND wktbl.WORKSTATUS < 5 -- Not cancelled
                --AND wktbl.WORKID ='WK0025410522'
                --GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, wktbl.WORKTEMPLATECODE, wkln.INVENTQTYWORK
        ) AS RepDetails
    GROUP BY SKU     
    ) AS RepBySKU       
    ON RepBySKU.SKU = SKUSales.SKU
    LEFT JOIN
    (
    SELECT
        SKU
        , COUNT( * )                AS[MMCount]
        , SUM(['ItemsMoved'])       AS [MMTotal]
    FROM
        (
        SELECT
            wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
            --, wkln.WORKID                                                 AS [WorkID]
            --, CASE WHEN wktbl.WORKTEMPLATECODE = 'Inventory Movement' THEN 'InventoryMovement' ELSE 'Unknown' END AS ReplenType
            , CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )      AS ['ItemsMoved']
        FROM
            WHSWORKTABLE wktbl
            LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
            LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
            LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
        WHERE
            wktbl.CREATEDDATETIME BETWEEN @StartDate AND @EndDate
            AND wktbl.WORKTRANSTYPE = 7  -- Inventory movement
            AND wkln.WORKTYPE = 2 -- Put. To get only one line
            AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
            AND loc.locprofileid LIKE 'Picking%' -- Only Picking locations, no gaylords
            AND wktbl.WORKSTATUS < 5 -- Not cancelled
            AND ISNULL(wktbl.TARGETLICENSEPLATEID, '') <> '' -- Only movements coming from Bulk
            --AND wktbl.WORKID ='WK0025410522'
        --GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, wktbl.WORKID
        ) AS ManualM
    GROUP BY SKU
    ) AS ManualMoves   
    ON ManualMoves.SKU = SKUSales.SKU
    WHERE 
        1 = 1
        --AND SKUSales.SKU LIKE '81259-75Z%'
    --ORDER BY SKUSales.Qty DESC
)
SELECT TOP 500
    RegLocSales.SKU
    , COALESCE(RegLocSales.[RegLocUnitsSold], 0) + COALESCE(SKUSales.Qty, 0)          AS [TotalUnitsSold]
    , COALESCE(RegLocSales.[RegLocUsedToPickFrom], 0) + COALESCE(SKUSales.RegLoc, 0)  AS [TotalLocUsed]
    , RegLocSales.[RegLocUsedToPickFrom]                    AS [TotalRegLocUsed]
    , SKUSales.RegLoc                                       AS [TotalGaylordsUsed]
    , RegLocSales.[RegLocMinMaxCartons]             
    , RepBySKU.MinMaxCartons                                AS [GaylordMinMaxCartons]
    , RegLocSales.[RegLocDemandCartons]
    , RepBySKU.DemandCartons                                AS [GaylordDemandCartons]
    , RegLocSales.[RegLocResetCartons]
    , RepBySKU.ResetCartons                                 AS [GaylordResetCartons]
    --, RegLocSales.[RegLocTotalUnitsReplenished]
    , RegLocSales.[RegLocManualMoves]
    , ManualMoves.MMCount       AS [GaylordManualMoves]
    --, RegLocSales.[RegLocTotalPiecesMoved]
    --, SKUSales.Qty              AS [GaylordUnitsSold]
    --, RepBySKU.TotalUnits       AS [GaylordTotalUnitsReplenished]
    -- ManualMoves.MMTotal       AS [GaylordTotalPiecesMoved]
FROM
    RegLocSales
LEFT JOIN
(
SELECT 
    wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
    --, wktbl.WORKID
    , CONVERT( DECIMAL( 10, 0 ), SUM( wkln.INVENTQTYWORK ) )     AS [Qty]
    , COUNT( DISTINCT wkln.WMSLOCATIONID )                       AS [RegLoc]
FROM
    WHSWORKTABLE wktbl
    LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
    LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
WHERE
    wktbl.CREATEDDATETIME BETWEEN @StartDate AND @EndDate
    AND wktbl.WORKTRANSTYPE = 2  -- Sales Order
    AND wkln.WORKTYPE = 1 -- Pick. To get only one line
    AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
    AND loc.locprofileid LIKE 'PalletPicking' -- Only gaylords
    AND wktbl.WORKSTATUS < 5 -- Not cancelled
GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
HAVING COUNT( * ) > @MinSalesQty 
) AS SKUSales
ON RegLocSales.SKU = SKUSales.SKU
LEFT JOIN
(    
SELECT
    SKU
    , SUM( CASE WHEN ReplenType = 'MinMax' THEN 1 ELSE 0 END)  AS [MinMaxCartons]
    , SUM( CASE WHEN ReplenType = 'Demand' THEN 1 ELSE 0 END)  AS [DemandCartons]
    , SUM( CASE WHEN ReplenType = 'Reset' THEN 1 ELSE 0 END)   AS [ResetCartons]
    , SUM( CartonQty )                                         AS [TotalUnits]
FROM    
    (
        SELECT
            wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
            --, wkln.WORKID
            , CASE 
                WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 'MinMax' 
                WHEN wktbl.WORKTEMPLATECODE = 'Reset Replenishment' THEN 'Reset' 
                ELSE 'Demand' END AS ReplenType
        , CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )     AS 'CartonQty'
        FROM
            WHSWORKTABLE wktbl
            LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
            LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
            LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
        WHERE
            wktbl.CREATEDDATETIME BETWEEN @StartDate - 1 AND @EndDate
            AND wktbl.WORKTRANSTYPE = 11  -- Replenishment
            AND wkln.WORKTYPE = 2 -- Put. To get only one line
            AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
            AND loc.locprofileid LIKE 'PalletPicking' -- Only gaylords
            AND wktbl.WORKSTATUS < 5 -- Not cancelled
            --AND wktbl.WORKID ='WK0025410522'
            --GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, wktbl.WORKTEMPLATECODE, wkln.INVENTQTYWORK
    ) AS RepDetails
GROUP BY SKU     
) AS RepBySKU       
ON RepBySKU.SKU = SKUSales.SKU
LEFT JOIN
(
SELECT
    SKU
    , COUNT( * )                AS[MMCount]
    , SUM(['ItemsMoved'])       AS [MMTotal]
FROM
    (
    SELECT
        wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
        --, wkln.WORKID                                                 AS [WorkID]
        --, CASE WHEN wktbl.WORKTEMPLATECODE = 'Inventory Movement' THEN 'InventoryMovement' ELSE 'Unknown' END AS ReplenType
        , CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )      AS ['ItemsMoved']
    FROM
        WHSWORKTABLE wktbl
        LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
        LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
        LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
    WHERE
        wktbl.CREATEDDATETIME BETWEEN @StartDate AND @EndDate
        AND wktbl.WORKTRANSTYPE = 7  -- Inventory movement
        AND wkln.WORKTYPE = 2 -- Put. To get only one line
        AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
        AND loc.locprofileid LIKE 'PalletPicking' -- Only gaylords
        AND wktbl.WORKSTATUS < 5 -- Not cancelled
        AND ISNULL(wktbl.TARGETLICENSEPLATEID, '') <> '' -- Only movements coming from Bulk
        --AND wktbl.WORKID ='WK0025410522'
    --GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, wktbl.WORKID
    ) AS ManualM
GROUP BY SKU
) AS ManualMoves   
ON ManualMoves.SKU = SKUSales.SKU
WHERE 
    1 = 1
    --AND SKUSales.SKU LIKE '81259-75Z%'
ORDER BY 
    TotalUnitsSold DESC

