

USE DAX_PROD;

DECLAR<PERSON> @ReportDays INT = 14;


DECLAR<PERSON> @TimeThreshold INT = 180; -- If there is more than 3 minutes difference between scans, the operator should be doing something else: discard

-- Calculating time between boxes received

WITH rpa (WorkID, userid, CreatedDT, WorkTemplate, LoadId) AS
(
SELECT
    wkln.WORKID
    , wkln.USERID
    , wkln.MOD<PERSON>IED<PERSON>TETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS CreatedDT -- DC time zone
    , wktbl.WORKTEMPLATECODE AS WorkTemplate
    , wktbl.LOADID
FROM
    WHSWORKLINE wkln
    JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wkln.DATAAREAID = wktbl.DATAAREAID AND wkln.[PARTITION] = wktbl.[PARTITION] 
WHERE
    CAST(DATEADD(hh, -4, wkln.MODIFIEDDATETIME) AS DATE) > GETUTCDATE() - @ReportDays
    AND wkln.WORKCLASSID IN ( 'RecPutaway', 'RecCubeIN' )
    AND wkln.WORKTYPE = 1 -- Pick
    AND wktbl.INVENTLOCATIONID = '4010' -- KY DC
    --AND wktbl.WORKTRANSTYPE = 1
),
rpac AS
(
SELECT
    CreatedDT
    , LAG(CreatedDT) OVER (PARTITION BY userid ORDER BY CreatedDT) AS previous_timestamp
    , userid
    --, DATEDIFF(SECOND, LAG(CreatedDT) OVER (PARTITION BY userid ORDER BY CreatedDT), CreatedDT) AS tds
    , CASE
        WHEN LAG(CreatedDT) OVER (PARTITION BY userid ORDER BY CreatedDT) IS NOT NULL
        THEN DATEDIFF(SECOND, LAG(CreatedDT) OVER (PARTITION BY userid ORDER BY CreatedDT), CreatedDT)
        ELSE NULL
    END AS time_difference_seconds
    , WorkTemplate
    , LoadId
    , CASE WHEN DATEPART( hh, CreatedDT ) IN ( 00, 01, 02, 03, 16, 17, 18, 19, 20, 21, 22, 23 ) THEN '2nd' ELSE '1st' END AS ShiftReceivedBy
FROM
    rpa
)
SELECT *
FROM
    rpac
WHERE
    time_difference_seconds < @TimeThreshold
ORDER BY
    CreatedDT



-- 