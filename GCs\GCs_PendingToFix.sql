
-- Finding GCs orders that are already shipped, but still open

SELECT 
    st.SALESID
FROM    
    SALESTABLE st 
    INNER JOIN salesline sl             ON st.salesid = sl.salesid              AND st.[PARTITION] = sl.[PARTITION]         AND st.DATAAREAID = sl.D<PERSON><PERSON><PERSON>AID
    INNER JOIN WHSSHIPMENTTABLE wshptbl ON wshptbl.ORDERNUM = st.SALESID        AND st.[PARTITION] = wshptbl.[PARTITION]    AND st.DATAAREAID = wshptbl.DATAAREAID
    INNER JOIN WHSCONTAINERTABLE wct    ON wct.SHIPMENTID = wshptbl.SHIPMENTID  AND wct.[PARTITION] = wshptbl.[PARTITION]   AND wct.DATAAREAID = wshptbl.DATAAREAID
WHERE
    sl.itemid = '30991' -- GC
    AND st.SALESSTATUS = 1  -- Open orders
    AND st.CREATEDDATETIME > GETUTCDATE() - 7  -- Orders from the last seven days
    AND ISNULL( wct.SHIPCAR<PERSON>ERTRACKINGNUM, '') <> '' -- Not shipped