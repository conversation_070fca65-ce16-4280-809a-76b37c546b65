
DECLARE @ReportDays INT = 16
DECLARE @GapTime INT = 30

-- AB orders completed too fast

SELECT
    PickT.PickWorkID
    , PickT.ModifiedBy
   -- , PutT.PutTime
    , CAST( 
CASE    WHEN DATEPART( mm, PickT.PickTime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, PickT.PickTime ) -- Daylight Savings Months 
        WHEN DATEPART( mm, PickT.PickTime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, PickT.PickTime ) -- No DST
        WHEN DATEPART( mm, PickT.PickTime ) = 3 
        THEN CASE   WHEN DATEPART( dd, PickT.PickTime ) < 8 OR DATEPART( dd, PickT.PickTime ) > 14 THEN  DATEADD( hh, - 5, PickT.PickTime ) -- No DST
                    WHEN DATEPART( dd, PickT.PickTime ) - DATEPART( w, PickT.PickTime ) + 1  >= 8 THEN  DATEADD(hh, - 4, PickT.PickTime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, PickT.PickTime )
             END
        WHEN DATEPART( dd, PickT.PickTime ) - DATEPART( w, PickT.PickTime ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, PickT.PickTime )
        ELSE DATEADD( hh, - 4, PickT.PickTime )
END   AS DATE )  AS WkDate
FROM
(
SELECT
    --TOP 5
    wkln.WORKID     AS PutWorkID
    , wkln.ORDERNUM 
    , wkln.SHIPMENTID
    , wkln.WORKCLOSEDUTCDATETIME AS PutTime
    , wkln.USERID
    , wkln.MODIFIEDBY
FROM 
    WHSWORKLINE wkln
    --LEFT JOIN WHSWORKTABLE wktbl ON wkln.workID = wktbl.workID AND wkln.PARTITION = wktbl.PARTITION AND wkln.DATAAREAID = wktbl.DATAAREAID
WHERE
    --wktbl.worktemplatecode = '4010 AutoBagger Sing'
    wkln.WORKCLASSID = 'DirectPick'
    AND wkln.wmslocationID = 'AutoBagger'
    AND wkln.WORKSTATUS = 4
    AND wkln.MODIFIEDBY NOT IN ( 'svc-vite' )
    --AND wkln.USERID <> 'MHX'
    AND wkln.WORKTYPE = 2
    AND wkln.MODIFIEDDATETIME > GETUTCDATE() - @ReportDays
) AS PutT
INNER JOIN
(
SELECT
    --TOP 5
    wkln.WORKID     AS PickWorkID
    , wkln.ORDERNUM 
    , wkln.SHIPMENTID
    , wkln.WORKCLOSEDUTCDATETIME AS PickTime
    , wkln.USERID
    , wkln.MODIFIEDBY
FROM 
    WHSWORKLINE wkln
    --LEFT JOIN WHSWORKTABLE wktbl ON wkln.workID = wktbl.workID AND wkln.PARTITION = wktbl.PARTITION AND wkln.DATAAREAID = wktbl.DATAAREAID
WHERE
    --wktbl.worktemplatecode = '4010 AutoBagger Sing'
    wkln.WORKCLASSID = 'AutoBSngl'
    AND wkln.wmslocationID = 'AutoBagger'
    AND wkln.WORKSTATUS = 4
    AND wkln.MODIFIEDBY NOT IN ( 'svc-vite' )
    --AND wkln.USERID <> 'MHX'
    AND wkln.WORKTYPE = 1
    AND wkln.MODIFIEDDATETIME > GETUTCDATE() - @ReportDays
) AS PickT
ON PickT.PickWorkID = PutT.PutWorkID AND PickT.ORDERNUM = PutT.ORDERNUM AND PickT.SHIPMENTID = PutT.SHIPMENTID AND PickT.MODIFIEDBY = PutT.MODIFIEDBY
WHERE
    DATEDIFF( second, PutT.PutTime, PickT.PickTime ) < @GapTime
ORDER BY
    PickT.PickTime