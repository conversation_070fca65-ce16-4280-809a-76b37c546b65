
-- Exceptions from the AIF - Voice
SELECT
    CASE 
        WHEN extbl.EXCEPTION = 0    THEN 'Info'
        WHEN extbl.EXCEPTION = 1    THEN 'Warning'
        WHEN extbl.EXCEPTION = 2    THEN 'Deadlock'
        WHEN extbl.EXCEPTION = 3    THEN 'Error'
        WHEN extbl.EXCEPTION = 7    THEN 'Sequence'
        WHEN extbl.EXCEPTION = 11   THEN 'UpdateConflict'
        WHEN extbl.EXCEPTION = 13   THEN 'DuplicateKeyException'
        ELSE 'Unknown'
    END AS [Exception]
    , extbl.[DESCRIPTION]   AS [Description]
    , extbl.[MODULE]        AS [Module]
    , extbl.CREATEDBY       AS [CreatedBy]
    , extbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'     AS [CreatedDateTime]
FROM
    SYSEXCEPTIONTABLE extbl
    JOIN AIFEXCEPTIONMAP aifxm ON aifxm.EXCEPTIONID = extbl.RECID
WHERE
    aifxm.PortName = 'HAVoiceLinkServicesHTTP'
    AND extbl.CREATEDDATETIME > '11/13/2023'
    --AND extbl.[DESCRIPTION] LIKE 'WK002943%'

    