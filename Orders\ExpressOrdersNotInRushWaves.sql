
-- Find express orders dropped in regular waves

SELECT
    shptbl.ORDERNUM             AS 'Order'
    , shptbl.MODECODE           AS DeliveryMode
    , st.SALESORIGINID          AS SalesOrigin
    --, shptbl.ADDRESS
   -- , logpa.CITY                AS City
    , logpa.[STATE]             AS 'State'
    , logpa.ZIPCODE             AS Zip
    , shptbl.WAVEID             AS Wave
    , wvtbl.WAVETEMPLATENAME    AS WaveTemplate
    , CASE 
        WHEN wvtbl.WAVESTATUS = 0 THEN 'Open'
        WHEN wvtbl.WAVESTATUS = 1 THEN 'Processing'
        WHEN wvtbl.WAVESTATUS = 2 THEN 'Held'
        WHEN wvtbl.WAVESTATUS = 3 THEN 'Released'
        WHEN wvtbl.WAVESTATUS = 4 THEN 'Picked'
        ELSE 'Shipped' 
    END AS WaveStatus
FROM
    WHSSHIPMENTTABLE shptbl
    INNER JOIN WHSWAVELINE wvln             ON wvln.SHIPMENTID  = shptbl.SHIPMENTID             AND wvln.[PARTITION]    = shptbl.[PARTITION]   AND wvln.DATAAREAID  = shptbl.DATAAREAID
    INNER JOIN WHSWAVETABLE wvtbl           ON wvtbl.WAVEID     = wvln.WAVEID                   AND wvln.[PARTITION]    = wvtbl.[PARTITION]    AND wvln.DATAAREAID  = wvtbl.DATAAREAID
    INNER JOIN SALESTABLE st                ON st.SALESID       = shptbl.ORDERNUM               AND st.[PARTITION]      = shptbl.[PARTITION]   AND st.DATAAREAID    = shptbl.DATAAREAID
    INNER JOIN LOGISTICSPOSTALADDRESS logpa ON logpa.RECID      = shptbl.DELIVERYPOSTALADDRESS  AND logpa.[PARTITION]   = shptbl.[PARTITION]  
WHERE
    wvtbl.WAVETEMPLATENAME NOT LIKE '%Rush%'    -- Not in Rush waves
    AND shptbl.MODECODE IN ( '1D', '2D', '3D')  -- Express orders
    AND shptbl.SHIPMENTSTATUS < 5               -- Not shipped
    AND st.SALESSTATUS = 1                      -- Open orders

