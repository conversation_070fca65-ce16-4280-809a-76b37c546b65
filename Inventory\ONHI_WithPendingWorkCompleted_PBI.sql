

-- Calculating Floor capacity, taking in account pending work
-- LA, modified on 1/21/2025 for PBI

WITH ONH_PWC AS
(
    SELECT
    ONHI_FC.[Location]
    , ONHI_FC.ZoneId
    , ONHI_FC.SKU
    , CONVERT( DECIMAL( 10, 0 ), 0.5*( ONHI_FC.Occupied + ONHI_FC.IncomingWork + ABS(ONHI_FC.Occupied - ONHI_FC.IncomingWork) )  ) AS Occupied -- Calculating the MAX value
    --, CASE WHEN CAST( hagpft.DATETIMESTAMP AS DATE ) > CAST( GETDATE() - 2 AS DATE ) THEN 1 ELSE 0 END AS Online
    FROM
    (
    SELECT  
        DISTINCT loc.wmslocationid	AS 'Location'
        , loc.zoneid			AS ZoneId
        , loc.locprofileid	AS LocProfile
        , 
        CASE WHEN 
                OH.PHYSICALINVENT > 0 THEN OH.itemid +'-' + OH.INVENTCOLORID + '-' +  OH.inventsizeid	
            ELSE
                wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
        END AS SKU   
        , CONVERT( DECIMAL( 10, 0),  OH.reservphysical	)		AS Reserved 
        , CASE WHEN OH.PHYSICALINVENT >= 1 THEN 1 ELSE 0 END    AS Occupied
        , CASE WHEN wkln.QTYWORK >= 1 THEN 1 ELSE 0 END         AS IncomingWork
        , OH.RESERVPHYSICAL
    FROM wmslocation loc
    LEFT OUTER JOIN whsworkline wkln ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha' 
        AND wkln.WORKTYPE = 2 -- Put
        AND wkln.WORKSTATUS < 4 -- Not completed
    LEFT OUTER JOIN inventdim idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = wkln.DATAAREAID
    LEFT OUTER JOIN 

    ( SELECT idim.wmslocationid, isum.itemid, idim.inventcolorid, idim.inventsizeid, isum.physicalinvent, isum.reservphysical
    FROM inventsum isum
    LEFT OUTER JOIN inventdim idim ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = 'ha' AND isum.PARTITION = '5637144576'
    WHERE isum.physicalinvent > 0 AND idim.inventlocationid = '4010' AND idim.DATAAREAID = 'ha'-- KY DC only
    ) as OH -- Pulls location with inventory. Avoiding duplicate records because inventsum has many records
    ON loc.wmslocationID = OH.wmslocationid
    WHERE 
        loc.inventlocationid = '4010' 
        AND loc.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking' /*'Offsite','Bulk'*/) 
        AND loc.zoneid NOT IN ( 'Current Pick') -- Exclude GCs, 3333s
    ) AS ONHI_FC
)
SELECT 
    SUM(Occupied)   AS [LocsAfterPendingWorkCompleted]
    , COUNT(*)      AS [TotalLoc]
    , (SUM(Occupied) / COUNT(*)) AS [PercentFilled]
FROM    
    ONH_PWC 

