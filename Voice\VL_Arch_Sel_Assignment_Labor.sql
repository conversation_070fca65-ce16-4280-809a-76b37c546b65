/****** <PERSON><PERSON>t for SelectTopNRows command from SSMS  ******/

--Selection>>Labor>>Assignment Labor
-- Group by Operator LaborId


SELECT TOP (10) [assignLaborId]
      ,[version]
      ,[assignmentId]
      ,[operatorIdentifier]
      ,[operatorName]
      ,[operatorLaborId]
      ,[breakLaborId]
      ,[startTime]
      ,[endTime]
      ,[duration]
      ,[exportStatus]
      ,[quantityPicked]
      ,[assignmentProrateCount]
      ,[groupProrateCount]
      ,[groupNumber]
      ,[groupCount]
      ,[actualRate]
      ,[percentOfGoal]
      ,[createdDate]
  FROM [VoiceLink].[dbo].[arch_sel_assignment_labor]
  WHERE [startTime] > '6/10/2022' 
	 AND [operatorIdentifier] = 'yuro'
  ORDER BY startTime
