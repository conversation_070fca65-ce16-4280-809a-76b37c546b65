
-- Author: <PERSON>

--Pending clusters, working
-- Modified on 7/8/2022 to align it with the new clustering proces.
-- A cluster could be associated with more than one wave

SELECT PendCL.CLUSTERID, PendCL.CreatedBy, 
CASE    WHEN DATEPART( mm, PendCL.CreatedOn ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, PendCL.CreatedOn ) -- Daylight Savings Months 
        WHEN DATEPART( mm, PendCL.CreatedOn ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, PendCL.CreatedOn ) -- No DST
        WHEN DATEPART( mm, PendCL.CreatedOn ) = 3 
        THEN CASE   WHEN DATEPART( dd, PendCL.CreatedOn ) < 8 OR DATEPART( dd, PendCL.CreatedOn ) > 14 THEN  DATEADD( hh, - 5, PendCL.CreatedOn ) -- No DST
                    WHEN DATEPART( dd, PendCL.CreatedOn ) - DATEPART( w, PendCL.CreatedOn ) + 1  >= 8 THEN  DATEADD(hh, - 4, PendCL.CreatedOn ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, PendCL.CreatedOn )
             END
        WHEN DATEPART( dd, PendCL.CreatedOn ) - DATEPART( w, PendCL.CreatedOn ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, PendCL.CreatedOn )
        ELSE DATEADD( hh, - 4, PendCL.CreatedOn )
END                     AS CreatedOn, 
PendCL.WkPending  		AS WkPending, 
WKC.WkCount  			AS ClusterTotal, 
PendCL.CLUSTERPROFILEID AS ClusterProfile
FROM (
	SELECT CT.CLUSTERID AS ClusterID, CT.CLUSTERPROFILEID, CT.CREATEDBY AS CreatedBy, CT.CREATEDDATETIME AS CreatedOn, COUNT( CL.WORKID ) AS WkPending/*,
	MIN( CAST( WT.CREATEDDATETIME AS DATE ) )AS WorkCreatedDate , MIN( CAST( wll.CREATEDDATETIME AS DATE ) ) AS ReleasedToWHDate*/
	FROM WHSWORKCLUSTERLINE CL
	INNER JOIN WHSWORKCLUSTERTABLE CT ON CL.CLUSTERID = CT.CLUSTERID
	INNER JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID
	INNER JOIN WHSCONTAINERTABLE CNTTbl ON CNTTbl.CONTAINERID =  WT.CONTAINERID
	--INNER JOIN WHSLOADLINE wll ON wll.SHIPMENTID = WT.SHIPMENTID
	WHERE	WT.WORKSTATUS		< 2	AND -- Open, In progress
			WT.WORKTRANSTYPE	= 2 AND -- Sales orders
			WT.WORKTEMPLATECODE LIKE '4010%' AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' AND
			--ISNULL( CNTTbl.SHIPCARRIERTRACKINGNUM, '' )  = '' AND -- Avoiding code errors, around 11/15/2021
			--WT.WORKTEMPLATECODE IN ( '4010 ADirect', '4010 AutoBagger Mult', '4010 AutoBagger Sing', '4010 Bander', '4010 CartonShip Mult', '4010 CartonShip Sing', '4010 Direct', '4010 Gift Card Only' ) AND
			WT.CREATEDDATETIME > ( GETDATE() - 20 )
			--AND CT.CLUSTERID = 'CL001134674'
	GROUP BY CT.CLUSTERID, CT.CLUSTERPROFILEID, CT.CREATEDBY, CT.CREATEDDATETIME--, CAST( WT.CREATEDDATETIME AS DATE ), CAST( wll.CREATEDDATETIME AS DATE )
) AS PendCL -- Total pending works by cluster
INNER JOIN
(
	SELECT CL.CLUSTERID, COUNT( CL.WORKID ) AS WkCount
	FROM WHSWORKCLUSTERLINE CL
	LEFT JOIN WHSWORKTABLE WT ON CL.WORKID = WT.WORKID
		WHERE WT.WORKTRANSTYPE = 2 AND 
	WT.WORKTEMPLATECODE LIKE '4010%' AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' AND WT.CREATEDDATETIME > ( GETDATE() - 20 )
	--AND CL.CLUSTERID = 'CL001134674'
	--WT.WORKTEMPLATECODE NOT LIKE 'W%' AND WT.WORKTEMPLATECODE NOT IN ('4010 Wholesale', 'Saks' ) AND WT.CREATEDDATETIME > ( GETDATE() - 20 )
	GROUP BY CL.CLUSTERID
) AS WKC -- Total work count by cluster
ON PendCL.ClusterID = WKC.CLUSTERID
--GROUP BY PendCL.CLUSTERID,  PendCL.CreatedBy, PendCL.CreatedOn,  PendCL.CLUSTERPROFILEID
ORDER BY PendCL.CLUSTERID
