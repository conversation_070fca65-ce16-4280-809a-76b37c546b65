
SELECT [Style] 

   ,[ColorId] 

   ,[SizeId] 

   ,[Offer] 

   ,[SKU] 

   ,[ItemName] 

   ,[ColorName] 

   ,[Division] 

   ,[Department] 

   ,[Class] 

   ,[Exec View] AS ExecView 

   ,[Key Category View] AS KeyCategoryView 

   ,[Season] 

   ,[Sub_Season] 

   ,[Season_Code] 

   ,[Life_cycle] 

   ,[Flow_Date] 

   ,[Start_Date] 

   ,[End_Date] 

   ,[Vendor] 

   ,[Gender] 

   ,[Color_Group] 

   ,[Family_Match] 

   ,[Collection] 

   ,[Theme] 

   ,[Royalty_Code] 

   ,[Product_Detail] 

   ,[Pack_Size] 

   ,[Silhouette] 

   ,[Licensor] 

   ,[Licensed_Property] 

   ,[GOTS_Cert_Date] 

   ,[GRS_Cert_Date] 

   ,[create_date] 

   ,[LastModified] 

FROM (SELECT * 

    ,ROW_NUMBER() OVER(PARTITION BY SKU ORDER BY create_date desc) AS row_num 

 FROM [TrilliumProd].[rpt].[Product_Dimensions_Hierarchy_Attributes]) r 

 WHERE row_num = 1 

12. Generate counts for Products source file 

@concat( 

  'SELECT ''',  

  pipeline().DataFactory, 

  ''' AS data_factory_name,''', 

  pipeline().Pipeline,  

  ''' AS pipeline_name,''',  

  variables('folder_name'), 

  ''' AS pipeline_timestamp,', 

  '''product'' AS export_filename,', 

  activity('generate-products-file').output.rowsCopied, 

  ' AS export_rowcount' 