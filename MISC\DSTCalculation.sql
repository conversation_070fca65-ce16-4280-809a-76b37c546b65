
DECLARE @MyUTCTime DATETIME = GETUTCDATE() 
--DECLARE @MyUTCTime DATETIME = '11/1/2023 09:00:05 AM'

SELECT
  @MyUTCTime As UTCTime
  , CASE  WHEN DATEPART( mm, @MyUTCTime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, @MyUTCTime ) -- Daylight Savings Months 
        WHEN DATEPART( mm, @MyUTCTime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, @MyUTCTime ) -- No DST
        WHEN DATEPART( mm, @MyUTCTime ) = 3 
        THEN CASE   --WHEN DATEPART( dd, @MyUTCTime ) < 8 THEN DATEADD( hh, - 5, @MyUTCTime ) -- No DST
                    --WHEN DATEPART( dd, @MyUTCTime ) > 14 THEN  DATEADD( hh, - 4, @MyUTCTime ) -- DST
                    WHEN DATEPART( dd, @MyUTCTime ) - DATEPART( w, @MyUTCTime ) + 1  >= 8 THEN  DATEADD(hh, - 4, @MyUTCTime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, @MyUTCTime )
             END
        WHEN DATEPART( dd, @MyUTCTime ) - DATEPART( w, @MyUTCTime ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, @MyUTCTime )
        ELSE DATEADD( hh, - 4, @MyUTCTime )
    END AS MyMethod
    
    , CASE WHEN @MyUTCTime < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, @MyUTCTime ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
		DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, @MyUTCTime ) AS nvarchar(4)) AS datetime) ) ) 
		THEN    DATEADD(hh, - 5, @MyUTCTime ) -- Before DST starts
		ELSE 
		CASE    WHEN @MyUTCTime < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, @MyUTCTime ) AS nvarchar(4)) AS datetime ) + 1 -
		DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, @MyUTCTime ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, @MyUTCTime ) -- Before DST ends
				ELSE dateadd(hh, - 5, @MyUTCTime ) -- DST ended
		END 
	END AS StackOvflMethod
  -- The following conversion matchs after 6am on the time zone change day
  , CAST( @MyUTCTime AT TIME Zone 'UTC' AT Time Zone 'Eastern Standard Time' AS DATETIME ) AS SQL2016Method
  --,  CONVERT_TIMEZONE('UTC', 'America/New_York', utc_date) AS edt_date
  ,  -- ChatGPT generated. Needs correction 
  DATEADD(MINUTE, 
  CASE
        WHEN DATEPART(YEAR, @MyUTCTime) >= 2007 AND DATEPART(YEAR, @MyUTCTime) <= 2038 -- Year range for current daylight savings rules
            THEN 
                CASE
                    WHEN @MyUTCTime >= DATEADD(MINUTE, -240, CAST(DATEPART(YEAR, @MyUTCTime) AS VARCHAR) + '-03-02T07:00:00')
                        AND @MyUTCTime < DATEADD(MINUTE, -240, CAST(DATEPART(YEAR, @MyUTCTime) AS VARCHAR) + '-11-01T06:00:00')
                        THEN -240 -- EDT offset during daylight savings time
                    ELSE -300 -- EST offset outside daylight savings time
                END
        ELSE
            CASE
                WHEN @MyUTCTime >= DATEADD(MINUTE, -240, CAST(DATEPART(YEAR, @MyUTCTime) AS VARCHAR) + '-04-01T02:00:00')
                    AND @MyUTCTime < DATEADD(MINUTE, -240, CAST(DATEPART(YEAR, @MyUTCTime) AS VARCHAR) + '-10-31T02:00:00')
                    THEN -240 -- EDT offset during daylight savings time
                ELSE -300 -- EST offset outside daylight savings time
            END
    END, @MyUTCTime)   AS ChatGPTMethod_01
    , SYSDATETIMEOFFSET() AT TIME ZONE 'Eastern Standard Time' AS ChatGPTMethod_02

--Select * from sys.time_zone_info where is_currently_dst=0

