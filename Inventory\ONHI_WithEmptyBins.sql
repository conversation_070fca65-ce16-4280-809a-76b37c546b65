-- On hand Inventory - simplified version 5/14/2021
-- Picking D added, 3/3/2022

SELECT  
    wmslocation.zoneid			          AS [ZoneId]
    --, wmslocation.HAFORWARDPICKZONE   AS [FwZone]
    ,	wmslocation.locprofileid	      AS [LocProfile]
    ,	OH.itemid					              AS [Item]
    ,	OH.inventcolorid			          AS [Color]
    ,	OH.inventsizeid				          AS [Size]
    ,	wmslocation.wmslocationid	      AS [Location]
    --, WMSLOCATION.SORTCODE            AS [SortCode]
    ,	CAST(OH.physicalinvent AS INT)	AS [Qty]
    ,	CAST(OH.reservphysical AS INT)  AS [Reserved ]
FROM wmslocation
LEFT OUTER JOIN 
( 
  SELECT
    idim.WMSLOCATIONID
    --, loc.ZONEID
    , isum.itemid
    , idim.inventcolorid
    , idim.inventsizeid
    , isum.physicalinvent
    , isum.reservphysical
  FROM inventsum isum
  INNER JOIN  inventdim idim ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = idim.DATAAREAID AND isum.PARTITION = idim.[PARTITION]
  --INNER JOIN WMSLOCATION loc ON idim.wmslocationid = loc.WMSLOCATIONID AND idim.dataareaid = loc.DATAAREAID AND idim.PARTITION = loc.[PARTITION]
  WHERE 
    idim.inventlocationid = '4010' 
    AND isum.physicalinvent > 0 
    AND idim.DATAAREAID = 'ha'-- KY DC only
    --AND ( loc.LOCPROFILEID = 'Picking' OR loc.LOCPROFILEID = 'Picking A' OR loc.LOCPROFILEID = 'Picking D' OR loc.LOCPROFILEID = 'PalletPicking' OR loc.LOCPROFILEID = 'Bulk' )
    
    --AND loc.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', /*'Offsite',*/'Bulk') 
    --AND loc.zoneid NOT IN ( 'Current Pick') -- Exclude GCs, 3333s

  ) as OH -- Pulls location with inventory. Avoiding duplicate records.
ON wmslocation.wmslocationID = OH.wmslocationid 
WHERE 
  wmslocation.inventlocationid = '4010' 
  AND wmslocation.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', /*'W001','Offsite',*/'Bulk') 
	AND wmslocation.zoneid NOT IN ( 'Current Pick') -- Exclude GCs, 3333s
ORDER BY 
  wmslocation.SORTCODE, wmslocation.wmslocationid

/*


Excel

SELECT  
    wmslocation.zoneid as ZoneId
    , wmslocation.HAForwardPickZone AS FwdZone
    , wmslocation.locprofileid as LocProfile
    , OH.itemid as Item
    , OH.inventcolorid as Color
    , OH.inventsizeid as Size
    , wmslocation.wmslocationid as Location
    , OH.physicalinvent as Qty
    , OH.reservphysical AS Reserved 
FROM wmslocation
LEFT OUTER JOIN 
( SELECT inventsum.itemid, inventsum.physicalinvent, inventsum.reservphysical, inventdim.inventcolorid, inventdim.inventsizeid, inventdim.wmslocationid
  FROM inventsum
  LEFT OUTER JOIN inventdim
  ON inventsum.inventdimid = inventdim.inventdimid
  WHERE inventsum.physicalinvent > 0 AND inventdim.inventlocationid = '4010'
) as OH
ON wmslocation.wmslocationID = OH.wmslocationid
WHERE wmslocation.inventlocationid = '4010' AND wmslocation.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', 'W001', 'Bulk') AND wmslocation.zoneid NOT IN ( 'Current Pick')
ORDER BY wmslocation.sortcode, wmslocation.wmslocationid


*/

--Testing
/*

  SELECT  * 
  FROM
    wmslocation loc
  WHERE
    loc.INVENTLOCATIONID = '4010'
    AND loc.wmslocationid = '4010'
    AND loc.LOCPROFILEID LIKE '%Picking%'

SELECT 
  idim.wmslocationid  AS Location
  , idim.inventstatusid
  , isum.POSTEDQTY
  , isum.PostedValue
  , isum.ItemID       AS Item
  , idim.inventcolorid  AS Color
  , idim.inventsizeid AS Size
  , isum.physicalinvent
  , idim.MODIFIEDDATETIME
  --, *
  , idim.MODIFIEDBY

FROM inventsum isum
INNER JOIN inventdim idim ON isum.inventdimid = idim.inventdimid
WHERE
  idim.wmslocationid = '17-085X'
  AND idim.inventsiteid = 'HA USA'
  AND idim.InventLocationid = '4010'

*/

