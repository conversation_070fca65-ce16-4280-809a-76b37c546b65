/*
Order Transactions
Sam's request 11/3/2023

<PERSON>, 1/18/2023

With the order data, we would like to have a line-level representation of the daily orders. So the Date field would be the date that the order was released to the warehouse 
or the date the order was picked, whatever you have access to. For the rest of the data, we only need the Order ID, SKU, and Quantity picked for that order line to get started 
with the analysis, but any additional fields you have would be helpful to add more depth to the analysis. An Item Master with dimensions and weights for each SKU would be helpful 
for us to accurately size the robotic system, as well, if you're able to provide one.  
*/
SELECT
    ll.CREATEDDATETIME /*AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'*/  AS [UTC_ReleasedToWH]
    --, wktbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYPickDate]
    
    -- The container could be modified way later that the order is picked
    --, cnttbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYShipDate]
    , wktbl.WORKCLOSEDUTCDATETIME /*AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' */ AS [UTC_Picked]
    , wktbl.OrderNum            AS [SalesId]
    , wktbl.ContainerID         AS [ContainerId]  
    , CASE 
        WHEN isnull(idim.inventcolorid, '') = '' THEN wkln.itemid
        WHEN isnull(idim.inventsizeid, '') = '' THEN wkln.itemid + '-' + idim.inventcolorid
        ELSE wkln.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid    
    END                                                                 AS [SKU]  
    , CONVERT( DECIMAL( 10, 2 ), physd.[WEIGHT],    0)  AS 'WeightInPounds'
    , CONVERT( DECIMAL( 10, 2 ), physd.[DEPTH],     0 ) AS 'DepthInInches'
    , CONVERT( DECIMAL( 10, 2 ), physd.[WIDTH],     0 ) AS 'WidthInInches'
    , CONVERT( DECIMAL( 10, 2 ), physd.[HEIGHT],    0 ) AS 'HeightInInches'
    , wkln.WMSLOCATIONID             AS [Location]
    , CAST(wkln.QTYWORK AS INT)     AS [Qty]
   -- , wkln.UNITID                   AS [Unit]
FROM
    [DAX_Archive].[arc].WHSWORKLINE wkln 
    INNER JOIN [DAX_Archive].[arc].WHSWORKTABLE wktbl       ON wktbl.WORKID         = wkln.WORKID       AND wktbl.DATAAREAID    = 'ha'  AND wktbl.[PARTITION]   = 5637144576
    INNER JOIN [DAX_Archive].[arc].INVENTDIM idim           ON idim.INVENTDIMID     = wkln.INVENTDIMID  AND idim.DATAAREAID     = 'ha'  AND idim.[PARTITION]    = wkln.[PARTITION]
    INNER JOIN [DAX_Archive].[arc].WHSCONTAINERTABLE cnttbl ON cnttbl.CONTAINERID   = wktbl.CONTAINERID AND cnttbl.DATAAREAID   = 'ha'  AND cnttbl.[PARTITION]  = wktbl.[PARTITION]
    LEFT JOIN  [DAX_PROD].[dbo].WHSPHYSDIMUOM physd         ON physd.ITEMID         = wkln.ITEMID       AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID
                AND physd.DATAAREAID = wkln.DATAAREAID AND physd.[PARTITION] = wkln.[PARTITION]
    INNER JOIN [DAX_Archive].[arc].WHSLOADLINE ll           ON ll.LOADID            = wkln.LOADID       AND ll.DATAAREAID       = 'ha'  AND ll.[PARTITION]      = wkln.[PARTITION]
                                --  AND ll.ITEMID = wkln.ITEMID AND ll.INVENTDIMID = wkln.INVENTDIMID 
                                  AND wkln.INVENTTRANSID = ll.INVENTTRANSID
                                --    AND wkln.LOADLINEREFRECID = ll.RECID
WHERE
    wkln.WORKCLASSID = 'DirectPick'
    AND wkln.WORKSTATUS = 4  -- Closed line
    AND wktbl.WORKSTATUS = 4 -- Closed header
    AND wkln.WorkType = 1 -- Pick lines only
    AND ISNULL(cnttbl.SHIPCARRIERTRACKINGNUM, '') <> '' -- Shipped
    AND wkln.ITEMID NOT IN ( '3333', '30991')
    AND ll.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 04:59:59 AM'
/*    
UNION

SELECT
    wktbl.OrderNum          AS [SalesId]
    , wktbl.ContainerID     AS [ContainerID]    
    , wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID      AS [SKU]
    , ll.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYCreatedDate]
    , wktbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYWorkCreatedDate]
    , cnttbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYShipDate]
    , CAST(wkln.QTYWORK AS INT)     AS [Qty]
    , wkln.UNITID                   AS [Unit]
FROM
    [DAX_PROD].[dbo].WHSWORKLINE wkln 
    JOIN [DAX_PROD].[dbo].WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION]
    JOIN [DAX_PROD].[dbo].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
    JOIN [DAX_PROD].[dbo].WHSCONTAINERTABLE cnttbl ON cnttbl.CONTAINERID = wktbl.CONTAINERID AND cnttbl.DATAAREAID = wktbl.DATAAREAID AND cnttbl.[PARTITION] = wktbl.[PARTITION]
    JOIN [DAX_PROD].[dbo].WHSLOADLINE ll  ON ll.LOADID =wkln.LOADID AND ll.SHIPMENTID = wkln.SHIPMENTID AND ll.DATAAREAID = wkln.DATAAREAID AND ll.[PARTITION] = wkln.[PARTITION]
                AND ll.ITEMID = wkln.ITEMID AND ll.INVENTDIMID = wkln.INVENTDIMID
WHERE
    wkln.WORKCLASSID = 'DirectPick'
    AND wkln.WORKSTATUS = 4  -- Closed
    AND wktbl.WORKSTATUS = 4 -- Closed
    AND wkln.WorkType = 1 -- Pick lines only
    AND ISNULL(cnttbl.SHIPCARRIERTRACKINGNUM, '') <> '' -- Shipped
    AND wkln.ITEMID NOT IN ( '3333', '30991')
    AND cnttbl.MODIFIEDDATETIME > '1/10/2024'
ORDER BY
    SalesId, ContainerID
*/
--GROUP BY
--    wktbl.OrderNum, wktbl.ContainerID
ORDER BY
    UTC_ReleasedToWH, SalesId, ContainerID
/*
SELECT TOP 15 *
FROM 
    WHSWORKLINE wkln
    LEFT JOIN WHSLOADLINE ll ON ll.INVENTTRANSID = wkln.INVENTTRANSID AND ll.DATAAREAID = wkln.DATAAREAID AND ll.[PARTITION] = wkln.[PARTITION]
WHERE 
     wkln.WORKID = 'WK0030607962'
     AND wkln.WORKTYPE = 1
     AND wkln.WORKCLASSID = 'DirectPick'
ORDER BY
    ll.RECID

SELECT TOP 20 *
FROM
    WHSWORKTABLE
WHERE
    --WORKID = 'WK0030607962'
    DATEDIFF(ss, MODIFIEDDATETIME, WORKCLOSEDUTCDATETIME) > 1
    AND WORKTRANSTYPE = 2
    AND WORKSTATUS = 4
*/

SELECT TOP 10 *
FROM WHSPHYSDIMUOM
--sp_columns whsloadline
--sp_columns whsworkline
--sp_columns whsworktable