
-- ********** - Amazon load 

USE DAX_PROD

SELECT 
    wkln.WMSLOCATIONID                                  AS Location,
    wkln.ITEMID                                         AS Item,
    idim.INVENTCOLORID                                  AS Color,
    idim.INVENTSIZEID                                   AS Size,
    CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )     AS Qty,
    wktbl.TAR<PERSON>TLICENSEPLATEID                          AS LicensePlate
FROM WHSWORKLINE wkln
INNER JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wkln.[PARTITION] = wktbl.[PARTITION] AND wktbl.DATAAREAID = wkln.DATAAREAID
INNER JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.[PARTITION] = wkln.[PARTITION]
WHERE 
    wktbl.LOADID = '**********'
    AND wkln.WORKTYPE = 2
ORDER BY wkln.WMSLOCATIONID

/*
SELECT *
FROM WHSWORKTABLE wt
WHERE wt.WORKID = 'WK0024394509'


SELECT *
FROM WHSWORKLINE wl
WHERE wl.WORKID = 'WK0024394509'
*/