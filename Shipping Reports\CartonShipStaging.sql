SELECT TOP 20 *
FROM HASHIPCARTONSTAGING
WHERE CARTONID IN( 'CN012458253' )

SELECT TOP 20 *
FROM HASHIPPEDCARTONSTAGING
WHERE 
	CARTONID IN ( 'CN013839680', 'CN013838014', 'CN013837708' ) /*AND
	CAST( MODIFIEDDATETIME AS DATE ) = '05/23/2022' AND
	SHIPPERID IN ( '1temp1', 'bcd' )*/

SELECT *
FROM WHSCONTAINERTABLE
WHERE CONTAINERID = 'cn011841892'

SELECT TOP 20 *
FROM HASHIPPEDCARTONSTAGING
WHERE CARTONID = 'CN011731328'

--WHERE CARTONID IN( 'CN010886286' , 'CN010900079', 'CN010905466', 'CN010893633' )

-- Reused trackings
select TrackingNumber, CARTONID, whswt.ORDERNUM
from HASHIPPEDCARTONSTAGING hacss  
LEFT JOIN WHSWORKTABLE whswt ON whswt.CONTAINERID = hacss.CARTONID
where (select count(*) from HASHIPPEDCARTONSTAGING i where i.TRACKINGNUMBER = hacss.TRACKINGNUMBER AND i.CREATEDDATETIME > '2021-09-01 04:00:00 am' AND i.TRACKINGNUMBER <> '' ) > 1 -- Duplicated
AND hacss.CREATEDDATETIME >'11/24/2021'













--CN010996078
SELECT  *
FROM HASHIPPEDCARTONSTAGING
WHERE CAST( MODIFIEDDATETIME As DATE ) = '04/21/2022'
	AND SHIPPERID not like 'MHX'
ORDER BY MODIFIEDDATETIME DESC




-- Duplicated tracking #s

select TrackingNumber, CARTONID, whswt.ORDERNUM
from HASHIPPEDCARTONSTAGING hacss  
LEFT JOIN WHSWORKTABLE whswt ON whswt.CONTAINERID = hacss.CARTONID
where (
select count(*) 
from HASHIPPEDCARTONSTAGING i 
where i.TRACKINGNUMBER = hacss.TRACKINGNUMBER 
AND i.CREATEDDATETIME > ( GETUTCDATE() - 95 ) 
AND i.TRACKINGNUMBER <> '' ) > 1 -- Duplicated
AND hacss.CREATEDDATETIME > ( GETUTCDATE() - 10 ) 
 --( GETUTCDATE() - 15 )

 --More than one tracking per container

 select CARTONID,TrackingNumber, whswt.ORDERNUM
from HASHIPPEDCARTONSTAGING hacss  
LEFT JOIN WHSWORKTABLE whswt ON whswt.CONTAINERID = hacss.CARTONID
where (
select count(*) 
from HASHIPPEDCARTONSTAGING i 
where i.CARTONID = hacss.CARTONID 
AND i.CREATEDDATETIME > ( GETUTCDATE() - 10 ) 
AND i.TRACKINGNUMBER <> '' ) > 1 -- Duplicated
AND hacss.CREATEDDATETIME > ( GETUTCDATE() - 10 ) 

SELECT GETUTCDATE() - 10, GETUTCDATE() -60, '2021-09-01 04:00:00 am' 



SELECT TOP 20 *
FROM WHSCONTAINERTABLE
WHERE CONTAINERID IN( 'CN010810256' )


SELECT TOP 200 *
FROM Logistyx_FedEx.dbo.FedEx$Shippers
