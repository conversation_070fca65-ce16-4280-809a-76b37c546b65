
-- <PERSON><PERSON><PERSON><PERSON><PERSON> Gaylords to cancel
-- UTC time

DECLARE @RepTime DATETIME = '12/29/2023 07:00:00 PM'

SELECT
    --wkln.WORKID,
    wkln.ITEMID                     AS [Item]
    , idim.INVENTCOLORID            AS [Color]
    , idim.INVENTSIZEID             AS [Size]
    , wkln.WMSLOCATIONID            AS [Location]
    , CAST(SUM(wkln.QTYWORK)  AS INT)    AS [Qty]
FROM
    WHSWORKLINE wkln
    LEFT JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION]
    LEFT JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.DATAAREAID = wkln.DATAAREAID AND loc.[PARTITION] = wkln.[PARTITION]
    LEFT JOIN INVENTDIM idim ON  wkln.INVENTDIMID = idim.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
WHERE
    wktbl.WORKTEMPLATECODE = 'Forward Replen'
    AND wktbl.CREATEDDATETIME > @RepTime
    AND loc.ZONEID = 'Bulk'
    AND wktbl.INVENTSITEID = 'HA USA'
    AND loc.INVENTLOCATIONID = '4010'
    AND wktbl.WORKSTATUS IN (0,1,2,3,5)
    --AND wkln.LINENUM = 1
GROUP BY
    wkln.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, wkln.WMSLOCATIONID

--sp_columns WHSWORKTABLE