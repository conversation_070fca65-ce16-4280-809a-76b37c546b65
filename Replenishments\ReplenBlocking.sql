
SELECT 
    wklk.REP<PERSON>NWORKID
    , CASE WHEN wkreptbl.FROZEN = 1 THEN 'Blocked' ELSE 'NotBlocked' END AS 'Status'
    , wkreptbl.WHSZONEID
    , wkreptbl.WORKPOOLID
    , wklk.DEMANDWORKID
    --, wkreptbl.WORKTEMPLATECODE
    --, wklk.REPLENLINENUM
    --, wklk.DEMANDLINENUM
    , wkrepln.ITEMID
    , idim.INVENTCOLORID    AS Color
    , idim.INVENTSIZEID     AS Size
    , wksltbl.ORDERNUM
    , wksltbl.SHIPMENTID
    , wksltbl.LOADID
    , CAST( wll.CREATEDDATETIME AS DATE ) AS ReleaseDate
FROM 
    WHSREPLENWORKLINK wklk
    LEFT JOIN WHSWORKTABLE wkreptbl ON wkreptbl.WORKID = wklk.REPLENWORKID      AND wkreptbl.DATAAREAID = wklk.DATAAREAID   AND wkreptbl.[PARTITION] = wklk.[PARTITION]
    LEFT JOIN WHSWORKTABLE wksltbl  ON wksltbl.WORKID = wklk.DEMANDWORKID       AND wksltbl.DATAAREAID = wklk.DATAAREAID    AND wksltbl.[PARTITION] = wklk.[PARTITION]
    LEFT JOIN WHSLOADLINE wll       ON wksltbl.LOADID = wll.LOADID              AND wksltbl.DATAAREAID = wll.DATAAREAID     AND wksltbl.[PARTITION] = wll.[PARTITION]
    LEFT JOIN WHSWORKLINE wkrepln   ON wkrepln.WORKID = wklk.REPLENWORKID       AND wkrepln.DATAAREAID = wklk.DATAAREAID    AND wkrepln.[PARTITION] = wklk.[PARTITION]
    LEFT JOIN INVENTDIM idim        ON idim.INVENTDIMID = wkrepln.INVENTDIMID   AND idim.DATAAREAID = wkrepln.DATAAREAID    AND idim.PARTITION = wkrepln.[PARTITION]
WHERE
    wklk.INVENTQTY > 0 -- Not canceled
    AND wksltbl.WORKTRANSTYPE = 2 -- Sales orders
    AND wksltbl.FROZEN = 1 -- Blocked sales order work
    AND wksltbl.WORKSTATUS < 4
    AND wkreptbl.WORKSTATUS < 4 -- Replenishment not completed
    AND wkrepln.LINENUM = wklk.REPLENLINENUM -- Mostly line 1
    AND wll.ITEMID = wkrepln.ITEMID 
    AND wll.INVENTDIMID = wkrepln.INVENTDIMID -- SKU line
    --AND wklk.REPLENWORKID IN ( 'WK0028159917', '')
ORDER BY
    wll.CREATEDDATETIME

/*
SELECT TOP 20 *
FROM WHSLOADLINE
WHERE   
    LOADID = '**********'
*/