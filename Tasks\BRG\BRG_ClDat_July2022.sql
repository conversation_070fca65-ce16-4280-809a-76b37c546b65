
SELECT 
    cl.C<PERSON><PERSON>TERI<PERSON>
    ,wktbl.<PERSON><PERSON><PERSON><PERSON>
    ,wktbl.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    ,wktbl.WAVE<PERSON>
    ,wktbl.ORDERNUM
    ,wktbl.CONTAINERID
    ,wktbl.HACONTAINERTYPECODE
    ,wktbl.WORKTEMPLATECODE
    --,DATEADD( hh, -4, wktbl.CREATEDDATE<PERSON>ME ) AS CREATEDDATETIME
FROM WHSWORKTABLE wktbl
LEFT JOIN WHSWORKCLUSTERLINE cl ON cl.WORKID = wktbl.WORKID AND cl.DATAAREAID = wktbl.DATAAREAID AND cl.[PARTITION] = wktbl.[PARTITION]
WHERE 
    wktbl.WORKSTATUS < 5
    AND wktbl.WORKTRANSTYPE = 2
    AND wktbl.INVENTLOCATIONID = '4010'
    AND wktbl.DATAAREAID = 'ha'
    AND wktbl.CREATEDDATETIME BETWEEN '07/01/2022 04:00:00 AM' AND '8/1/2022 03:59:59 AM'
    AND ISNULL( cl.CLUSTERID, '' ) <> ''
ORDER BY    
    cl.CLUSTERID
