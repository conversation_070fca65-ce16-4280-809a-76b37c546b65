USE DAX_PROD

-- Troubleshooting miss sorts on the conveyor


SELECT TOP 5
	hasctstg.CARTONID
	, DATEADD( hh, -5, wktbl.CREATEDDATETIME )	AS CntCreatedDateTime
	 , hasctstg.SHIPPERID
	 , hasctstg.SHIPMETHOD
	 , hasctstg.CARRIERTYPE
	 , hasctstg.TRACKINGNUMBER
	 , DATEADD( hh, -5, hasctstg.CREATEDDATETIME ) AS StgCreatedDateTime -- EDT
FROM 
	[DAX_PROD].[dbo].[HASHIPPEDCARTONSTAGING] hasctstg
	LEFT JOIN WHSWORKTABLE wktbl ON wktbl.CONTAINERID = hasctstg.CARTONID AND wktbl.DATAAREAID = hasctstg.DATAAREAID AND wktbl.[PARTITION] = hasctstg.[PARTITION]
WHERE 
	ISNULL( SHIPMETHOD, '' ) = ''
	AND ISNULL(CARRIERTYPE, '' ) = ''
	AND hasctstg.TRACKINGNUMBER LIKE '1Z%'
	AND hasctstg.SHIPPERID = 'MHX'
	AND wktbl.WORK<PERSON>MPLATECODE IN ( '4010 Bander', '4010 AutoBagger Sing')
ORDER BY
	wktbl.CREATEDDATETIME ASC


SELECT TOP 5
	hasctstg.CARTONID
	, DATEADD( hh, -5, wktbl.CREATEDDATETIME )	AS CntCreatedDateTime
	 , hasctstg.SHIPPERID
	 , hasctstg.SHIPMETHOD
	 , hasctstg.CARRIERTYPE
	 , hasctstg.TRACKINGNUMBER
	 , DATEADD( hh, -5, hasctstg.CREATEDDATETIME ) AS StgCreatedDateTime -- EDT
FROM 
	[DAX_PROD].[dbo].[HASHIPPEDCARTONSTAGING] hasctstg
	LEFT JOIN WHSWORKTABLE wktbl ON wktbl.CONTAINERID = hasctstg.CARTONID AND wktbl.DATAAREAID = hasctstg.DATAAREAID AND wktbl.[PARTITION] = hasctstg.[PARTITION]
WHERE 
	ISNULL( SHIPMETHOD, '' ) != ''
	AND ISNULL(CARRIERTYPE, '' ) != ''
	AND hasctstg.TRACKINGNUMBER LIKE '1Z%'
	AND hasctstg.SHIPPERID = 'MHX'
	AND wktbl.WORKTEMPLATECODE IN ( '4010 Bander', '4010 AutoBagger Sing')
ORDER BY
	wktbl.CREATEDDATETIME DESC

/*
SELECT 
	 CARTONID
	 , SHIPPERID
	 , SHIPMETHOD
	 , CARRIERTYPE
	 , TRACKINGNUMBER
	 , DATEADD( hh, -5, CREATEDDATETIME ) AS CreatedDateTime -- EDT
FROM 
	[DAX_PROD].[dbo].[HASHIPPEDCARTONSTAGING]
WHERE 
	CARTONID IN ( 'CN013857398', 'CN013851824' , 'CN013846572', 'CN013840777', 'CN013830615' )



CN013843477 -- Ana Suarez

SELECT TOP 20 *
FROM [DAX_PROD].[dbo].[HASHIPCARTONSTAGING]
WHERE 
	CARTONID = 'CN013214427'



*/