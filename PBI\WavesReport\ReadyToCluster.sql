-- Waves report - PBI
-- Ready to cluster
-- 1/7/2025 - Will modify the date to match the SLA date


SELECT	
    CLI.WorkId
    --, CLI.CreatedDate
    , CLI.ReleasedToWH
    --, CLI.WAVEID
    , CLI.[Wave Template]
    , CLI.Frozen
    , WorkType
/*		COUNT( CLI.WORKID ) AS PendWkCount, -- Segregating Work's ready by work template
    SUM( CASE WHEN FROZEN = 0 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 1 ELSE 0 END 
              END  
            ) AS AB_Ready, 
        SUM( CASE WHEN FROZEN = 0 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 0 ELSE 1 END 
              END  
            ) AS Direct_Ready,     
		SUM( CASE WHEN FROZEN = 1 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 1 ELSE 0 END 
              END  
            ) AS AB_Blocked, 
        SUM( CASE WHEN FROZEN = 1 THEN 
                CASE WHEN CNT_Type = 'AB' THEN 0 ELSE 1 END 
              END  
            ) AS Direct_Blocked
*/
FROM (    
SELECT 
CAST(LL.createddatetime AS DATE)    AS [ReleasedToWH]
--, CAST( wt.CreatedDateTime AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE )  AS CreatedDate
    /*
  CASE  WHEN DATEPART( mm, wt.CreatedDateTime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wt.CreatedDateTime ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wt.CreatedDateTime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wt.CreatedDateTime ) -- No DST
        WHEN DATEPART( mm, wt.CreatedDateTime ) = 3 
        THEN CASE   WHEN DATEPART( dd, wt.CreatedDateTime ) < 8 OR DATEPART( dd, wt.CreatedDateTime ) > 14 THEN  DATEADD( hh, - 5, wt.CreatedDateTime ) -- No DST
                    WHEN DATEPART( dd, wt.CreatedDateTime ) - DATEPART( w, wt.CreatedDateTime ) + 1  >= 8 THEN  DATEADD(hh, - 4, wt.CreatedDateTime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wt.CreatedDateTime )
             END
        WHEN DATEPART( dd, wt.CreatedDateTime ) - DATEPART( w, wt.CreatedDateTime ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wt.CreatedDateTime )
        ELSE DATEADD( hh, - 4, wt.CreatedDateTime )
END  */ 
--, WT.WAVEID

, WVT.WAVETEMPLATENAME AS [Wave Template]
, WT.WORKID     AS [WorkId]
, WT.FROZEN,
CASE WHEN WT.WORKTEMPLATECODE IN ( '4010 AutoBagger Sing', '4010 Bander', '4010 AutoBagger Mult' ) THEN 'AB' ELSE 'Direct' END AS WorkType
FROM 
    WHSWORKTABLE WT
LEFT JOIN WHSWORKCLUSTERLINE CL ON CL.WORKID = WT.WORKID    AND CL.DATAAREAID = 'ha' AND CL.PARTITION = WT.PARTITION
INNER JOIN WHSWAVETABLE WVT     ON WVT.WAVEID  = WT.WAVEID  AND WT.DATAAREAID = 'ha' AND WVT.PARTITION = WT.PARTITION
INNER JOIN WHSLOADLINE ll       ON ll.LOADID = WT.LOADID    AND WT.DATAAREAID = 'ha' AND WT.PARTITION   = LL.PARTITION
WHERE WT.WORKSTATUS = 0																			AND -- Open
                                WT.WORKTRANSTYPE    = 2											AND -- Sales Order
                                WT.WORKTEMPLATECODE NOT LIKE 'W%'								AND -- Excluding Wholesale
                                WT.WORKTEMPLATECODE NOT IN ('4010 Wholesale', 'Saks' )       	AND -- More exclusions
                                WT.CREATEDDATETIME > ( GETDATE() - 15 )                         AND -- Last two weeks
                                CL.CLUSTERID IS NULL                                                -- Not clustered
GROUP BY 
    WT.WORKID, ll.CREATEDDATETIME, WT.WORKTEMPLATECODE, WVT.WAVETEMPLATENAME, WT.CREATEDDATETIME,  FROZEN
) AS CLI
--GROUP BY WAVEID, TEMPLATE, CreatedDate, Frozen, CNT_Type
ORDER BY
    ReleasedToWH, WorkId

--sp_columns whsloadline
--sp_columns whsworkline