

-- Stored Procedures
/*
SELECT object_definition(object_id) AS [Proc Definition]
FROM sys.objects 
WHERE type='P' and name= 'sp_running'

*/

--exec sp_helptext 'sp_running'

--SELECT OBJECT_DEFINITION (OBJECT_ID(N'sp_running'))
/*
SELECT definition 
FROM sys.sql_modules 
WHERE object_id = OBJECT_ID('dbo.sp_running')
*/
USE [DAX_PROD] --Database Name
SELECT
    sch.name+'.'+ob.name AS       [Object], 
    ob.create_date, 
    ob.modify_date, 
    ob.type_desc, 
    mod.definition
FROM 
     sys.objects AS ob
     LEFT JOIN sys.schemas AS sch ON
            sch.schema_id = ob.schema_id
     LEFT JOIN sys.sql_modules AS mod ON
            mod.object_id = ob.object_id
WHERE mod.definition IS NOT NULL --Selects only objects with the definition (code)