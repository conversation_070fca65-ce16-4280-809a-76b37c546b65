SELECT wt.<PERSON><PERSON><PERSON><PERSON>, wt.<PERSON>AR<PERSON><PERSON><PERSON>ENSEPLATEID, wt.<PERSON><PERSON><PERSON><PERSON><PERSON>, wt.<PERSON><PERSON><PERSON><PERSON>, wt.<PERSON><PERSON><PERSON><PERSON><PERSON>, wt.<PERSON><PERSON><PERSON>, wt.<PERSON><PERSON><PERSON><PERSON><PERSON>LATECODE, wt.<PERSON><PERSON><PERSON><PERSON>DB<PERSON>, wt.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ME
FROM WHSWORKTABLE wt 
WHERE 
    wt.CONTAINERID =  '' 
    AND wt.WORKTRANSTYPE = 2 
    AND wt.MODIFIEDDATETIME >'9/20/2022'
    AND wt.SHIPMENTID = ''
    AND wt.ORDERNUM = ''

SELECT TOP 20 *
FROM INVENTTRANS

SELECT TOP 20 *
FROM INVENTTRANSORIGIN invto
LEFT JOIN WHSWORKLINE wkl ON wkl.INVENTTRANSID = invto.INVENTTRANSID
WHERE wkl.WORKID IN( 'WK0021467220', 'WK0021469230', 'WK0021471590' )
--WHERE CAST( MODIFIEDDATETIME AS DATE ) = '11-13-2021'

