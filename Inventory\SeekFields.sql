
USE DAX_PROD

SELECT 
    TOP 20 *
FROM
    Catalog
WHERE   
    HASUBSEASONCODE LIKE '%FWH22%'

SELECT 
    TOP 20 *
FROM
    HASUBSEASONTABLE
WHERE   
    HASUBSEASONCODE LIKE '%FWH22%'

SELECT 
    TOP 20 *
FROM
    RETAILSEASONTABLE
WHERE   
    SEASONCODE LIKE '%22%'

SELECT 
    TOP 20 *
FROM
    SUNTAFSEASONCODEMASTER

WHERE   
    SEASONCODE LIKE '%22%'

SELECT 
    TOP 20 *
FROM
  ECORESCATEGORY
WHERE 
    1 = 1 
    AND.RecID = 5637146836
    --AND P<PERSON>NFL<PERSON>ORSETID LIKE 'FWH23'
  --AND STYLEID LIKE '8%'

SELECT 
    TOP 20 *
FROM
  HARMSPRODUCT
WHERE
    ITEMSKU LIKE '8%'

SELECT 
    TOP 20 *
FROM
  ECORESPRODUCT
WHERE
    DISPLAYPRODUCTNUMBER LIKE '80026-VN4%'
./*


SELECT
    TOP 200*
FROM    
    HAINVENTDE<PERSON>ILREPORTBATCHTMP
WHERE   
   ISNULL(SEASONCODEs, '' ) <> ''
   AND SE<PERSON><PERSON><PERSON>DE<PERSON> LIKE '%22%'
   AND INVENTLOCATIONID = '4010'
   AND SKU = '30070-U74-M'    
*/




/*
SELECT 
    TOP 20 *
FROM
    HAPLANFLOORSETTABLE
WHERE   
    FLOORSETID LIKE '%FWH22%'
*/