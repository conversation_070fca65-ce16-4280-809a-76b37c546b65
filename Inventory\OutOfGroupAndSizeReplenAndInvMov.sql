
-- Trynig to determine who is moving items out of zone( Group and size)
-- Replenishments and inventory movements moving items out of it's forecasted slot  

WITH PutTrans AS (
SELECT --TOP 20 
    wkln.WORKID
    , wktbl.WORKBUILDID                 AS [WorkCreation]
    , wktbl.CREA<PERSON><PERSON><PERSON><PERSON>TIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'   AS [CreatedAt]
    , CASE 
        WHEN ISNULL(wktbl.WORKTEMPLATECODE, '') <> '' 
            THEN wktbl.WORKTEMPLATECODE ELSE 'Inventory Movement' 
    END AS [WorkTemplate]
    , CASE 
        WHEN wktbl.WORKTRANSTYPE = 11 THEN 'Replenishment'
        WHEN wktbl.WORKTRANSTYPE = 1 THEN 'Purchase Orders'
        WHEN wktbl.WORKTRANSTYPE = 7 THEN 'Inventory Movement'
        ELSE 'Unknown'
    END AS [MovmtType]
    , CASE WHEN ISNULL(wkln.WorkClassID, '') <> '' 
        THEN  wkln.WorkClassID ELSE 'Movement'                 
    END AS [WorkClass]
    , wkln.WMSLOCATIONID                AS [Location]
    --, CAST(wkln.QTYWORK AS INT)         AS [QtyWk]
    , wkln.USERID
    /*
    , RIGHT( '0' + LTRIM(loc.AISLEID),2)       AS [Aisle_Table]
    , CASE 
        WHEN LEFT(wkln.WMSLOCATIONID,2) <> '17' 
            THEN LEFT(wkln.WMSLOCATIONID, 2 )
            ELSE
                CASE WHEN RIGHT(wkln.WMSLOCATIONID,1) < 'J' -- A-D, F-I
                    THEN '17A' ELSE '17Z'
                END
      END    AS [AisleId]
    , RIGHT( wkln.WMSLOCATIONID, 1 )    AS [Bin]
    , wkln.ITEMID                       AS [Item]  
    , idim.INVENTCOLORID                AS [Color]  
    , idim.INVENTSIZEID                 AS [Size]
    */
    , wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID AS [SKU]
    , CAST(wkln.QTYWORK AS INT)         AS [Qty]
    , loc.ZONEID                        AS [LocZoneId]
    , CASE WHEN fc.SLOTTIERVALUE  IS NOT NULL THEN fc.SLOTTIERVALUE ELSE 'N/A' END  AS [FcSlotTier]
   -- , COUNT(*)  OVER(PARTITION BY loc.AISLEID, loc.ZONEID) AS [AisleZonePicks]
    --, COUNT(*) OVER(PARTITION BY loc.AISLEID) AS [AislePicks]
    --, loc.LEVEL_
    --, wkln.WORKCLASSID
    --
FROM
    WHSWORKLINE wkln
    INNER JOIN WHSWORKTABLE wktbl ON wkln.WORKID = wktbl.WORKID AND wktbl.DATAAREAID = N'ha' AND wktbl.[PARTITION] = wkln.[PARTITION] AND wkln.WORKTYPE = 2 -- Put
    INNER JOIN WMSLOCATION loc ON loc.wmslocationid = wkln.wmslocationid AND loc.DATAAREAID = N'ha' AND loc.PARTITION = 5637144576 AND loc.INVENTLOCATIONID = '4010' 
        AND loc.LOCPROFILEID IN ( 'Picking', 'Picking A', 'PalletPicking' ) AND loc.ZONEID NOT IN ('Current Pick')  -- No GCs 
        AND (wkln.WORKCLASSID IN ('Fwd Throw', 'FwdThrow') OR wktbl.WORKTRANSTYPE = 7)
    INNER JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
    LEFT JOIN HAFORECASTREPLENISHMENTTABLE fc  ON fc.ITEM = wkln.ITEMID AND idim.INVENTCOLORID =fc.COLOR AND idim.INVENTSIZEID = fc.SIZE_ AND idim.DATAAREAID = fc.DATAAREAID
WHERE
    wkln.WORKSTATUS = 4 -- Closed    
    --AND wkln.ITEMID IS NOT NULL
    --AND wktbl.CREATEDDATETIME > '07/17/2024 20:00:00' -- Work creation history ON after 
    AND wktbl.CREATEDDATETIME > GETUTCDATE() - 5  
    --AND wktbl.INVENTSITEID = 'HA USA'
    AND wktbl.INVENTLOCATIONID = '4010'
)
, PutD2 AS
(
    SELECT 
    /*
        WORKID
    --, loc.AISLEID
        , [Location]
        , [AisleId]
        , [Bin]
        , [Item]
        , [Qty]
        , [LocZoneId]*/
        *
        , CASE WHEN LEFT(LocZoneId, 3) = LEFT(FcSlotTier, 3) THEN 'Yes' Else 'No' END AS [GroupMatch]
        , CASE WHEN LEFT(LocZoneId, 4) = LEFT(FcSlotTier, 4) THEN 'Yes' Else 'No' END AS [GroupSizeMatch]
        
    FROM
        PutTrans
)
SELECT 
   WORKID
   , WorkCreation
   , CreatedAt
   , WorkTemplate
   , MovmtType
   , WorkClass
   , UserID
   , SKU
   , [Location]
   , Qty
   , LocZoneId
   , FcSlotTier
    , GroupMatch
    , GroupSizeMatch

FROM 
    PutD2
WHERE
    1 = 1
    AND GroupMatch = 'No'
    --AND GroupSizeMatch = 'No'
ORDER BY
    WORKID

