
-- The most likely items that are preventing the load to move from "In Packing" to "Loaded"

DECLARE @TransferOrder VARCHAR(20) = 'TO-00075006'


SELECT 
    --TOL.TransferId          AS [Transfer]
    TOL.LineNum                         AS TransferOrderLineNum
    , TOL.ITEMID                        AS [Item]
    , idim.INVENTCOLORID                AS [Color]
    , idim.INVENTSIZEID                 AS [Size]
    , CAST(TOL.QTYTRANSFER AS INTEGER)  AS [TransferQty]
    , CAST(ll.QTY AS INTEGER)           AS [LoadQty]
    , CAST(ll.PICKEDQTY AS INTEGER)     AS [LoadPickedQty]
    --, CAST(ll.WORKCREATEDQTY AS INTEGER)AS [LoadWorkCreatedQty]
    , LL.LoadId
    --, itorig.*
    /*
    , tol.INVENTDIMID           AS [TO_DimId]
    , ll.INVENTDIMID            AS [LL_DimId]
    , itransto.INVENTDIMID      AS [Trans_DimId]
    , itorig.ITEMINVENTDIMID    AS [TransOrig_DimId]
    */
FROM 
    InventTransferLine TOL
    JOIN WHSLoadLine LL ON TOL.TransferId = LL.ORDERNUM AND ll.DATAAREAID = 'ha' AND ll.[PARTITION] = tol.[PARTITION]
                        AND TOL.INVENTTRANSID = LL.INVENTTRANSID
                        --AND TOL.ITEMID = LL.ITEMID AND TOL.INVENTDIMID = LL.INVENTDIMID 
    --JOIN INVENTTRANSFERTABLE totbl ON totbl.TRANSFERID = tol.TRANSFERID AND totbl.DATAAREAID = 'ha' AND totbl.[PARTITION] = tol.[PARTITION]
    JOIN InventTransOrigin itorig ON itorig.INVENTTRANSID = ll.INVENTTRANSID AND itorig.DATAAREAID = 'ha' AND itorig.[PARTITION] = ll.[PARTITION]
    --JOIN INVENTTRANS itransto ON /*itransto.ITEMID = TOL.ITEMID AND */itransto.INVENTTRANSORIGIN = itorig.RECID AND itransto.DATAAREAID = 'ha'
    JOIN INVENTDIM idim ON idim.INVENTDIMID = tol.INVENTDIMID AND idim.DATAAREAID = tol.DATAAREAID AND idim.[PARTITION] = tol.[PARTITION]
WHERE 
    TOL.TRANSFERID = @TransferOrder
    --AND TOL.QTYTRANSFER != ll.QTY
    AND ll.PICKEDQTY != ll.QTY

    --AND TOL.Warehouse != LL.Warehouse
    --OR TOL.Location != LL.Location
    --OR TOL.BatchId != LL.BatchId
    --OR TOL.SerialId != LL.SerialId;


ORDER BY
    ITEM, Color, Size

--SELECT * FROM WHSLOADTABLE WHERE LOADID = 'LD16162294'