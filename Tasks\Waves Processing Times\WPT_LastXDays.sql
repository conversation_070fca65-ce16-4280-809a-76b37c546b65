USE DAX_PROD;
DECLARE @ReportDays INT = 10;

WITH wpt  AS ( -- Wave processing times
SELECT 
	wt.WAVEID							AS Wave
	, wt.EXECUTINGUTCDATETIME	AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'	AS ProcessingDateTime
	/*
	CASE WHEN wt.EXECUTINGUTCDATETIME < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, wt.EXECUTINGUTCDATETIME ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
			DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, wt.EXECUTINGUTCDATETIME ) AS nvarchar(4)) AS datetime) ) ) 
			THEN    DATEADD(hh, - 5, wt.EXECUTINGUTCDATETIME ) 
			ELSE 
				CASE    WHEN wt.EXECUTINGUTCDATETIME < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, wt.EXECUTINGUTCDATETIME ) AS nvarchar(4)) AS datetime ) + 1 -
					DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, wt.EXECUTINGUTCDATETIME ) AS nvarchar(4)) AS datetime))) 
					THEN dateadd(hh, - 4, wt.EXECUTINGUTCDATETIME ) 
					ELSE dateadd(hh, - 5, wt.EXECUTINGUTCDATETIME ) 
				END 
	END
	AS ProcessingDateTime*/
	, COUNT( DISTINCT wl.shipmentid ) 	AS Shipments
	, COUNT(  wl.LOADID )					AS LoadLines
	, CONVERT( DECIMAL(10,0)
	, SUM( ll.qty )	)					AS Units
	, DATEDIFF( MINUTE, wt.EXECUTINGUTCDATETIME, wt.HELDUTCDATETIME ) AS ProcTimeMinutes
FROM WHSWAVETABLE wt
LEFT JOIN WHSWAVELINE wl ON wl.WAVEID = wt.WAVEID
LEFT JOIN WHSLOADLINE LL ON ll.SHIPMENTID = wl.SHIPMENTID-- AND ll.ORDERNUM = wl.ORDERNUM( slower, no index on ordernum ) 
WHERE
	wt.EXECUTINGUTCDATETIME BETWEEN GETUTCDATE() - @ReportDays AND GETUTCDATE()
	AND wt.WAVESTATUS > 1
	AND wt.WAVETEMPLATENAME = 'REGULAR DIRECT'
	AND wt.[DESCRIPTION] NOT LIKE 'Replenishment%'
GROUP BY wt.WAVEID, wt.EXECUTINGUTCDATETIME , wt.HELDUTCDATETIME
--ORDER BY wt.EXECUTINGUTCDATETIME
)
 SELECT
	CAST( wpt.ProcessingDateTime AS DATE)					AS ProcDate
	, AVG( wpt.Shipments )									AS AvgShipments
	, AVG( wpt.LoadLines )									AS AvgLoadLines
	, SUM( wpt.LoadLines )									AS TotalLoadLines
	, CAST( AVG( wpt.Units ) AS DECIMAL(10, 2))				AS AvgUnits
	, AVG( wpt.ProcTimeMinutes )							AS AvgMinutes
	, CONVERT( DECIMAL(10,0), AVG( wpt.LoadLines )/ AVG( wpt.ProcTimeMinutes ))	AS LLPerMin
FROM
	wpt
GROUP BY
	CAST( wpt.ProcessingDateTime AS DATE)