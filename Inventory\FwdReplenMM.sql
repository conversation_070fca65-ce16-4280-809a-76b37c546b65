
-- 12/02/2024
-- Calculating if a Manual move is needed based off the replenishment forecast table
-- Useful if Forward replen can't be ran(high volume)

WITH fwdreplen AS(
    SELECT 
        Item + '-' + Color + '-' + SIZE_ AS 'SKU'
        , CAST(HAFORECASTSTARTDATE AS DATE)          AS ForecastDate
        , LEFT( SLOTTIERVALUE, 3)       AS 'Category'
        , SLOTTIERVALUE                 As 'FcZone'
        ,  HAFORECASTDAY1 + HAFORECASTDAY2 + HAFORECASTDAY3 + HAFORECASTDAY4 + HAFORECASTDAY5 + HAFORECASTDAY6 + HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 +  HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14  AS 'TotalForecast'
        , CASE 
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 0 THEN 
                HAFORECASTDAY1 + HAFORECASTDAY2 + HAFORECASTDAY3 + HAFORECASTDAY4 + HAFORECASTDAY5 + HAFORECASTDAY6 + HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 1 THEN 
                HAFORECASTDAY2 + HAFORECASTDAY3 + HAFORECASTDAY4 + HAFORECASTDAY5 + HAFORECASTDAY6 + HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 2 THEN 
                HAFORECASTDAY3 + HAFORECASTDAY4 + HAFORECASTDAY5 + HAFORECASTDAY6 + HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 3 THEN 
                HAFORECASTDAY4 + HAFORECASTDAY5 + HAFORECASTDAY6 + HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 4 THEN 
                HAFORECASTDAY5 + HAFORECASTDAY6 + HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 5 THEN 
                HAFORECASTDAY6 + HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 6 THEN 
                HAFORECASTDAY7 + HAFORECASTDAY8 + HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 7 THEN 
                HAFORECASTDAY8 + HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 8 THEN 
                HAFORECASTDAY9 + 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 9 THEN 
                HAFORECASTDAY10 + HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 10 THEN 
                HAFORECASTDAY11 + HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 11 THEN 
                HAFORECASTDAY12 + HAFORECASTDAY13 + HAFORECASTDAY14
            WHEN DATEDIFF(day, HAFORECASTSTARTDATE, GETUTCDATE()) = 12 THEN 
                HAFORECASTDAY13 + HAFORECASTDAY14
            ELSE HAFORECASTDAY14 END
        AS DemandForecast
    FROM
        HAFORECASTREPLENISHMENTTABLE
    WHERE
        HAFORECASTSTARTDATE > GETUTCDATE() - 14 -- Recent forecast
),
Invent AS
(
    SELECT
        fwdreplen.*
        , CAST(SUM( CASE WHEN loc.LOCPROFILEID IN ('Bulk', 'Overflow') THEN 0 ELSE isum.PHYSICALINVENT END ) AS INT) AS 'PickingInvent'
        , CAST(SUM( CASE WHEN loc.LOCPROFILEID IN ('Bulk', 'Overflow') THEN isum.PHYSICALINVENT ELSE 0 END ) AS INT) AS 'BulkInvent'
    FROM
        INVENTSUM isum
        JOIN INVENTDIM idim ON isum.INVENTDIMID = idim.INVENTDIMID AND isum.DATAAREAID = 'ha' AND isum.[PARTITION] = idim.[PARTITION]
        JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = idim.WMSLOCATIONID AND idim.DATAAREAID = 'ha' AND idim.[PARTITION] = loc.[PARTITION]
        JOIN fwdreplen ON SKU = isum.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
    WHERE
        TotalForecast > 0 -- Only items with quantities forecasted
        AND isum.PHYSICALINVENT > 0 -- Locations with inventory
        AND loc.LOCPROFILEID IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', 'Bulk', 'Overflow') -- Picking & OS
    GROUP BY    
        SKU, ForecastDate, Category,FcZone, TotalForecast, DemandForecast
),
InventMM AS
(
    SELECT
        Invent.*
        , CASE WHEN DemandForecast > PickingInvent THEN 
            CASE WHEN BulkInvent > 0 THEN 'Yes' ELSE 'No' END 
        ELSE 'No' END AS 'ManualMove'
    FROM
        Invent
)
SELECT *
FROM
    InventMM
ORDER BY
    Category, SKU

