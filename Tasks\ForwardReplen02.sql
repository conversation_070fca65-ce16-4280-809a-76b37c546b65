
-- If you run the whole query will only show duplicated location work
-- Run the first part to get all the forward replenishments

SELECT
    FwdRepWk.CreatedDate, FwdRepWk.WORKID, FwdRepWk.[Location]
FROM    
(

SELECT
    CAST(
    CASE WHEN wktbl.CREATEDDATETIME < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, wktbl.CREATEDDATETIME ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
		DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, wktbl.CREATEDDATETIME ) AS nvarchar(4)) AS datetime) ) ) 
		THEN    DATEADD(hh, - 5, wktbl.CREATEDDATETIME ) 
		ELSE 
		CASE    WHEN wktbl.CREATEDDATETIME < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, wktbl.CREATEDDATETIME ) AS nvarchar(4)) AS datetime ) + 1 - DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, wktbl.CREATEDDATETIME ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, wktbl.CREATEDDATETIME ) 
				ELSE dateadd(hh, - 5, wktbl.CREATEDDATETIME ) 
		END 
    END
    AS DATE ) AS CreatedDate -- Conversion to KY Time
    , wktbl.WORKID
    , wkln.WMSLOCATIONID    AS 'Location'
    , wkln.ITEMID           AS 'Item'
    , idim.INVENTCOLORID    AS 'Color'
    , idim.INVENTSIZEID     AS 'Size'
    , CONVERT( DECIMAL(10, 0), wkln.INVENTQTYWORK )    AS 'Qty'
FROM
    WHSWORKLINE wkln
    LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
    LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
WHERE
    --wktbl.CREATEDDATETIME BETWEEN '11/15/2022 05:00:00' AND '11/16/2022 04:59:59' -- Play with this. Time in UTC. EDT is UTC-4, EST is UTC-5
    wktbl.WORKTEMPLATECODE = 'Forward Replen'
    AND wkln.WORKTYPE = 2 -- Put
    AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
    AND loc.locprofileid LIKE '%Picking%' -- Only Picking locations
    AND wktbl.WORKSTATUS < 5 -- Not cancelled
    AND wktbl.WORKBUILDID IN ( 'WC002457100', 'WC002457210')

) AS FwdRepWk
INNER JOIN
(
SELECT
   wkln.WMSLOCATIONID    AS 'Location'
    , COUNT(*)              AS 'Count'
FROM
    WHSWORKLINE wkln
    LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
WHERE
    wktbl.CREATEDDATETIME BETWEEN '10/31/2022 04:00:00' AND '11/4/2022 03:59:59' -- Play with this. Time in UTC. EDT is UTC-4, EST is UTC-5
    AND wktbl.WORKTEMPLATECODE = 'Forward Replen'
    AND wkln.WORKTYPE = 2 -- Put
    AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
    AND loc.locprofileid LIKE '%Picking%' -- Only Picking locations
    AND wktbl.WORKSTATUS < 5 -- Not cancelled
GROUP BY 
     wkln.WMSLOCATIONID
HAVING 
    COUNT( * ) > 1
) AS DupLoc
ON FwdRepWk.[Location] = DupLoc.[Location]
GROUP BY 
    FwdRepWk.[Location], FwdRepWk.WORKID, FwdRepWk.CreatedDate