
-- Power BI Ship Report - seven days

DECLARE @ReportDate AS DATE 	= CAST( GETDATE() AS DATE ) -- Portland's time is what matters
DECLARE @Days 		AS INTEGER 	= 7

SELECT
	Container
	, KYShippedDate
	, ShipDate
	, Shift
	, OrderNum
	, Qty
FROM
(

	SELECT 
		CNTInfo.Container
		, CNTInfo.KYShippedDate
		,
			CASE	WHEN DATEPART( hh, CNTInfo.KYShippedDate ) < 6    OR  DATEPART( hh, CNTInfo.KYShippedDate ) >= 17	THEN '2nd' ELSE 
			CASE	WHEN DATEPART( hh, CNTInfo.KYShippedDate ) >=  6  AND DATEPART( hh, CNTInfo.KYShippedDate ) < 16	THEN '1st' ELSE
			CASE	WHEN DATEPART( minute, CNTInfo.KYShippedDate ) <=  30 THEN '1st' ELSE '2nd' END
			END END AS Shift
		,
			CASE	WHEN DATEPART( hh,CNTInfo.KYShippedDate ) < 3 THEN CAST( ( CNTInfo.KYShippedDate - 1 ) AS DATE ) -- Before 3 am belongs to the previous day
			ELSE CAST( CNTInfo.KYShippedDate  AS DATE ) END AS ShipDate
		, CNTInfo.OrderNum
		, CNTInfo.Qty
	FROM(
		SELECT  
			CNTs.ContainerID AS Container, 
			CASE WHEN CNTs.modifieddatetime < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, CNTs.modifieddatetime ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
				DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, CNTs.modifieddatetime ) AS nvarchar(4)) AS datetime) ) ) 
			THEN    DATEADD(hh, - 5, CNTs.modifieddatetime ) -- EST
			ELSE 
				CASE WHEN CNTs.modifieddatetime < 
					DATEADD(hh, 2, CAST('11/8/' + CAST(DATEPART(yyyy, CNTs.modifieddatetime ) AS nvarchar(4)) AS datetime )  --8/11/xxxx will be always out of the daylight savings time
					- DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, CNTs.modifieddatetime ) AS nvarchar(4)) AS datetime)))  --Day of the week of the 7th. The result is the day when time changed.
					THEN dateadd(hh, - 4, CNTs.modifieddatetime ) -- EDT
					ELSE dateadd(hh, - 5, CNTs.modifieddatetime )  -- EST
				END 
			END AS KYShippedDate
			, ShipTbl.ordernum                				AS OrderNum
			, SUM( CONVERT( DECIMAL, CNTLine.Qty , 10 ) )   AS Qty
		FROM WHSContainerTable          AS CNTs
		LEFT JOIN WHSShipmentTable      AS ShipTbl	ON CNTs.ShipmentId	= ShipTbl.ShipmentId AND CNTs.[PARTITION] = ShipTbl.[PARTITION] AND CNTS.DATAAREAID = ShipTbl.DATAAREAID
		LEFT JOIN WHSContainerLine      AS CNTLine  ON CNTS.ContainerID = CNTLine.ContainerID AND CNTs.[PARTITION] = CNTLine.[PARTITION] AND CNTs.DATAAREAID = CNTLine.DATAAREAID
		WHERE CNTs.ModifiedDateTime BETWEEN  GETUTCDATE() - @Days AND GETUTCDATE()  -- Will discard 7th day later
		--CAST( ( CONVERT(nvarchar, GETUTCDATE() - 1, 1 ) +' 3:59:59' ) AS DateTime ) AND GETUTCDATE() -- KY is -4, or -5, Redundant data will be discarded later
		AND CNTs.SHIPCARRIERTRACKINGNUM not LIKE '' -- Only containers with tracking numbers
		AND    ShipTbl.ShipmentStatus = 5 --Shipped( Matching with Javier's )
		GROUP BY CNTs.ContainerID, CNTs.modifieddatetime, ShipTbl.ordernum
) AS CNTInfo

) AS ShipDayReport
WHERE
	DATEDIFF( DAY, ShipDate, @ReportDate) <= @Days - 1		-- Trimming extra data
ORDER BY KYShippedDate




/*
SELECT  *
FROM HASHIPPEDCARTONSTAGING
WHERE --CREATEDDATETIME BETWEEN '11/02/2021 22:20:00.000' AND '11/02/2021 22:59:00.000' AND 
CARTONID IN ( 'CN010439318', 'CN010439315', 'CN010404699' )

ORDER BY CREATEDDATETIME

SELECT GETUTCDATE() AS UTCTime, GETDATE() AS ServerTime, DATEADD( hh,-5 , GETUTCDATE() ) AS KYTime
SELECT CAST( ( CONVERT(nvarchar, DATEADD( hh, -4, GETUTCDATE() ) - 8, 1 ) +' 17:00:00' ) AS DateTime )

DECLARE @TestDate AS DateTime = '11/1/2020 1:20:20'

SELECT 
	DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, @TestDate ) AS nvarchar(4)) AS datetime)) AS DayOfTheWk
	, CAST('11/7/' + CAST(DATEPART(yyyy, @TestDate ) AS nvarchar(4)) AS datetime )  AS LastPossibleDate
	, DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, @TestDate ) AS nvarchar(4)) AS datetime ) + 1 - DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, @TestDate ) AS nvarchar(4)) AS datetime))) AS TzChange
	,
	CASE WHEN @TestDate < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, @TestDate ) AS nvarchar(4)) AS datetime ) + 1 - DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, @TestDate ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, @TestDate ) 
				ELSE dateadd(hh, - 5, @TestDate ) 
		END AS KYDate

CASE    WHEN DATEPART( mm, CNTs.modifieddatetime ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, CNTs.modifieddatetime ) -- Daylight Savings Months 
        WHEN DATEPART( mm, CNTs.modifieddatetime ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, CNTs.modifieddatetime ) -- No DST
        WHEN DATEPART( mm, CNTs.modifieddatetime ) = 3 
        THEN CASE   WHEN DATEPART( dd, CNTs.modifieddatetime ) < 8 OR DATEPART( dd, CNTs.modifieddatetime ) > 14 THEN  DATEADD( hh, - 5, CNTs.modifieddatetime ) -- No DST
                    WHEN DATEPART( dd, CNTs.modifieddatetime ) - DATEPART( w, CNTs.modifieddatetime ) + 1  >= 8 THEN  DATEADD(hh, - 4, CNTs.modifieddatetime ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, CNTs.modifieddatetime )
             END
        WHEN DATEPART( dd, CNTs.modifieddatetime ) - DATEPART( w, CNTs.modifieddatetime ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, CNTs.modifieddatetime )
        ELSE DATEADD( hh, - 4, CNTs.modifieddatetime )
END                             AS KYShippedDate, 

*/