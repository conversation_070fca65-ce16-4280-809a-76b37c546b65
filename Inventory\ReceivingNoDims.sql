
-- Troubleshooting items with no dimensions in picking
-- Appears to be related to an issue when new colors are added
-- Looking for items with zero dimensions on pending loads and marked as measured

WITH icb AS(
SELECT 
   icomb.ITEMID
   , idim.INVENTCOLORID AS [Color]
   , idim. INVENTSIZEID AS [Size]
   , idim.INVENTDIMID   AS [icb_InventDimId]
   , icomb.HAMEASUREMENTWORKCREATED
   , icomb.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'  AS [CreatedDateTime]
FROM
    INVENTDIMCOMBINATION icomb
    JOIN INVENTDIM idim ON idim.INVENTDIMID = icomb.INVENTDIMID AND icomb.DATAAREAID = 'ha' AND icomb.[PARTITION] = idim.[PARTITION]
),
itl AS(
SELECT 
    uoms.SHIPMENTID                     AS [ShipmentId]
    , uoms.LOADID                       AS [LoadId]
    , uoms.HAPURCHASEORDER              AS [PO]
    , uoms.LICENSEPLATEID               AS [LicensePlate]
    , icb.icb_InventDimId
    , uoms.ITEMID                       AS [ItemId]
    , idim.INVENTCOLORID                AS [Color]
    , idim.INVENTSIZEID                 AS [Size]
    --, asni.INVENTDIMID
    , CAST(uoms.QTY AS INT)             AS [Qty]
    --, CAST(asni.WORKCREATEDQTY AS INT)  AS [WkQty]
    --, uoms.CREATEDWORKID                AS [CREATEDWORKID]
   -- , icb.HAMEASUREMENTWORKCREATED
   , icb.CreatedDateTime                AS [ItemCombCreatedDT]
FROM 
    WHSUOMSTRUCTURE uoms
    JOIN WHSASNITEM asni 
        ON uoms.LICENSEPLATEID = asni.LICENSEPLATEID AND uoms.SHIPMENTID = asni.SHIPMENTID AND uoms.[MODULE] = asni.[MODULE] AND uoms.DATAAREAID = 'ha' AND uoms.[PARTITION] = **********
    JOIN INVENTDIM idim ON idim.INVENTDIMID = asni.INVENTDIMID AND idim.DATAAREAID = 'ha' AND idim.[PARTITION] = asni.[PARTITION]
    JOIN WHSPHYSDIMUOM physd    ON physd.ITEMID = uoms.ITEMID AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID
        AND idim.DATAAREAID = 'ha'  AND idim.[PARTITION] = physd.[PARTITION]
    JOIN icb ON icb.ITEMID = uoms.ITEMID AND icb.Color = idim.INVENTCOLORID AND icb.[Size] = idim.INVENTSIZEID
WHERE
    physd.HEIGHT = 0 -- No dimensions
    AND icb.HAMEASUREMENTWORKCREATED =  1  -- Marked as measured
    AND uoms.CREATEDWORKID IS NULL   -- Not received
    --AND ISNULL(uoms.CREATEDWORKID, '')  = ''  -- Not received
    --AND uoms.LOADID IN ('**********', '**********')
)
SELECT 
    * 
FROM itl
WHERE
    1 = 1 
    --AND LoadId IN ('**********', '**********', 'LD15466879', 'LD15467550', 'LD15479416')
/*
UPDATE idc
SET idc.HAMEASUREMENTWORKCREATED = 0
FROM INVENTDIMCOMBINATION as idc
INNER JOIN itl ON idc.ITEMID = itl.ItemId AND idc.INVENTDIMID = itl.icb_InventDimId
*/


/*
SELECT 
   icomb.ITEMID
   , idim.INVENTCOLORID AS [Color]
   , idim. INVENTSIZEID AS [Size]
   --, idim.INVENTDIMID
   , icomb.HAMEASUREMENTWORKCREATED
   , icomb.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'  AS [CreatedDateTime]
FROM
    INVENTDIMCOMBINATION icomb
    JOIN INVENTDIM idim ON idim.INVENTDIMID = icomb.INVENTDIMID AND icomb.DATAAREAID = 'ha' AND icomb.[PARTITION] = idim.[PARTITION]
WHERE
    ITEMID = '81737'
    --AND INVENTDIMID = '0443049293'
    --HAMEASUREMENTWORKCREATED = 0

*/