-- Sign ON and OFf by Operator

SELECT TOP (1000) [operatorId]
      ,[version]
      ,[password]
      ,[signOnTime]
      ,[signOffTime]
      ,[lastLocationId]
      ,[currentRegionId]
      ,[assignedRegionId]
      ,[workgroupId]
      ,[taskFunctionId]
      ,[status]
      ,[createdDate]
FROM 
  [VoiceLink].[dbo].[core_operators]
WHERE
    signOnTime > GETDATE()  - 1
    AND signOffTime IS NULL -- Signed ON