/*
Checking for weight discrepancies on Containers. Possible duplicated clusters.
*/


USE DAX_PROD
/*
SELECT
    DISTINCT wkclt.CLUSTERID
    , wkcln.WORKID
    , DATEADD( hh, -4, wktbl.MODIFIEDDATETIME ) AS ModifiedDateTime
    , wktbl.ORDERNUM
    , wktbl.CONTAINERID
FROM 
    [DAX_PROD].[dbo].[WHSWORKCLUSTERTABLE] wkclt
    LEFT JOIN [DAX_PROD].[dbo].[WHSWORKCLUSTERLINE] wkcln ON wkclt.CLUSTERID = wkcln.CLUSTERID AND wkclt.[PARTITION] = wkcln.[PARTITION] AND wkclt.DATAAREAID = 'ha'
    LEFT JOIN [DAX_PROD].[dbo].[WHSWORKTABLE] wktbl ON wktbl.WORKID = wkcln.WORKID AND wktbl.[PARTITION] = wkcln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
WHERE   
    wktbl.ORDERNUM IN ( '********', '********', '********', '********', '********', '********', '********', '********' )
    AND wktbl.WORKSTATUS = 4 --Closed work

-- Identify all orders affected 
*/
SELECT 
    CLInfo.ClusterID
    , CLInfo.ORDERNUM
    , CLInfo.CUSTACCOUNT
    , CLInfo.SALESNAME
    , CLInfo.CONTAINERID
    , CLInfo.ExpectedWeight
    , CLInfo.ActualWeight
    , CLInfo.ShippedDateTime
FROM
(
SELECT
      wkcln.CLUSTERID
    , wktbl.ORDERNUM
    , st.CUSTACCOUNT
    , st.SALESNAME
    , wktbl.CONTAINERID
    , CONVERT( DECIMAL( 4, 2), cnttbl.WEIGHT ) AS ExpectedWeight
    , CONVERT( DECIMAL( 4, 2), cnttbl.HAWHSAJILLUSWEIGHT ) AS ActualWeight
    , DATEADD( hh, -4, wktbl.MODIFIEDDATETIME) AS PickedDateTime
    , DATENAME( WEEKDAY, DATEADD(hh,-4, wktbl.MODIFIEDDATETIME) ) AS 'PickedDay'
    , DATEADD( hh, -4, cnttbl.MODIFIEDDATETIME) AS ShippedDateTime
FROM
    WHSWORKTABLE wktbl
    LEFT JOIN WHSWORKCLUSTERLINE wkcln ON wktbl.WORKID = wkcln.WORKID AND wktbl.[PARTITION] = wkcln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN SALESTABLE st ON wktbl.ORDERNUM = st.SALESID AND wktbl.[PARTITION] = st.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN WHSCONTAINERTABLE cnttbl ON wktbl.CONTAINERID = cnttbl.CONTAINERID AND wktbl.[PARTITION] = cnttbl.[PARTITION] AND wktbl.DATAAREAID = 'ha'
WHERE
    wktbl.WORKSTATUS = 4
    --AND wktbl.CREATEDDATETIME BETWEEN '9/10/2022' AND '9/30/2022'
    --AND cnttbl.HAWHSAJILLUSWEIGHT / cnttbl.WEIGHT > 1.5
    AND wkcln.CLUSTERID IN ('CL001147010', 'CL001148270', 'CL001148014', 'CL001148092', 'CL001148093', 'CL001149347')
) AS CLInfo
WHERE
    1 = 1
    --AND CLInfo.ActualWeight / CLInfo.ExpectedWeight > 1.5
ORDER BY    
    CLInfo.CLUSTERID

--
/*
SELECT
    TOP  2 *
FROM WHSSHIPMENTTABLE

SELECT
    TOP  2 *
FROM WHSContainerTABLE
*/

