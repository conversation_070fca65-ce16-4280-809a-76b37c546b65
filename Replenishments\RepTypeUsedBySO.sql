
-- When a sales order line is(was) created and it depends on a replenishment, What kind of replenishment is?

SELECT 
    Link.ReplenWorkID           AS [RepWorkID]
    /*
    , CASE 
        WHEN wtr.FROZEN = 1 
            THEN 'Yes' 
            ELSE 'No' END       AS [Blocked]
    , wtr.WORKPRIORITY
    , CASE 
        WHEN wtr.WORKSTATUS = 0 
            THEN 'Open' 
        WHEN wtr.WORKSTATUS = 1 
            THEN 'In Process' 
        WHEN wtr.WORKSTATUS = 4
            THEN 'Closed'
    END                         AS [Status]
    , wtr.LOCKEDUSER            AS [LockedBy]
    , wlr.USERID                AS [PulledBy]
    , CONVERT( VARCHAR(10), CAST(wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS TIME), 0) AS [PulledAt]
    --, CONVERT(VARCHAR(8), wlr.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 108) AS [TimePulled2]
*/
    , wlr.WMSLOCATIONID         AS [FromLoc]
    , wld.WMSLOCATIONID         AS [ToLoc]
    , wld.ITEMID                AS [Item]
    , idim.INVENTCOLORID        AS [Color]
    , idim.INVENTSIZEID         AS [Size]
    , CAST(wlr.QTYWORK AS INT)  AS [Qty]
    , RIGHT(wlr.LOCATEDLPID, 5) AS [LP]
    , CASE WHEN wtr.WORKTEMPLATECODE = 'Forward Replen' THEN 'MinMax' ELSE 'Demand' END AS [RepType]
FROM 
    WHSREPLENWORKLINK link
    JOIN WHSWORKLINE wld        ON wld.workid = link.DEMANDWORKID       AND wld.LINENUM = link.DEMANDLINENUM	AND wld.DATAAREAID = link.DATAAREAID AND wld.PARTITION = link.PARTITION
    JOIN INVENTDIM idim         ON idim.INVENTDIMID = wld.INVENTDIMID   AND idim.DATAAREAID = wld.DATAAREAID AND idim.[PARTITION] = wld.[PARTITION]
    JOIN WHSWORKTABLE wtd       ON link.DEMANDWORKID = wtd.WORKID	    AND link.DATAAREAID	= wtd.DATAAREAID AND link.PARTITION	= wtd.PARTITION
    JOIN WHSWAVETABLE wavetd    ON wtd.WAVEID = wavetd.WAVEID           AND wtd.DATAAREAID	= wavetd.DATAAREAID	AND wtd.PARTITION	= wavetd.PARTITION
	JOIN WHSWORKLINE wlr        ON wlr.workid = link.REPLENWORKID       AND wlr.LINENUM = link.REPLENLINENUM AND wlr.DATAAREAID = link.DATAAREAID AND wlr.PARTITION = link.PARTITION 
    JOIN WHSWORKTABLE wtr       ON link.REPLENWORKID = wtr.WORKID       AND link.DATAAREAID	= wtr.DATAAREAID AND link.PARTITION	= wtr.PARTITION
	--JOIN WHSWAVETABLE wavetr ON wtr.WAVEID = wavetr.WAVEID         AND wtr.DATAAREAID = wavetr.DATAAREAID      AND wtr.PARTITION	= wavetr.PARTITION
WHERE 
    wtr.WORKSTATUS < 5 -- Not Canceled
	AND wtr.CREATEDDATETIME > GETUTCDATE() - 15