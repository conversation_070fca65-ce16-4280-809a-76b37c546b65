SELECT TOP 100 st.<PERSON><PERSON><PERSON><PERSON><PERSON>,ct.<PERSON>NT<PERSON>IN<PERSON><PERSON>, ct.<PERSON><PERSON><PERSON><PERSON><PERSON>HIPPERID, ct.MODIFIEDBY, ct.MOD<PERSON>IEDDATETI<PERSON>, ct.SHIPCAR<PERSON>ERTRACKINGNUM, ct.<PERSON><PERSON>ER<PERSON><PERSON>KINGNUM
FROM WHSCONTAINERTABLE ct
LEFT JOIN WHSSHIPMENTTABLE st ON ct.SHIPMENTID = st.SHIPMENTID
LEFT JOIN SALESTABLE slt ON st.ORDERNUM = slt.SALESID
WHERE /*ct.MODIFIEDBY IN ( 'svc-vite', 'MHX' ) AND */st.SHIPMENTSTATUS = 5 AND ct.MODIFIEDDATETIME > '1/28/2021' AND slt.SALESSTATUS < 3 AND
		ISNULL( ct.SHIPCARRIERTRACKINGNUM, '') = '' AND ISNULL( ct.MASTERTRACKINGNUM, '' ) = ''

SELECT st.ORDERNUM, 
	CASE	WHEN slt.SALESSTATUS = 1 THEN 'Open'
			WHEN slt.SALESSTATUS = 2 THEN 'Delivered'
			WHEN slt.SALESSTATUS = 3 THEN 'Invoiced'
			ELSE 'Canceled' END AS OrderStatus, 
	CASE 	WHEN st.SHIPMENTSTATUS = 4 THEN 'Loaded' ELSE 'Shipped' END AS ShipmentStatus, 
ct.CONTAINERID, CONVERT(  DECIMAL, sum( cl.QTY ), 4 ) AS Qty, ct.HAWHSSHIPPERID, ct.MODIFIEDBY, DATEADD( hh,-4, ct.MODIFIEDDATETIME) AS KYModDateAndTime, ct.SHIPCARRIERTRACKINGNUM, ct.MASTERTRACKINGNUM
FROM WHSCONTAINERTABLE ct
LEFT JOIN WHSSHIPMENTTABLE st ON ct.SHIPMENTID = st.SHIPMENTID
LEFT JOIN SALESTABLE slt ON st.ORDERNUM = slt.SALESID
LEFT JOIN WHSCONTAINERLINE cl ON ct.CONTAINERID = cl.CONTAINERID
WHERE ( ct.MODIFIEDBY = 'svc-vite' OR ISNULL( ct.HAWHSShipperID, '' ) = '' ) AND st.SHIPMENTSTATUS IN ( 4, 5 ) AND ct.MODIFIEDDATETIME BETWEEN '10/1/2021' AND DATEADD( minute, -240, GETUTCDATE() ) AND slt.SALESSTATUS < 4  AND
		ISNULL( ct.SHIPCARRIERTRACKINGNUM, '') = '' AND ISNULL( ct.MASTERTRACKINGNUM, '' ) = '' AND st.HAQUERYAMAZON = 0
GROUP BY st.ORDERNUM, slt.SALESSTATUS, st.SHIPMENTSTATUS, ct.CONTAINERID, ct.HAWHSSHIPPERID, ct.MODIFIEDBY, ct.MODIFIEDDATETIME, ct.SHIPCARRIERTRACKINGNUM, ct.MASTERTRACKINGNUM
ORDER BY ct.MODIFIEDDATETIME