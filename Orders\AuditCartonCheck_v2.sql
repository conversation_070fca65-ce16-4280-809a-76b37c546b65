--AuditCartonCheck_v2.sql
-- This query checks for work orders that have all specified UPCs in the WHSWORKLINE table.

-- Drop the temp table if it exists
--IF OBJECT_ID('tempdb..#UPCs') IS NOT NULL DROP TABLE #UPCs;

CREATE TABLE #UPCs (UPC nvarchar(20));
INSERT INTO #UPCs (UPC) VALUES
    --('196705311173'),
    ('196705459417'), --67377-2KD-130
    ('196705459493'), --67377-2KE-130
    ('196705223506'); --67377-1DH-130

WITH WkUPCs AS
(
    SELECT
        wkln.WORKID,
        CASE WHEN wktbl.WORKSTATUS = 0 THEN 'Open' ELSE 'InProcess' END AS [WorkStatus],
        haig.GLOBALTRADEITEMNUMBER AS [UPC]
    FROM 
        WHSWORKTABLE wktbl
        INNER JOIN WHSWORKLINE wkln ON wkln.WORKID = wktbl.WORKID AND wktbl.DATAAREAID = 'ha' AND wktbl.[PARTITION] = wkln.[PARTITION]
        INNER JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.DATAAREAID = 'ha'
        INNER JOIN INVENTDIM id ON id.INVENTDIMID = wkln.INVENTDIMID AND wkln.DATAAREAID = 'ha' AND wkln.[PARTITION] = id.[PARTITION]
        INNER JOIN HAINVENTITEMGTIN haig ON wkln.ITEMID = haig.ITEMID AND id.INVENTCOLORID = haig.INVENTCOLORID AND id.INVENTSIZEID = haig.INVENTSIZEID 
            AND haig.[PARTITION] = wkln.[PARTITION] AND haig.DATAAREAID = 'ha'
    WHERE
        wktbl.INVENTLOCATIONID = '4010'
        AND haig.GLOBALTRADEITEMNUMBER IN (SELECT UPC FROM #UPCs)
        AND wktbl.WORKSTATUS IN (0, 1)
        AND wktbl.WORKTRANSTYPE = 2
        AND wkln.WORKTYPE = 1
        AND loc.LOCPROFILEID IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking')
)
SELECT 
    WORKID,
    WorkStatus
FROM
    WkUPCs
GROUP BY WORKID, WorkStatus
HAVING COUNT(DISTINCT UPC) = (SELECT COUNT(*) FROM #UPCs)
ORDER BY WORKID;
-- Clean up the temp table
IF OBJECT_ID('tempdb..#UPCs') IS NOT NULL DROP TABLE #UPCs;