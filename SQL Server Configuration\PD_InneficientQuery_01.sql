-- Inneficient query #1


(@P1 bigint,@P2 nvarchar(5),@P3 int,@P4 numeric(32,16),@P5 numeric(32,16),@P6 bigint,@P7 nvarchar(5),@P8 nvarchar(26))

SELECT TOP 1
    T1.RECID
FROM INVENTSUM T1
WHERE (((T1.PARTITION=@P1) AND (T1.DATAAREAID=@P2)) AND ((T1.CLOSED=@P3) AND ((T1.PHYSICALINVENT<>@P4) OR (T1.PICKED<>@P5)))) AND EXISTS (SELECT TOP 1
        'x'
    FROM INVENTDIM T2
    WHERE (((T2.PARTITION=@P6) AND (T2.DATAAREAID=@P7)) AND ((T1.INVENTDIMID=T2.INVENTDIMID) AND (T2.LICENSEPLATEID=@P8))))

-- Query rewritten

SELECT TOP 1 T1.RECID
FROM INVENTSUM T1
INNER JOIN INVENTDIM T2
    ON T1.INVENTDIMID = T2.INVENTDIMID
    AND T1.PARTITION = T2.PARTITION
    AND T1.DATAAREAID = T2.DATAAREAID
WHERE
    T1.PARTITION = @P1 AND
    T1.DATAAREAID = @P2 AND
    T1.CLOSED = @P3 AND
    (T1.PHYSICALINVENT <> @P4 OR T1.PICKED <> @P5) AND
    T2.LICENSEPLATEID = @P8
