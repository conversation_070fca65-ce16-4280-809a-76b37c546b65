
DECLARE @StartDate DATE = '02/01/2023'

SELECT
    rtgctrans.CARDNUMBER
    , rtgctrans.MCRSALESID
    --, st.SALESTYPE
    , rtgctrans.TRANSDATE
    , rtgctrans.MCRINVENTTRANSID
    , rtgctrans.OPERATION
    , rtgctrans.CHANNEL
    , rtgctrans.TRANSACTIONID
FROM 
    RETAILGI<PERSON><PERSON><PERSON>TRANSACTIONS rtgctrans
INNER JOIN
    ( SELECT CARDNUMBER, COUNT( * )  AS Count FROM RETAILGIFTCARDTRANSACTIONS WHERE TRANSDATE > @StartDate GROUP BY CARDNUMBER HAVING COUNT( * ) > 1 ) dup ON dup.CARDNUMBER = rtgctrans.CARDNUMBER    
--INNER JOIN SALESTABLE st ON st.SALESID = rtgctrans.MCRSALESID AND st.DATAAREAID = rtgctrans.DATAAREAID AND st.[PARTITION] = rtgctrans.[PARTITION]
--INNER JOIN SALESLINE sl ON sl.SALESID = st.SALESID AND st.DATAAREAID = sl.DATAAREAID AND st.[PARTITION] = sl.[PARTITION]
INNER JOIN SALESLINE sl ON sl.INVENTTRANSID = rtgctrans.MCRINVENTTRANSID AND rtgctrans.DATAAREAID = sl.DATAAREAID AND rtgctrans.[PARTITION] = sl.[PARTITION] AND sl.ITEMID IN ( '30991', '46229')
WHERE
    --rtgctrans.CARDNUMBER = '****************'
    rtgctrans.TRANSDATE > @StartDate
    --AND sl.ITEMID IN ( '30991', '46229')
    MCRSALESID = '44991575'
ORDER BY
    rtgctrans.CARDNUMBER, rtgctrans.MCRSALESID
