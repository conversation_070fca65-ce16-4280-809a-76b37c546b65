
/*
SELECT 'PendingVoicePicks' AS QueueType, COUNT(*) AS PendingCount
FROM HAVOICEINTEGRATIONQUEUEPICKS
WHERE STATUS = 0
UNION ALL
SELECT 'PendingVoicePuts' AS QueueType, COUNT(*) AS PendingCount
FROM HAVOICEINTEGRATIONQUEUEPUTS
WHERE STATUS = 0;
*/
 
 -- Orders pending export from Voice after the voice integration(10/2024)

SELECT
    (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPUTS WHERE STATUS = 0 -- Pending
    ) AS OrdersPendingExport
    , (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPICKS WHERE STATUS = 0) AS PendingVoicePicks;

WITH PendingOrders AS (
SELECT
    SUM(CASE WHEN wktbl.WORKTEMPLATECODE = '4010 Direct' THEN 1 ELSE 0 END) AS PendingDirect,
    SUM(CASE WHEN wktbl.WORKTEMPLATECODE != '4010 Direct' THEN 1 ELSE 0 END) AS PendingAB
FROM HAVOICEINTEGRATIONQUEUEPUTS vputs
JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = vputs.WORKID AND vputs.DATAAREAID = 'ha' AND wktbl.[PARTITION] = vputs.[PARTITION]
WHERE vputs.STATUS = 0
)
SELECT
    PendingDirect,
    PendingAB,
    (SELECT COUNT(*) FROM HAVOICEINTEGRATIONQUEUEPICKS WHERE STATUS = 0) AS PendingVoicePicks
FROM PendingOrders;
