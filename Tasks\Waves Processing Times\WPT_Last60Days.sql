

--Waves Processing times / by day
-- 

DECLARE @ReportDays INT = 60;

SELECT 
	WPT.ProcessingDate, 
    AVG( WPT.ProcTimeMinutes )                      AS AVGProcMinutes,
    AVG( WPT.Shipments )                            AS AVGShipments, 
    AVG( WPT.LoadLines )                            AS AVGLoadLines, 
    CONVERT( Decimal( 10, 0 ), AVG( WPT.Units ) )   AS AVGUnits, 
    AVG( WVR.Replen )                               AS AVGReplenCartons
	
FROM
(
SELECT 
	wt.WAVEID							AS Wave, 
    CAST
    (
	CASE WHEN wt.EXECUTINGUTCDATETIME < DATEADD( hh, 2, CAST('3/14/' + CAST( DATEPART( yyyy, wt.EXECUTINGUTCDATETIME ) AS nvarchar( 4 ) ) AS datetime ) + 1 - 
			DATEPART(w, CAST('3/14/' + CAST(DATEPART(yyyy, wt.EXECUTINGUTCDATETIME ) AS nvarchar(4)) AS datetime) ) ) 
			THEN    DATEADD(hh, - 5, wt.EXECUTINGUTCDATETIME ) 
			ELSE 
				CASE    WHEN wt.EXECUTINGUTCDATETIME < DATEADD(hh, 2, CAST('11/7/' + CAST(DATEPART(yyyy, wt.EXECUTINGUTCDATETIME ) AS nvarchar(4)) AS datetime ) + 1 -
					DATEPART(w, CAST('11/7/' + CAST(DATEPART(yyyy, wt.EXECUTINGUTCDATETIME ) AS nvarchar(4)) AS datetime))) 
				THEN dateadd(hh, - 4, wt.EXECUTINGUTCDATETIME ) 
				ELSE dateadd(hh, - 5, wt.EXECUTINGUTCDATETIME ) 
				END 
	END
	AS DATE ) AS ProcessingDate,
    COUNT( DISTINCT wl.shipmentid ) 	AS Shipments,
	COUNT(  wl.LOADID )					AS LoadLines,
	SUM( ll.qty )						AS Units,
    DATEDIFF( MINUTE, wt.EXECUTINGUTCDATETIME, wt.HELDUTCDATETIME ) AS ProcTimeMinutes
FROM WHSWAVETABLE wt
LEFT JOIN WHSWAVELINE wl ON wl.WAVEID = wt.WAVEID
LEFT JOIN WHSLOADLINE LL ON ll.SHIPMENTID = wl.SHIPMENTID-- AND ll.ORDERNUM = wl.ORDERNUM( slower, no index on ordernum ) 
WHERE
	wt.EXECUTINGUTCDATETIME > GETUTCDATE() - @ReportDays
	AND wt.WAVESTATUS > 1
	AND wt.WAVETEMPLATENAME = 'REGULAR DIRECT'
	AND wt.[DESCRIPTION] NOT LIKE 'Replenishment%'
GROUP BY wt.WAVEID, wt.EXECUTINGUTCDATETIME , wt.HELDUTCDATETIME
) AS WPT
LEFT JOIN
(
	SELECT wavtblr.DEMANDWAVEID As Wave, COUNT( wkt.WORKID ) AS Replen
	FROM WHSWAVETABLE wavtblr  -- Replenishments waves
	LEFT JOIN WHSWAVETABLE wavtbl ON wavtblr.DEMANDWAVEID = wavtbl.WAVEID
	LEFT JOIN WHSWORKTABLE wkt ON wavtblr.WAVEID = wkt.WAVEID -- Work from replenishment wave
	WHERE
		wavtbl.EXECUTINGUTCDATETIME > GETUTCDATE() - @ReportDays
		AND wavtbl.WAVESTATUS > 1
		AND wavtbl.WAVETEMPLATENAME = 'REGULAR DIRECT'
		AND wavtblr.[DESCRIPTION] LIKE 'Replenishment%'
	GROUP BY wavtblr.DEMANDWAVEID
) AS WVR
ON WPT.Wave = WVR.WAVE
WHERE WPT.Shipments > 100
GROUP BY WPT.ProcessingDate
ORDER BY WPT.ProcessingDate

