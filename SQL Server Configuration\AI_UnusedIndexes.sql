
-- Bing AI
/*
This query uses the sys.dm_db_index_usage_stats dynamic management view to find indexes that have more writes than reads, suggesting they may not be 
effectively used for query optimization. The ‘Write-Read Difference’ column shows the difference between the number of writes and reads, which helps to 
prioritize indexes that may have the most significant impact on performance if removed. The ‘Included Columns’ column lists the columns that are included 
in each index but are not part of the index key.

Please review the results carefully and consider the full workload of your database before making any changes. It’s also a good practice to monitor these 
indexes over a significant period of time to ensure they are consistently unused before deciding to remove them.

When the “Included Columns” value is NULL in the query result, it means that there are no non-key columns included in the index. In SQL Server, 
an index can be created with key columns that define the index structure and, optionally, included columns that are stored in the leaf level of the 
index for covering queries. If an index does not have any included columns, the query will return NULL for that index’s “Included Columns” field.

Indexes without included columns are not necessarily a problem; it depends on the specific use case and query patterns. However, if a query could benefit
from having certain columns included in the index to avoid additional lookups, this might be an area for potential optimization.
*/
SELECT 
    t.name AS 'Table Name',
    i.name AS 'Index Name',
    i.type_desc AS 'Index Type',
    s.user_updates AS 'Number of Writes',
    s.user_seeks + s.user_scans + s.user_lookups AS 'Number of Reads',
    s.user_updates - (s.user_seeks + s.user_scans + s.user_lookups) AS 'Write-Read Difference',
    STUFF((SELECT ', ' + c.name 
           FROM sys.index_columns ic
           INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
           WHERE ic.is_included_column = 1 AND ic.object_id = i.object_id AND ic.index_id = i.index_id
           FOR XML PATH('')), 1, 2, '') AS 'Included Columns'
FROM sys.dm_db_index_usage_stats s
INNER JOIN sys.indexes i ON i.object_id = s.object_id AND i.index_id = s.index_id
INNER JOIN sys.tables t ON i.object_id = t.object_id
WHERE OBJECTPROPERTY(i.object_id, 'IsUserTable') = 1
AND s.user_updates > (s.user_seeks + s.user_scans + s.user_lookups)
AND i.index_id > 0 -- Exclude heap
AND s.database_id = DB_ID() -- Current database
ORDER BY 'Write-Read Difference' DESC;



