
DECLARE @MaxDays INT = 90

SELECT 
    ID
    , Name,
    LastLogon
FROM
(
SELECT 
    ui.ID
    , ui.NAME
    , MAX( ul.CREATEDDATETIME ) AS LastLogon
FROM    
    userinfo ui
    JOIN SYSUSERLOG ul ON ul.USERID = ui.ID AND ui.[PARTITION] = ul.[PARTITION] AND ui.[ENABLE] = 1
GROUP BY
    ui.ID, ui.Name
) AS LastLog
WHERE
    LastLogon < ( GETUTCDATE() - @MaxDays )
    AND ID NOT LIKE 'ax%'
ORDER BY
    LastLogon


