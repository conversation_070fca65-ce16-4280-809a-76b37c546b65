-- SlotTiers. Not used Anymore

SELECT 
    CASE WHEN ISNULL(idim.INVENTSIZEID, '') <> '' THEN icomb.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
          WHEN ISNULL(idim.INVENTCOLORID,'') <> '' THEN icomb.ITEMID + '-' + idim.INVENTCOLORID
          ELSE icomb.ITEMID
      END 
    AS 'SKU', icomb.HASLOTTIERACTIVE AS 'ActiveSlotTier', icomb.HASLOTTIEROVERRIDE AS 'OverRide SlotTier'
FROM INVENTDIMCOMBINATION icomb
LEFT JOIN inventdim idim  ON icomb.INVENTDIMID = idim.INVENTDIMID
WHERE ISNULL(icomb.HASLOTTIERACTIVE,'') <> ''
ORDER BY SKU
