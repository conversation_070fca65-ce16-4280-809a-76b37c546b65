
-- Identifying/updating items in inventory with no dimensions
-- Trying to find if there are available dimensions to use it as AVG

-- Issue was noted around February 10, 2024

SELECT 
    onhi.*
    , avgd.AvgWeigth
    , avgd.AvgDepth
    , avgd.AvgWidth
    , avgd.AvgHeigth
FROM
(
-- Items on hand without dimensions
SELECT 
    isum.ITEMID             AS [Item]
    , idim.INVENTCOLORID    AS [Color]
    , idim.INVENTSIZEID     AS [Size]
    /*, loc.WMSLOCATIONID     AS [Loc]
    , isum.PHYSICALINVENT   AS [Qty]
    , idim.INVENTSTATUSID   AS [Status]*/
FROM
    INVENTSUM isum
    JOIN INVENTDIM idim ON isum.INVENTDIMID = idim.INVENTDIMID AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION]
    JOIN WMSLOCATION loc ON loc.WMSLOCATIONID =idim.WMSLOCATIONID AND loc.DATAAREAID = idim.DATAAREAID AND loc.[PARTITION] = idim.[PARTITION]
    join WHSPHYSDIMUOM physd ON physd.ITEMID = isum.ITEMID AND physd.ECORESITEMCOLORNAME = idim.INVENTCOLORID AND physd.ECORESITEMSIZENAME = idim.INVENTSIZEID 
        AND physd.DATAAREAID = 'ha' AND physd.[PARTITION] = isum.[PARTITION]
WHERE
    isum.PHYSICALINVENT BETWEEN 1 AND 99999 
    AND idim.inventlocationid = '4010'
    AND loc.inventlocationid = '4010' 
    AND loc.locprofileid IN ('Picking', 'Picking A', 'Picking D', 'PalletPicking', 'Bulk')
    AND physd.WEIGHT = 0 
    AND physd.DEPTH = 0 
    AND physd.WIDTH = 0 
    AND physd.HEIGHT = 0
    --AND loc.zoneid NOT IN ( 'Current Pick')
    --AND idim.INVENTSTATUSID = 'Available'
   -- AND isum.ITEMID     = '33218'
GROUP BY
    isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID
/*ORDER BY
    isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID   */ 
) AS onhi
LEFT JOIN
(
-- Average dimensions for items
SELECT 
    --TOP 100
    physd.ITEMID                AS [Item]
    , physd.ECORESITEMCOLORNAME AS [Color]
    , physd.ECORESITEMSIZENAME  AS [Size]
    , AVG(physd_avg.WEIGHT)     AS [AvgWeigth]
    , AVG(physd_avg.DEPTH)      AS [AvgDepth]        
    , AVG(physd_avg.WIDTH)      AS [AvgWidth]
    , AVG(physd_avg.HEIGHT)     AS [AvgHeigth]
FROM 
    WHSPHYSDIMUOM physd
    JOIN WHSPHYSDIMUOM physd_avg ON physd.ITEMID = physd_avg.ITEMID AND physd.ECORESITEMSIZENAME = physd_avg.ECORESITEMSIZENAME
WHERE
    physd.WEIGHT = 0 
    AND physd.DEPTH = 0 
    AND physd.WIDTH = 0 
    AND physd.HEIGHT = 0

    AND physd_avg.WEIGHT > 0 
    AND physd_avg.DEPTH > 0 
    AND physd_avg.WIDTH > 0 
    AND physd_avg.HEIGHT > 0 -- Missing dimensions
    --AND physd.ITEMID = '80312'
GROUP BY
    physd.ITEMID, physd.ECORESITEMCOLORNAME, physd.ECORESITEMSIZENAME
) AS avgd
ON onhi.Item = avgd.Item AND onhi.Color = avgd.Color AND onhi.[Size] = avgd.[Size]

-- Working query
/*
WITH avgd AS(
SELECT 
    --TOP 100
    ITEMID
    --, ECORESITEMCOLORNAME   AS [Color]
    , ECORESITEMSIZENAME    AS [Size]
    , AVG(WEIGHT)           AS [AvgWeigth]
    , AVG(DEPTH)            AS [AvgDepth]        
    , AVG(WIDTH)            AS [AvgWidth]
    , AVG(HEIGHT)           AS [AvgHeigth]
FROM 
    WHSPHYSDIMUOM physd
    --JOIN ONHI ON physd.ITEMID = ONHI.Item AND physd.ECORESITEMCOLORNAME = ONHI.Color AND physd.ECORESITEMSIZENAME = ONHI.Size
WHERE
    physd.WEIGHT > 0 
    AND physd.DEPTH > 0 
    AND physd.WIDTH > 0 
    AND physd.HEIGHT > 0
   -- AND ISNULL(physd.ECORESITEMSIZENAME, '') <> ''
   -- AND ISNULL(physd.ECORESITEMCOLORNAME, '') <> ''
GROUP BY
    ItemID, ECORESITEMSIZENAME
)
SELECT -- Identifying only 
    physd.ITEMID                AS [Item]    
    , physd.ECORESITEMCOLORNAME AS [Color]
    , physd.ECORESITEMSIZENAME  AS [Size]
    , physd.WEIGHT
    , physd.DEPTH
    , physd.WIDTH
    , physd.HEIGHT
    , avgd.AvgWeigth
    , avgd.AvgDepth
    , avgd.AvgWidth
    , AvgHeigth
    --, onhi.loc
FROM 
    WHSPHYSDIMUOM physd
    JOIN avgd ON avgd.ITEMID = physd.ITEMID AND avgd.[Size] = physd.ECORESITEMSIZENAME 
WHERE
    physd.WEIGHT = 0 
    AND physd.DEPTH = 0 
    AND physd.WIDTH = 0 
    AND physd.HEIGHT = 0 -- Missing dimensions

*/


