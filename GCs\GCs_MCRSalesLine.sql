
/*
MCRSalesLine.GiftCardNumber == RetailGifCardTable.entryid
MCRSalesTable
*/


SELECT 
    sl.SALESID
    , msl.GIFTCARDNUMBER
    ,msl.GIFTCARDPIN
    , CASE WHEN msl.GIFTCARDTYPE = 1 THEN 'PhysGC' WHEN msl.GIFTCARDTYPE = 0 THEN 'EGC' ELSE 'UNK' END AS GCType
    , msl.GIFTCARDGIFTMESSAGE
    , msl.GIFTCARDRECIPIENTNAME
    , msl.GIFTCARDRECIPIENTEMAIL
FROM 
    MCRSALESLINE msl
LEFT JOIN 
    SALESLINE sl ON sl.recid = msl.salesline AND sl.DATAAREAID = msl.DATAAREAID AND sl.[PARTITION] = msl.[PARTITION]
WHERE
    sl.salesid IN ( '47110293', '47121402', '47121887')