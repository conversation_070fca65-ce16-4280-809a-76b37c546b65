/*
Order Transactions
Sam's request 11/3/2023

*/
SELECT
    wktbl.OrderNum          AS [SalesId]
    , wktbl.ContainerID     AS [ContainerId]  
    , CASE 
        WHEN isnull(idim.inventcolorid, '') = '' THEN wkln.itemid
        WHEN isnull(idim.inventsizeid, '') = '' THEN wkln.itemid + '-' + idim.inventcolorid
        ELSE wkln.itemid + '-' + idim.inventcolorid + '-' + idim.inventsizeid    
    END                                                                 AS [SKU]  
    , ll.CREATEDDATETIME /*AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'*/  AS [UTC_CreatedDateTime]
    --, wktbl.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYPickDate]
    
    -- The container could be modified way later that the order is picked
    --, cnttbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYShipDate]
    , wktbl.MODIFIEDDATETIME /*AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time' */ AS [UTC_ShippedDateTime]
    , CAST(wkln.QTYWORK AS INT)     AS [Qty]
    , wkln.UNITID                   AS [Unit]
FROM
    [DAX_Archive].[arc].WHSWORKLINE wkln 
    INNER JOIN [DAX_Archive].[arc].WHSWORKTABLE wktbl       ON wktbl.WORKID         = wkln.WORKID       AND wktbl.DATAAREAID    = 'ha'  AND wktbl.[PARTITION]   = 5637144576
    INNER JOIN [DAX_Archive].[arc].INVENTDIM idim           ON idim.INVENTDIMID     = wkln.INVENTDIMID  AND idim.DATAAREAID     = 'ha'  AND idim.[PARTITION]    = wkln.[PARTITION]
    INNER JOIN [DAX_Archive].[arc].WHSCONTAINERTABLE cnttbl ON cnttbl.CONTAINERID   = wktbl.CONTAINERID AND cnttbl.DATAAREAID   = 'ha'  AND cnttbl.[PARTITION]  = wktbl.[PARTITION]
    INNER JOIN [DAX_Archive].[arc].WHSLOADLINE ll           ON ll.LOADID            = wkln.LOADID       AND ll.DATAAREAID       = 'ha'  AND ll.[PARTITION]      = wkln.[PARTITION]
                                --  AND ll.ITEMID = wkln.ITEMID AND ll.INVENTDIMID = wkln.INVENTDIMID 
                                  AND wkln.INVENTTRANSID = ll.INVENTTRANSID
                                --    AND wkln.LOADLINEREFRECID = ll.RECID
WHERE
    wkln.WORKCLASSID = 'DirectPick'
    AND wkln.WORKSTATUS = 4  -- Closed line
    AND wktbl.WORKSTATUS = 4 -- Closed header
    AND wkln.WorkType = 1 -- Pick lines only
    AND ISNULL(cnttbl.SHIPCARRIERTRACKINGNUM, '') <> '' -- Shipped
    AND wkln.ITEMID NOT IN ( '3333', '30991')
    AND ll.CREATEDDATETIME BETWEEN '1/1/2023 05:00:00 AM' AND '1/1/2024 04:59:59 AM'
/*    
UNION

SELECT
    wktbl.OrderNum          AS [SalesId]
    , wktbl.ContainerID     AS [ContainerID]    
    , wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID      AS [SKU]
    , ll.CREATEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYCreatedDate]
    , wktbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYWorkCreatedDate]
    , cnttbl.MODIFIEDDATETIME AT Time Zone 'UTC' AT Time Zone 'Eastern Standard Time'  AS [KYShipDate]
    , CAST(wkln.QTYWORK AS INT)     AS [Qty]
    , wkln.UNITID                   AS [Unit]
FROM
    [DAX_PROD].[dbo].WHSWORKLINE wkln 
    JOIN [DAX_PROD].[dbo].WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wktbl.DATAAREAID = wkln.DATAAREAID AND wktbl.[PARTITION] = wkln.[PARTITION]
    JOIN [DAX_PROD].[dbo].INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND idim.[PARTITION] = wkln.[PARTITION]
    JOIN [DAX_PROD].[dbo].WHSCONTAINERTABLE cnttbl ON cnttbl.CONTAINERID = wktbl.CONTAINERID AND cnttbl.DATAAREAID = wktbl.DATAAREAID AND cnttbl.[PARTITION] = wktbl.[PARTITION]
    JOIN [DAX_PROD].[dbo].WHSLOADLINE ll  ON ll.LOADID =wkln.LOADID AND ll.SHIPMENTID = wkln.SHIPMENTID AND ll.DATAAREAID = wkln.DATAAREAID AND ll.[PARTITION] = wkln.[PARTITION]
                AND ll.ITEMID = wkln.ITEMID AND ll.INVENTDIMID = wkln.INVENTDIMID
WHERE
    wkln.WORKCLASSID = 'DirectPick'
    AND wkln.WORKSTATUS = 4  -- Closed
    AND wktbl.WORKSTATUS = 4 -- Closed
    AND wkln.WorkType = 1 -- Pick lines only
    AND ISNULL(cnttbl.SHIPCARRIERTRACKINGNUM, '') <> '' -- Shipped
    AND wkln.ITEMID NOT IN ( '3333', '30991')
    AND cnttbl.MODIFIEDDATETIME > '1/10/2024'
ORDER BY
    SalesId, ContainerID
*/
--GROUP BY
--    wktbl.OrderNum, wktbl.ContainerID
ORDER BY
    SalesId, ContainerID
/*
SELECT TOP 15 *
FROM 
    WHSWORKLINE wkln
    LEFT JOIN WHSLOADLINE ll ON ll.INVENTTRANSID = wkln.INVENTTRANSID AND ll.DATAAREAID = wkln.DATAAREAID AND ll.[PARTITION] = wkln.[PARTITION]
WHERE 
     wkln.WORKID = 'WK0030607962'
     AND wkln.WORKTYPE = 1
     AND wkln.WORKCLASSID = 'DirectPick'
ORDER BY
    ll.RECID
*/

--sp_columns whsloadline
--sp_columns whsworkline
--sp_columns whsworktable