
-- Get Inventory Data
-- This query is in use as of 7/25/2023

SELECT --TOP 100
     CASE WHEN ISNULL( idim.INVENTSIZEID, '' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
          WHEN ISNULL( idim.INVENTCOLORID,'' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID
          ELSE itbl.ITEMID
     END AS 'SKU'
    , prodtrans.DESCRIPTION AS 'Description'
    , idim.[INVENTSTATUSID] AS 'Status'
     --CONVERT( DECIMAL( 10, 0 ), AVG( isum.PHYSICALINVENT ), 0 ) AS 'Qty', 
    , isum.PHYSICALINVENT AS Qty
        --CONVERT( DECIMAL( 10, 0 ), AVG( isum.RESERVPHYSICAL ), 0 ) AS 'Reserve', 
    , isum.RESERVPHYSICAL AS Reserve
    --CONVERT( DECIMAL(10, 0 ), AVG( isum.ONORDER ), 0 ) AS 'OnOrder', 
    , isum.ONORDER AS OnOrder
    --CONVERT( DECIMAL( 10, 0 ), SUM( isum.PHYSICALINVENT ), 0 ) - CONVERT( DECIMAL( 10, 0 ), SUM( isum.RESERVPHYSICAL ), 0 ) - CONVERT(DECIMAL( 10, 0 ), SUM( isum.ONORDER ), 0 ) AS 'Available', 
    , isum.physicalinvent - isum.reservphysical - isum.onorder AS Available
    , idim.[LICENSEPLATEID] AS 'LicensePlate', idim.[WMSLOCATIONID] AS 'Bin'
    , wmsloc.LOCPROFILEID AS 'Bin Type'
    --CONVERT( decimal( 10, 2 ), prod.COST, 0 ) AS 'Cost', 
    , prod.cost AS Cost
    --( CONVERT( DECIMAL( 10, 0 ),SUM( isum.PHYSICALINVENT ), 0 ) - CONVERT( DECIMAL( 10, 0),SUM( isum.RESERVPHYSICAL ), 0 ) - CONVERT( DECIMAL( 10, 0 ), SUM( isum.ONORDER ), 0 ) ) * CONVERT( DECIMAL( 10, 2 ), prod.COST, 0 ) AS 'ExtCost' 
    , ( isum.physicalinvent - isum.reservphysical - isum.onorder ) * prod.cost AS 'ExtCost'
FROM INVENTDIM idim 
INNER JOIN INVENTSUM isum  ON idim.INVENTDIMID = isum.INVENTDIMID 
INNER JOIN INVENTTABLE itbl ON isum.ITEMID      = itbl.ITEMID
INNER JOIN WMSLOCATION wmsloc ON idim.WMSLOCATIONID = wmsloc.WMSLOCATIONID
INNER JOIN ECORESPRODUCTTRANSLATION prodtrans ON itbl.PRODUCT = prodtrans.PRODUCT
LEFT JOIN HARMSPRODUCT prod ON 
CASE    WHEN ISNULL( idim.INVENTSIZEID, '' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
        WHEN ISNULL( idim.INVENTCOLORID,'' ) <> '' THEN itbl.ITEMID + '-' + idim.INVENTCOLORID
        ELSE itbl.ITEMID END = prod.ITEMSKU       
WHERE 
    isum.PHYSICALINVENT <> 0 AND isum.PHYSICALINVENT < 99999 AND idim.INVENTLOCATIONID = '4010' AND idim.INVENTSITEID = 'HA USA' 
    AND wmsloc.LOCPROFILEID IN ( 'Bulk', /*'Inbound', 'No LP Track',*/ 'W001', 'Picking', 'Picking A','CUBISCAN' ) 
    AND itbl.ITEMID NOT IN ( '30991', '3333', '9999', '9997' ) 
GROUP BY 
    idim.INVENTLOCATIONID, itbl.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, prodtrans.DESCRIPTION, idim.[INVENTSTATUSID], idim.[LICENSEPLATEID], idim.[WMSLOCATIONID], isum.PHYSICALINVENT, isum.RESERVPHYSICAL, isum.onorder, wmsloc.LOCPROFILEID, prod.COST
ORDER BY 
    'SKU', 'Bin Type', 'Bin'