/****** Script for SelectTopNRows command from SSMS  ******/


-- Qty by workid, Cluster
SELECT TOP (1000) 
        /*[assignmentId]
      ,[version]
      [assignmentNumber]
      ,[splitNumber]
      ,[groupNumber]
      ,[groupCount]
      ,[route]
      ,[deliveryDate]
      ,[deliveryLocation]
      [operatorName]
      ,[operatorIdentifier]*/
      [customerNumber]
      ,[customerName]
     /* ,[customerAddress]
      ,[regionName]
      ,[regionNumber]*/
      ,[workIdentifierValue]
      ,[totalItemQuantity]
     /* ,[goalTime]
      ,[assignmentType]
      ,[status]
      ,[exportStatus]
      ,[containerCount]
      ,[pickDetailCount]
     ,[loadingRegionName]
      ,[loadingRegionNumber]
      ,[departureDateTime]
      ,[siteId]
      ,[siteName]
      ,[createdDate]*/
      ,[hostOrderNumber]/*
      ,[shortDeliveryLocation]
      ,[orderType]
      ,[importID]*/
  FROM [VoiceLink].[dbo].[arch_sel_assignments]
  WHERE
    workIdentifierValue IN ( 'CL001307343')
  ORDER BY
    [workIdentifierValue],[hostOrderNumber]