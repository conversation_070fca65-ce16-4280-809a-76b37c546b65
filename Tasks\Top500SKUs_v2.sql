-- WIP
/*
SELECT SKU,
       COUNT(DISTINCT Location) OVER (PARTITION BY SKU) AS TotalDistinctLocations,
       SUM(CASE WHEN LocProfile IN ('Picking', 'Picking A') THEN 1 ELSE 0 END) OVER (PARTITION BY SKU) AS PickingOrPickingALocations,
       COUNT(DISTINCT CASE WHEN LocProfile = 'PalletPicking' THEN Location END) OVER (PARTITION BY SKU) AS PalletPickingLocations
FROM YourTable

*/

SELECT 
    wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
    --, wktbl.WORKID
    , CONVERT( DECIMAL( 10, 0 ), SUM( wkln.INVENTQTYWORK ) )     AS 'Qty'
    , COUNT( DISTINCT wkln.WMSLOCATIONID )                       AS [RegLoc]
FROM
    WHSWORKTABLE wktbl
    LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
    LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
WHERE
    wktbl.CREATEDDATETIME BETWEEN @StartDate AND @EndDate
    AND wktbl.WORKTRANSTYPE = 2  -- Sales Order
    AND wkln.WORKTYPE = 1 -- Pick. To get only one line
    AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
    AND loc.locprofileid LIKE 'Picking%' -- Only Picking locations, no gaylords
    AND wktbl.WORKSTATUS < 5 -- Not cancelled
GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID