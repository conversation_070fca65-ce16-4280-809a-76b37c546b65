

SELECT TOP 10
    qt.query_sql_text AS QueryText,
    SUM(rs.count_executions) AS TotalExecutions,
    SUM(rs.avg_duration * rs.count_executions / 1000) AS TotalDurationInMs,
    MAX(rs.max_duration / 1000) AS MaxDurationInSec
FROM
    sys.query_store_query_text qt
INNER JOIN
    sys.query_store_query q ON qt.query_text_id = q.query_id
INNER JOIN
    sys.query_store_plan p ON q.query_id = p.query_id
INNER JOIN
    sys.query_store_runtime_stats rs ON p.plan_id = rs.plan_id
INNER JOIN
    sys.query_store_runtime_stats_interval rsi ON rs.runtime_stats_interval_id = rsi.runtime_stats_interval_id
GROUP BY
    qt.query_sql_text
ORDER BY
    MAX(rs.max_duration) DESC;