/*
Updated on 11/16/2023 to count for Manual Moves
*/
USE DAX_PROD

DECLARE @ReportDays AS INT = 21
DECLARE @MinSalesQty AS INT = 5

-- Calculating what type of replenishments have been used for the most sold SKUs
SELECT
    SKUSales.SKU
    , SKUSales.Qty              AS [UnitsSold]
    , RepBySKU.MinMaxCartons    AS [MinMaxCartons]
    , RepBySKU.DemandCartons    AS [DemandCartons]
    , RepBySKU.TotalUnits       AS [TotalUnitsReplenished]
    , ManualMoves.MMCount       AS [ManualMoves]
    , ManualMoves.MMTotal       AS [TotalPiecesMoved]
FROM
(
SELECT 
    wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
    --, wktbl.WORKID
    , CONVERT( DECIMAL( 10, 0 ), SUM( wkln.INVENTQTYWORK ) )     AS 'Qty'
FROM
    WHSWORKTABLE wktbl
    LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
    LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
WHERE
    wktbl.CREATEDDATETIME > GETUTCDATE() - @ReportDays
    AND wktbl.WORKTRANSTYPE = 2  -- Sales Order
    AND wkln.WORKTYPE = 1 -- Pick. To get only one line
    AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
    AND loc.locprofileid LIKE '%Picking%' -- Only Picking locations
    AND wktbl.WORKSTATUS < 5 -- Not cancelled
GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID
HAVING COUNT( * ) > @MinSalesQty 
) AS SKUSales
LEFT JOIN
(    
SELECT
    SKU
    , SUM( CASE WHEN ReplenType = 'MinMax' THEN 1 ELSE 0 END)  AS [MinMaxCartons]
    , SUM( CASE WHEN ReplenType = 'Demand' THEN 1 ELSE 0 END)  AS [DemandCartons]
    , SUM( CartonQty )                                         AS [TotalUnits]
FROM    
    (
        SELECT
            wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
            --, wkln.WORKID
            , CASE WHEN wktbl.WORKTEMPLATECODE = 'Forward Replen' THEN 'MinMax' ELSE 'Demand' END AS ReplenType
        , CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )     AS 'CartonQty'
        FROM
            WHSWORKTABLE wktbl
            LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
            LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
            LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
        WHERE
            wktbl.CREATEDDATETIME > GETUTCDATE() - ( @ReportDays + 7 )  -- Replenishments since the previous week
            AND wktbl.WORKTRANSTYPE = 11  -- Replenishment
            AND wkln.WORKTYPE = 2 -- Put. To get only one line
            AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
            AND loc.locprofileid LIKE '%Picking%' -- Only Picking locations
            AND wktbl.WORKSTATUS < 5 -- Not cancelled
            --AND wktbl.WORKID ='WK0025410522'
            --GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, wktbl.WORKTEMPLATECODE, wkln.INVENTQTYWORK
     ) AS RepDetails
GROUP BY SKU     
) AS RepBySKU       
ON RepBySKU.SKU = SKUSales.SKU
LEFT JOIN
(
SELECT
    SKU
    , COUNT( * )                AS[MMCount]
    , SUM(['ItemsMoved'])       AS [MMTotal]
FROM
    (
    SELECT
        wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID        AS 'SKU'
        --, wkln.WORKID                                                 AS [WorkID]
        --, CASE WHEN wktbl.WORKTEMPLATECODE = 'Inventory Movement' THEN 'InventoryMovement' ELSE 'Unknown' END AS ReplenType
        , CONVERT( DECIMAL( 10, 0 ), wkln.INVENTQTYWORK )      AS ['ItemsMoved']
    FROM
        WHSWORKTABLE wktbl
        LEFT JOIN WHSWORKLINE wkln ON wktbl.WORKID = wkln.WORKID AND wktbl.[PARTITION] = wkln.[PARTITION] AND wktbl.DATAAREAID = 'ha'
        LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.[PARTITION] = wkln.[PARTITION] AND idim.DATAAREAID = 'ha'
        LEFT JOIN WMSLOCATION loc ON loc.wmsLocationID = wkln.wmslocationid AND loc.INVENTLOCATIONID = '4010' AND loc.[PARTITION] = wkln.[PARTITION] AND wkln.DATAAREAID = 'ha'
    WHERE
        wktbl.CREATEDDATETIME > GETUTCDATE() - (@ReportDays + 7)  -- Replenishments since the previous week
        AND wktbl.WORKTRANSTYPE = 7  -- Inventory movement
        AND wkln.WORKTYPE = 2 -- Put. To get only one line
        AND wktbl.INVENTLOCATIONID = '4010'  -- KY DC
        AND loc.locprofileid LIKE '%Picking%' -- Only Picking locations
        AND wktbl.WORKSTATUS < 5 -- Not cancelled
        AND ISNULL(wktbl.TARGETLICENSEPLATEID, '') <> '' -- Only movements coming from Bulk
        --AND wktbl.WORKID ='WK0025410522'
    --GROUP BY wkln.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, wktbl.WORKID
    ) AS ManualM
GROUP BY SKU
) AS ManualMoves   
ON ManualMoves.SKU = SKUSales.SKU
WHERE 
    1 = 1
    --AND SKUSales.SKU LIKE '81259-75Z%'
ORDER BY SKUSales.Qty DESC

/*
SELECT *
FROM 
    WHSWORKLINE
WHERE 
    WORKID = 'WK0025337844'

SELECT 
    sl.SALESID              AS OrderNumber
    , sl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID AS SKU
    , CONVERT( DECIMAL( 10, 0), SUM( sl.QTYORDERED ) ) AS Qty
FROM 
    salesline sl
    LEFT JOIN INVENTDIM idim ON idim.INVENTDIMID = sl.INVENTDIMID AND idim.[PARTITION] = sl.[PARTITION] AND sl.DATAAREAID = 'ha'
WHERE
    sl.SALESSTATUS = 3 -- Invoiced
    AND sl.SALESTYPE = 3  -- Sales Order
    AND sl.CUSTGROUP NOT LIKE 'AMAZON'
    AND sl.MODIFIEDDATETIME > GETUTCDATE() - 5
    --AND sl.SALESID = '42248988'
GROUP BY sl.ITEMID + '-' + idim.INVENTCOLORID + '-' + idim.INVENTSIZEID, sl.SALESID
--HAVING COUNT( * ) > 10 -- More than 10 pieces sold


SELECT *
FROM
    WHSWORKTABLE wktbl
WHERE 
    wktbl.ORDERNUM = '43558356'


*/