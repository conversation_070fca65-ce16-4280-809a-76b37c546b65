
-- Where active put away is putting items

USE DAX_PROD

SELECT
    wktbl.WORKID
    , wktbl.WORKBUILDID
    , wkln.WMSLOCATIONID
    , loc.LOCPROFILEID
    , loc.ZONEID
    , hafc.SLOTTIERVALUE
    , CAST( hafc.HAFORECASTSTARTDATE AS DATE ) AS ForecastDate
FROM
    WHSWORKTABLE wktbl
    INNER JOIN WHSWORKLINE wkln ON wkln.WORKID = wktbl.WORKID AND wkln.DATAAREAID = wktbl.DATAAREAID AND wkln.[PARTITION] = wktbl.[PARTITION]
    INNER JOIN INVENTDIM idim ON idim.INVENTDIMID = wkln.INVENTDIMID AND idim.DATAAREAID = wkln.DATAAREAID AND wkln.[PARTITION] = idim.[PARTITION]
    INNER JOIN WMSLOCATION loc ON wkln.WMSLOCATIONID = loc.WMSLOCATIONID AND wkln.DATAAREAID = loc.DATAAREAID AND wkln.[PARTITION] = loc.[PARTITION]
    LEFT JOIN HAFORECASTREPLENISHMENTTABLE hafc ON wkln.ITEMID = hafc.ITEM AND idim.INVENTCOLORID = hafc.COLOR AND idim.INVENTSIZEID = hafc.SIZE_
WHERE 
    wktbl.worktemplatecode = 'Active Putaway'
    AND wktbl.CREATEDDATETIME > '4/15/2025' 
    AND wkln.WORKTYPE = 2   -- Put
    AND loc.LOCPROFILEID LIKE '%Picking%'  -- The last Put
    AND loc.ZONEID <> hafc.SLOTTIERVALUE  -- The ones not matching
ORDER BY
    wktbl.WORKBUILDID


--sp_columns WHSWORKTABLE