

    SELECT SALESTABLE.dataareaid, 
		   SALESTABLE.salesid, 
		   SA<PERSON><PERSON>IN<PERSON>.dlvmode, 
		   SALESLINE.createddatetime, 
		   SALESTABLE.salesstatus, 
		   SALESLINE.itemid, 
		   SALESLINE.inventdimid, 
		   SALESLINE.inventtransid, 
		   SALESLINE.qtyordered, 
		   SALESLINE.remainsalesphysical, 
		   SALESTABLE.<PERSON><PERSON><PERSON>rStopped,
            (select SUM(QTY) * -1
             from INVENTTRANSORIGIN WITH(NOLOCK)
			join INVENTTRANS WITH(NOLOCK) on INVENTTRANS.INVENTTRANSORIGIN = INVENTTRANSORIGIN.RECID and INVENTTRANS.STATUSISSUE = 4                     
			where INVENTTRANSORIGIN.INVENTTRANSID = SALESLINE.INVENTTRANSID) as ReservedPhysical, 
			SALESTABLE.RELEASESTATUS, MC<PERSON><PERSON>ESTABLESHIPPING.ShipComplete, 
			WHSLOADLINE.SHIPMENTID, 
			ReleasedToWHDate = WHSLOADLINE.CREATED<PERSON><PERSON><PERSON><PERSON>, 
			WHSLOADLINE.LOADID, 
			W<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.LOADSTATUS,
			WHSSH<PERSON>MENTTABLE.SHIPMENTSTATUS
	INTO #TEMP
    FROM SALESTABLE WITH(NOLOCK) 
    JOIN SALESLINE  WITH(NOLOCK) ON  SALESTABLE.salesid = SALESLINE.salesid 
        AND SALESTABLE.dataareaid = SALESLINE.dataareaid
        AND SALESTABLE.Partition = SALESLINE.Partition
        AND SALESLINE.salesstatus = 1 -- only open sales lines
        AND SALESLINE.qtyordered > '0'    
        AND SALESLINE.custgroup not like 'W%'

    JOIN inventdim id WITH(NOLOCK)  ON  SALESLINE.inventdimid = id.inventdimid 
         AND SALESLINE.dataareaid = id.dataareaid   
         AND SALESLINE.Partition = id.Partition
         
	LEFT JOIN WHSLOADLINE WITH(NOLOCK) ON     SALESLINE.INVENTTRANSID = WHSLOADLINE.INVENTTRANSID 
         AND    SALESLINE.DATAAREAID = WHSLOADLINE.DATAAREAID  
         AND SALESLINE.Partition = WHSLOADLINE.Partition    
         AND WHSLOADLINE.INVENTTRANSTYPE = 0 -- Sales transaction types only
        
	LEFT JOIN WHSLOADTABLE WITH(NOLOCK) ON  WHSLOADLINE.LOADID = WHSLOADTABLE.LOADID
        AND WHSLOADLINE.DATAAREAID = WHSLOADTABLE.DATAAREAID
        AND WHSLOADLINE.Partition = WHSLOADTABLE.Partition

    LEFT JOIN MCRSalesTableShipping WITH(NOLOCK) ON (MCRSALESTABLESHIPPING.salestable = SALESTABLE.recid) 
	
	LEFT JOIN WHSSHIPMENTTABLE WITH(NOLOCK) ON (WHSSHIPMENTTABLE.SHIPMENTID=WHSLOADLINE.SHIPMENTID) 

		ORDER BY SALESLINE.createddatetime desc




		/*This temp table is created because I couldnt connect the two LEFT JOINS-THEREFORE I HAD TO DO 1 JOIN IN #TEMP AND THEN THE SECOND JOIN IS IN THE #OPENORDER TABLE */
		/*This one contains the waveid but not the wavenametemplate*/
	SELECT  WHSWAVELINE.WAVEID,
			
			--WHSWAVETABLE.WAVETEMPLATENAME,
			#TEMP.DATAAREAID,	
			salesid,
			dlvmode,
			DATEADD(hh, -4, createddatetime) AS KYtime,
			salesstatus,
			itemid,	
			inventdimid,	
			inventtransid,	
			qtyordered,	
			remainsalesphysical,	
			MCROrderStopped,	
			ReservedPhysical,	
			RELEASESTATUS,	
			ShipComplete,	
			#TEMP.SHIPMENTID,	
			ReleasedToWHDate,	
			#TEMP.LOADID,	
			LOADSTATUS,	
			SHIPMENTSTATUS,
		CASE	
			WHEN SHIPMENTSTATUS IS NULL THEN '0 UnWaved'
			WHEN SHIPMENTSTATUS = 0 THEN '1 Open'
			WHEN SHIPMENTSTATUS = 1 THEN '2 Waved'
			WHEN SHIPMENTSTATUS = 2 THEN '3 In Process'
			WHEN SHIPMENTSTATUS = 3 THEN '4 In Packing'
			WHEN SHIPMENTSTATUS = 4 THEN '5 Loaded'
			WHEN SHIPMENTSTATUS = 5 THEN '6 Shipped'
			WHEN SHIPMENTSTATUS = 6 THEN '7 Recieved'			
		END AS 'Shipment Status'
	INTO #OPENORDERS
	FROM #TEMP
		LEFT JOIN WHSWAVELINE ON #TEMP.SHIPMENTID = WHSWAVELINE.SHIPMENTID
		ORDER BY #TEMP.SHIPMENTID


	

		
		/*The openorders table contains the wavename template and waveid*/
	SELECT *,
		 WAVETEMPLATENAME,
		 WHSWAVETABLE.RELEASEDUTCDATETIME AS WAVERELEASED,
		CASE	
        /* Javier
			WHEN WAVETEMPLATENAME = 'SINGLE DIRECT'	THEN 'SINGLE'
			WHEN WAVETEMPLATENAME <> 'SINGLE DIRECT'	THEN 'MULTIPLE'
        */
        -- Me, 4/4/2022. This is just a patch. AZ3P singles not included
            WHEN WAVETEMPLATENAME like '%Single%' THEN 'SINGLE' ELSE 'MULTIPLE'
		END AS 'WaveType'
		FROM #OPENORDERS
		LEFT JOIN WHSWAVETABLE ON #OPENORDERS.WAVEID = WHSWAVETABLE.WAVEID