-- Modifying on 5/1/2025 to take into account rush orders going thru the AB

DECLARE @DaysToCheck INT = 10; -- Number of days to check for rush orders
DECLARE @TruckGone NVARCHAR(20) = ' 16:55:00.000'; -- Time when the truck leaves the building
DECLARE @CutOffTime NVARCHAR(20) = ' 14:20:00.000'; -- At this time, all the rush orders should be released to the WH


--SELECT DATEPART(w, GETDATE()-3)
--SELECT DATEADD(dd, 0, DATEDIFF(dd, 0, GETDATE()))
--SELECT CONCAT( @DayToCheck, '06:09:00 PM' )
--SELECT CAST( CAST( CAST(GETDATE() AS DATE) AS nvarchar(10) ) + ' 05:30:00 PM' AS datetime)

WITH rush AS
(
SELECT
    wst.ORDERNUM
    , wst.ACCOUNTNUM
   -- , st.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'         AS KYCreatedDate
    , wst.SHIPMENTID
    , cnttbl.CONTAINERID
    , wst.MODECODE
    , wst.WAVEID
    , wst.DELIVERYNAME                                                                      AS ShipTo
    , MIN( wll.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' )    AS  ReleasedToWH
    --, wst.SHIPCONFIRMUTCDATETIME 
    , wst.SHIPMENTSTATUS                                                                    AS ShipmentStatus
    , hacs.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS ManualShippedTime
    , hacs.SHIPPERID                                                                        AS ManualShipperId
    , cnttbl.SHIPCARRIERTRACKINGNUM                                                         AS CntTrackingNum
    , cnttbl.MASTERTRACKINGNUM                                                              AS CntMasterTrackingNum
    , cnttbl.HAWHSSHIPPERID                                                                 AS CntShipperId      
    , cnttbl.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'       AS CntModifiedDateTime
    , wst.SHIPCONFIRMUTCDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'    AS KYShipConfirmDateTime
    , wst.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS KYModifiedDateTime
    , CONVERT( DECIMAL( 10, 0), SUM( cntln.QTY ) )                                            AS Units
    --, *
FROM
    WHSCONTAINERTABLE cnttbl 
    JOIN WHSCONTAINERLINE cntln ON cntln.CONTAINERID    = cnttbl.CONTAINERID    AND cnttbl.[PARTITION]  = cntln.[PARTITION]  AND cnttbl.DATAAREAID  = cntln.DATAAREAID
    JOIN WHSSHIPMENTTABLE wst   ON cnttbl.SHIPMENTID    = wst.SHIPMENTID        AND cnttbl.[PARTITION]  = wst.[PARTITION]    AND cnttbl.DATAAREAID  = wst.DATAAREAID 
    JOIN WHSLOADLINE wll        ON wll.SHIPMENTID       = wst.SHIPMENTID        AND wll.LOADID          = wst.LOADID         AND wll.ORDERNUM       = wst.ORDERNUM 
        AND wll.[PARTITION] = wst.[PARTITION] AND wst.DATAAREAID = 'ha' AND wll.ITEMID = cntln.ITEMID AND wll.INVENTDIMID = cntln.INVENTDIMID
        -- AND cntln.CONTAINERID = cnttbl.CONTAINERID AND cnttbl.SHIPMENTID = wst.SHIPMENTID
    --JOIN SALESTABLE st ON st.SALESID = wst.ORDERNUM AND st.[PARTITION] = wst.[PARTITION] AND wst.DATAAREAID = st.DATAAREAID 
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs ON hacs.CARTONID = cnttbl.CONTAINERID AND hacs.[PARTITION] = cnttbl.[PARTITION] AND cnttbl.DATAAREAID = hacs.DATAAREAID
WHERE
    wll.CREATEDDATETIME > GETUTCDATE() - @DaysToCheck
    AND wst.MODECODE IN ( '1D', '2D', '3D', 'SA', 'IE' )
    --AND wst.SHIPCONFIRMUTCDATETIME > CONCAT( @DayToCheck, '07:30:00 PM' )
    --AND wst.ORDERNUM = '********'
GROUP BY
    cnttbl.CONTAINERID, wst.SHIPMENTID, wst.LOADID, wst.ORDERNUM, wst.ACCOUNTNUM, wst.MODECODE --, st.DLVMODE, st.CREATEDDATETIME
    , CAST( wll.CREATEDDATETIME AS DATE) , wst.DELIVERYNAME, wst.SHIPCONFIRMUTCDATETIME, wst.MODIFIEDDATETIME, hacs.CREATEDDATETIME,  wst.WAVEID
    , cnttbl.SHIPCARRIERTRACKINGNUM, cnttbl.MODIFIEDDATETIME, cnttbl.HAWHSSHIPPERID, hacs.SHIPPERID, wst.SHIPMENTSTATUS, cnttbl.MASTERTRACKINGNUM
),
rushp AS
(
SELECT
    OrderNum
    , ACCOUNTNUM
    , SHIPMENTID
    , CONTAINERID
    , MODECODE
    , WAVEID
    , ShipTo
    , DATENAME(dw, ReleasedToWH)    AS DayReleased
    , ReleasedToWH
        -- , CAST( CAST( CAST(ReleasedToWH AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT Time Zone 'Eastern Standard Time' AS CalcTime
    , CASE 
        WHEN DATEPART(w, ReleasedToWH) IN (2, 3, 4, 5, 6)  -- Monday-Friday
         THEN 
            CASE
                WHEN ReleasedToWH  < CAST( CAST( CAST(ReleasedToWH AS DATE) AS nvarchar(10) ) + @CutOffTime AS datetime2) AT TIME ZONE 'Eastern Standard Time' 
                    THEN -- Before the batch jobs runs at 2:10pm
                        CAST( CAST( CAST(ReleasedToWH AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time' 
                ELSE -- released after the batch job ran(> 2:20 pm). Should go the following weekday
                   CASE 
                     WHEN DATEPART(w, ReleasedToWH) IN (2, 3, 4, 5)  -- Monday-Thursday. Should go the following day
                        THEN 
                            CAST( CAST( CAST((DATEADD(DAY, 1, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                        ELSE -- Friday. Should go on Monday
                            CAST( CAST( CAST((DATEADD(DAY, 3, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                    END
            END
         ELSE
            CASE 
                WHEN DATEPART(w, ReleasedToWH) = 7 -- Saturday
                    THEN
                        CAST( CAST( CAST((DATEADD(DAY, 2, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
                    ELSE -- Sunday
                        CAST( CAST( CAST((DATEADD(DAY, 1, ReleasedToWH )) AS DATE) AS nvarchar(10) ) + @TruckGone AS datetime2) AT TIME ZONE 'Eastern Standard Time'  
            END
    END AS "ExpectedShipTime"
    , CntTrackingNum
    , CntMasterTrackingNum
    , ShipmentStatus
    , KYShipConfirmDateTime
    , ManualShippedTime
    , CntModifiedDateTime
    , CASE 
         -- If the order was shipped manually, use that time
        WHEN ManualShippedTime IS NOT NULL THEN ManualShippedTime
        -- With tracking number updated by the AB, most probable case, or manually updated
        --WHEN LEN(RTRIM(LTRIM(CntTrackingNum))) > 0 THEN CntModifiedDateTime 
        WHEN CntTrackingNum <> '' THEN CntModifiedDateTime 
        -- Handling the cases where the work is manually completed and the shipment is not updated with the tracking number
        -- CN018220779, for example, the work was manually completed and the tracking number was not updated in the container table
        WHEN ShipmentStatus = 5 AND CntMasterTrackingNum <> '' THEN CntModifiedDateTime 
        ELSE NULL --ManualShippedTime
    END AS ActualShippedTime
    , CntShipperId
    , ManualShipperId
    , Units
FROM
    rush
),
rushf AS
(
SELECT 
    ORDERNUM
    , ACCOUNTNUM 
    , SHIPMENTID
    , CONTAINERID
    , Units
    , MODECODE  AS ShipMethod
    , ShipTo
    , WAVEID
    , DayReleased
    , ReleasedToWH       AS [ReleasedToWH_DT]
    , FORMAT(ReleasedToWH, 'MMM dd, yyyy hh:mmtt', 'en-US') AS ReleasedToWH
    , FORMAT(ExpectedShipTime, 'MMM dd, yyyy hh:mmtt', 'en-US') AS MustShipBy  
    , ManualShippedTime
    , CntTrackingNum
    , CntModifiedDateTime
    , FORMAT(ActualShippedTime, 'MMM dd, yyyy hh:mmtt', 'en-US') AS ActualShippedTime
    , ManualShipperId
    , CntShipperId
    , CASE 
        WHEN ManualShipperId IS NOT NULL THEN ManualShipperId -- Manual shipment already updated with the shipper id
        --WHEN LEN(RTRIM(LTRIM(CntShipperId))) > 0 THEN CntShipperId -- ContainerTable already updated with the shipper id
        WHEN CntShipperId <> '' THEN CntShipperId -- ContainerTable already updated with the shipper id
        ELSE NULL END AS ShipperId
    , KYShipConfirmDateTime
    , CASE 
        --WHEN DATEPART(yy,KYShipConfirmDateTime) = 1899 THEN 'No' -- Temporary  path for NULL dates
        WHEN ActualShippedTime IS NULL  THEN 'No'   
        WHEN CAST(ActualShippedTime AS DATETIME2) > CAST(ExpectedShipTime AS DATETIME2) THEN 'No' ELSE 'Yes' END AS ShippedOnTime
FROM
    rushp
)
SELECT 
    ORDERNUM            AS [OrderNum]
    --, ACCOUNTNUM        AS [AccountNum]
    --, SHIPMENTID        AS [ShipmentId]
    , Units
    , ShipMethod
    , CONTAINERID       AS [ContainerId]
    --, ShipTo
    --, WAVEID            AS [WaveId]
    --, DayReleased
    
    --, ExpectedShipTime   AS [ExpectedShipTime]
    , ReleasedToWH
    --, CntTrackingNum
    , MustShipBy
    --, ManualShippedTime
    --, CntModifiedDateTime
    , ActualShippedTime
    --, ManualShipperId
    --, CntShipperId
    , ShipperId 
    --, KYShipConfirmDateTime 
    , ShippedOnTime
FROM rushf
WHERE
    1 = 1
    --AND ShippedOnTime = 'Yes'
ORDER BY
    ReleasedToWH_DT -- to maintain the original order of the query

--sp_columns WHSCONTAINERTABLE