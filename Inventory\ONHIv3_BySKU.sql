/*
On hand inventory(Only picking area). Adding some more useful columns
*/

USE DAX_PROD;

/*

join inventtable it on vt.itemid=it.itemid
join ecoresproduct ep on it.itemid=ep.displayproductnumber
join ecoresproductcategory epc on epc.product=it.product
join ECORESCATEGORYHIERARCHY ech on epc.CATEGORYHIERARCHY=ech.recid
join ecorescategory low1 on epc.CATEGORY = low1.recid
join ecorescategory class on low1.PARENTCATEGORY=class.recid
join ECORESCATEGORY dept on class.PARENTCATEGORY=dept.recid
join ECORESCATEGORY div on dept.PARENTCATEGORY=div.recid
*/
SELECT  
    loc.zoneid			                                                    AS [ZoneId]
    , loc.locprofileid	                                                    AS [LocProfile]
    , OH.itemid					                                            AS [Item]
    , OH.inventcolorid			                                            AS [Color]
    , OH.inventsizeid			                                            AS [Size]
    , loc.wmslocationid	                                                    AS [Location]
    , CONVERT( DECIMAL( 20,0), OH.physicalinvent )			                AS [Qty]
    , CONVERT( DECIMAL( 20,0), OH.reservphysical )			                AS [Reserved]
    , OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid            AS [SKU]
    , CASE WHEN ( hagpft.AVAILABILITY = 'in stock' OR  OH.RESERVPHYSICAL > 0 ) THEN 'Yes' ELSE 'No' END  AS [Online]
    , CAST( OH.LastPickWC AS DATE )                                         AS [LastMovement]
    , DATEDIFF( DD, OH.LastPickWC, GETDATE() )                              AS [SittingDays]
     --CASE WHEN CAST( hagpft.DATETIMESTAMP AS DATE ) = CAST( GETDATE() AS DATE ) THEN 'Yes' ELSE 'No' END AS Online
FROM 
    wmslocation loc
LEFT OUTER JOIN 
    ( 
        SELECT 
            isum.itemid
            , isum.physicalinvent
            , isum.reservphysical
            , idim.inventcolorid
            , idim.inventsizeid
            , idim.wmslocationid
            , MAX( isum.MODIFIEDDATETIME ) AS LastPickWC
        FROM inventsum isum
        LEFT OUTER JOIN inventdim idim ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = idim.DATAAREAID AND isum.[PARTITION] = idim.[PARTITION]
        WHERE isum.physicalinvent > 0 AND idim.inventlocationid = '4010'
        GROUP BY
            idim.WMSLOCATIONID, isum.ITEMID, idim.INVENTCOLORID, idim.INVENTSIZEID, isum.PHYSICALINVENT, isum.RESERVPHYSICAL
    ) AS OH -- Pulls location with inventory. Avoiding duplicate records.
ON wmslocation.wmslocationID = OH.wmslocationid
LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid
WHERE 
    wmslocation.inventlocationid = '4010' 
    AND wmslocation.locprofileid LIKE '%Picking%'
    AND wmslocation.zoneid NOT IN ( 'Current Pick')
    AND OH.physicalinvent > 0 -- Only locations with inventory to be considered as candidates
--ORDER BY 
 --   SKU ASC, SittingDays ASC
 --Item, Color, Size

--sp_columns inventsum



