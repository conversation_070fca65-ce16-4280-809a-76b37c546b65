
-- This query finds when a multi pick from the same location is spread through multiple lines

SELECT
    ct.CLUSTERID
    , wkln.WORKID
    , wkln.WMSLOCATIONID
    , wkln.MODIFIEDBY
    , COUNT( * ) AS TotalPicks
FROM
    WHSWORKCLUSTERTABLE ct
    LEFT JOIN WHSWORKCLUSTERLINE cl ON ct.CLUSTERID = cl.CLUSTERID AND ct.[PARTITION] = cl.[PARTITION] AND ct.DATAAREAID = cl.DATAAREAID
    LEFT JOIN WHSWORKLINE wkln ON wkln.WORKID = cl.WORKID AND wkln.[PARTITION] = cl.[PARTITION] AND wkln.DATAAREAID = cl.DATAAREAID
    LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = wkln.WORKID AND wktbl.PARTITION = wkln.PARTITION AND wktbl.DATAAREAID = wkln.DATAAREAID
    LEFT JOIN WMSLOCATION loc ON loc.WMSLOCATIONID = wkln.WMSLOCATIONID AND loc.[PARTITION] = wkln.[PARTITION] AND loc.DATAAREAID = wkln.DATAAREAID
WHERE
    wkln.WORKSTATUS = 4
    AND ISNULL( ct.CLUSTERID, '' ) <> ''
    AND loc.LOCPROFILEID LIKE 'PalletPicking'
    AND wktbl.WORKSTATUS < 5
    AND ct.CREATEDDATETIME > GETUTCDATE() - 5
GROUP BY
    wkln.WORKID, ct.CLUSTERID, wkln.WMSLOCATIONID, wkln.MODIFIEDBY
HAVING
    COUNT( * ) > 1
ORDER BY
    ct.CLUSTERID, wkln.WMSLOCATIONID


