

SELECT *
FROM    
    WHSWorkTable wt
WHERE
    1 = 1
    AND wt.WORKSTATUS = 1 -- In process
    AND wt.WORKTRANSTYPE = 10 -- Cycle count
    AND wt.INVENTLOCATIONID = '4010'
    AND wt.MODIFIEDDATETIME < GETUTCDATE() - 1
    --AND wt.WORKID = 'WK0032503875'

-- Remove the locked user

/*
BEGIN TRAN;

UPDATE WHSWorkTable 
SET LOCKEDUSER = ''
WHERE
    1 = 1
    AND WORKSTATUS = 1 -- In process
    AND WORKTRANSTYPE = 10 -- Cycle count
    AND INVENTLOCATIONID = '4010'
    AND MODIFIEDDATETIME < GETUTCDATE() - 1

COMMIT TRAN;

*/