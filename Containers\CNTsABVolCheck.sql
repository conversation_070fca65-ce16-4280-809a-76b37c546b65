
-- AB monitoring tool(Volume, dimensions, work templates)
-- Monitoring volume on AB Containers

DECLARE @AB_Max_Length INT = 17.90
DECLARE @AB_Max_Width INT = 12
DECLARE @AB_Max_Heigth INT = 6
DECLARE @AB_Max_Volume INT = 1198

SELECT
    wktbl.WAVEID
    
    --, cnttbl.CONTAINERTYPECODE AS CNTType
    --, wktbl.W<PERSON><PERSON><PERSON><PERSON>LATECODE    AS WorkTemplate
    , cnttbl.ContainerID
    , wktbl.WORKID
    , shptbl.ADDRESS    AS ShipmentAddress
   
    , CASE    
        WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wktbl.CREATEDDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wktbl.CREATEDDATETIME ) -- No DST
        WHEN DATEPART( mm, wktbl.CREATEDDATETIME ) = 3  THEN 
            CASE   WHEN DATEPART( dd, wktbl.CREATEDDATETIME ) - DATEPART( w, wktbl.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wktbl.CREATEDDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wktbl.CREATEDDATETIME )
             END
        WHEN DATEPART( dd, wktbl.CREATEDDATETIME ) - DATEPART( w, wktbl.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wktbl.CREATEDDATETIME )
        ELSE DATEADD( hh, - 4, wktbl.CREATEDDATETIME )
    END                             AS CreatedDateTime
  
    , SUM( CONVERT( DECIMAL( 10, 2 ), physdim.DEPTH * physdim.WIDTH * physdim.HEIGHT * cntln.Qty, 1 ) ) AS Volume
    , MAX( CONVERT( DECIMAL( 10, 2 ), CASE WHEN physdim.DEPTH >= physdim.WIDTH THEN physdim.DEPTH   ELSE physdim.WIDTH END, 1 ) )   AS "MaxLength"
    , MAX( CONVERT( DECIMAL( 10, 2 ), CASE WHEN physdim.DEPTH < physdim.WIDTH   THEN physdim.DEPTH  ELSE physdim.WIDTH END, 1 ) )   AS "MaxDepth"
    , MAX( CONVERT( DECIMAL( 10, 2 ), physdim.HEIGHT ) ) AS MaxHeight
  
   
FROM WHSCONTAINERTABLE cnttbl
LEFT JOIN WHSCONTAINERLINE cntln        ON cnttbl.CONTAINERID = cntln.CONTAINERID   AND cntln.[PARTITION] = cnttbl.PARTITION                AND cnttbl.DATAAREAID = 'ha'
LEFT JOIN WHSWORKTABLE wktbl            ON wktbl.CONTAINERID = cnttbl.CONTAINERID   AND cntln.[PARTITION] = wktbl.PARTITION                 AND wktbl.DATAAREAID = 'ha'
LEFT JOIN INVENTDIM idim                ON idim.INVENTDIMID = cntln.INVENTDIMID     AND cntln.[PARTITION] = idim.PARTITION                  AND idim.DATAAREAID = 'ha'
LEFT JOIN WHSPHYSDIMUOM physdim         ON physdim.ITEMID = cntln.ITEMID            AND physdim.ECORESITEMCOLORNAME = idim.INVENTCOLORID    AND physdim.ECORESITEMSIZENAME = idim.INVENTSIZEID
LEFT JOIN WHSSHIPMENTTABLE shptbl       ON wktbl.SHIPMENTID = shptbl.SHIPMENTID     AND wktbl.DATAAREAID = shptbl.DATAAREAID                AND wktbl.[PARTITION] = shptbl.[PARTITION]
LEFT JOIN LOGISTICSPOSTALADDRESS lpo    ON LPO.RECID = shptbl.DELIVERYPOSTALADDRESS AND lpo.[PARTITION] = shptbl.[PARTITION]
LEFT JOIN WHSWAVETABLE wavtbl           ON wavtbl.WAVEID = wktbl.WAVEID             AND wavtbl.[PARTITION] = wktbl.[PARTITION]               AND wavtbl.DATAAREAID = 'ha'
--LEFT JOIN WHSWAVETEMPLATELINE wavtl ON wavtbl.WAVETEMPLATENAME = wavtl.WAVETEMPLATENAME AND wavtl.[PARTITION] = wavtbl.[PARTITION] AND wavtl.DATAAREAID = 'ha'
WHERE 
   --cnttbl.CONTAINERID IN ('CN014072815', 'CN014072537') AND
    --wktbl.WAVEID = 'WV000203168' AND
     wktbl.CREATEDDATETIME BETWEEN GETUTCDATE() - 1 AND GETUTCDATE()
     AND cnttbl.CONTAINERTYPECODE ='AB'
     AND wktbl.WORKTEMPLATECODE = '4010 Direct'
     AND shptbl.HABANDERSHIPMENT = 1 
     AND lpo.CITY NOT IN ('APO', 'FPO', 'DPO' )
     AND lpo.[STATE] NOT IN ( 'AK', 'HI' )
     AND wavtbl.WAVEATTRIBUTECODE = 'AutoBagger'
GROUP BY
    wktbl.WAVEID, cnttbl.CONTAINERID,  wktbl.CREATEDDATETIME, wktbl.WORKID, shptbl.ADDRESS
        
--HAVING SUM( CONVERT( DECIMAL( 10, 2 ), physdim.DEPTH * physdim.WIDTH * physdim.HEIGHT * cntln.Qty, 1 ) ) BETWEEN 100 AND @AB_Max_Volume