
-- The tiger on AX 2012

(@P1 bigint,@P2 nvarchar(5),@P3 bigint,@P4 nvarchar(5),@P5 bigint)
SELECT 
	T1.ITEMID,T1.INVENTDIMID,T1.RECID 
FROM 
	INVENTSUM T1 WITH ( UPDLOCK) CROSS JOIN INVENTSUMDELTADIM T2 
WHERE 
	((T1.PARTITION=@P1) 
	AND (T1.DATAAREAID=@P2)) 
	AND (((T2.PARTITION=@P3) 
	AND (T2.DATAAREAID=@P4)) 
	AND (((T2.ITEMID=T1.ITEMID) 
	AND (T2.INVENTDIMID=T1.INVENTDIMID)) 
	AND (T2.TTSID=@P5)))