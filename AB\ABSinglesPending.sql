-- AB Singles pending 

USE DAX_PROD

SELECT 
        haig.G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ITEMNUMBER      AS UPC
        --,itin.G<PERSON><PERSON><PERSON>LTRADEITEMNUMBER    AS UPC
        , wt.ORDERNUM                     AS OrderNum
        , wt.CONTAINERID                  AS Container
        , CAST( wll.CREATEDDATETIME AS DATE ) AS ReleasedToWH -- Open orders date
        , CASE WHEN wt.WORKSTATUS = 0 THEN 'Open' ELSE 'In progress' END AS WorkStatus
        , wkcl.CLUSTERID
        , CASE 
                WHEN OQ.MHXOUTBOUNDSTATUS       = 0 THEN 'Created'
                WHEN OQ.MHXOUTBOUNDSTATUS       = 5 THEN 'Sent'
                WHEN OQ.MHXOUTBOUNDSTATUS       = 7 THEN 'Manifesting'
                WHEN OQ.MHXOUTBOUNDSTATUS       = 6 THEN 'Error'
                ELSE 'Unknown'
        END AS MHXStatus
        , wt.WAVEID                       AS WaveID
        , wll.ITEMID                      AS Item
        , id.INVENTCOLORID                AS Color
        , id.INVENTSIZEID                 AS Size       
       -- , ct.SHIPCARRIERTRACKINGNUM
        , ct.MASTERTRACKINGNUM
FROM WHSWORKTABLE wt
INNER JOIN WHSLOADLINE wll ON wll.LOADID = wt.LOADID AND wll.ORDERNUM = wt.ORDERNUM AND wll.[PARTITION] = wt.[PARTITION] AND wll.DATAAREAID = wt.DATAAREAID
INNER JOIN INVENTDIM id ON wll.INVENTDIMID = id.INVENTDIMID AND id.[PARTITION] = wll.[PARTITION] AND id.DATAAREAID = 'ha'
--LEFT JOIN INVENTITEMGTIN itin ON itin.ITEMID = wl.ITEMID AND itin.INVENTDIMID = wl.INVENTDIMID
INNER JOIN HAINVENTITEMGTIN haig ON wll.ITEMID = haig.ITEMID AND id.INVENTCOLORID = haig.INVENTCOLORID AND id.INVENTSIZEID = haig.INVENTSIZEID AND haig.[PARTITION] = wll.[PARTITION] AND haig.DATAAREAID = 'ha'
INNER JOIN WHSCONTAINERTABLE ct ON wt.CONTAINERID = ct.CONTAINERID AND ct.[PARTITION] = wt.[PARTITION] AND ct.DATAAREAID = wt.DATAAREAID
INNER JOIN WHSWORKCLUSTERLINE wkcl ON wkcl.WORKID = wt.WORKID AND wkcl.[PARTITION] = wt.[PARTITION] AND wkcl.DATAAREAID = wt.DATAAREAID
LEFT JOIN MHXOutboundQueue OQ ON wt.CONTAINERID = OQ.MHXDATA1 AND wt.[PARTITION] = OQ.[PARTITION] AND wt.DATAAREAID = OQ.DATAAREAID
WHERE	wt.WORKSTATUS < 2 -- Open or In progress
        --AND wt.WORKTRANSTYPE = 2 -- Sales Order
        AND wt.WORKTEMPLATECODE= N'4010 AutoBagger Sing'  -- Only AB Singles
        AND ISNULL( ct.SHIPCARRIERTRACKINGNUM, N''  ) = N'' -- Not manually shipped
        -- Work created in the last 7 days
        AND wt.CREATEDDATETIME BETWEEN GETUTCDATE() - 7 AND DATEADD( MINUTE, -5, GETUTCDATE() )
        AND OQ.MHXOUTBOUNDTRANSTYPE = 10  -- Work Creation
	AND ISNULL( OQ.MHXDATA5, '' ) <> '' -- Avoiding duplicate containers and getting the wave number
       -- AND wl.LINENUM = 1 -- Only the pick line( Item, color, size )

/*ORDER BY 
        CAST( wll.CREATEDDATETIME AS DATE )
        , wkcl.CLUSTERID*/
