
select 
    r.[RO<PERSON><PERSON><PERSON>_SCHEMA]
    , r.[ROUTINE_NAME]
    , r.[ROUTINE_TYPE]
    , p.create_date [CREATED_DATE]
    , p.modify_date [MODIFIED_DATE]
    , cast(e.value as nvarchar(max)) [DESCRIPTION] 
from 
    [INFORMATION_SCHEMA].[ROUTINES] r 
    join sys.schemas s on s.name = r.[ROUTINE_SCHEMA] 
    join sys.objects p on p.name = r.[ROUTINE_NAME] and p.schema_id = s.schema_id and p.parent_object_id = 0 
    left outer join sys.extended_properties e on p.object_id = e.major_id and e.minor_id = 0 and e.class = 1 and e.name = 'MS_Description' 
where 
    1=1 and 1=1