

SELECT TOP 30
    CAST(shptbl.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE) AS ShippedDate
    --, shptbl.SHIPMENTID
    --, shptbl.ORDERNUM
    --, SUM(CASE WHEN shptbl.HASINGLESKU = 1 THEN 1 ELSE 0 END) AS Single
    --, SUM(CASE WHEN shptbl.HASINGLESKU = 0 THEN 1 ELSE 0 END) AS Multiple
    , COUNT(*) AS GiftNotesPrinted
FROM 
    WHSSHIPMENTTABLE shptbl
WHERE 
    1 = 1
    AND shptbl.LOADDIRECTION = 2
    AND shptbl.INVENTLOCATIONID = '4010'
    AND shptbl.WORKTRANSTYPE = 2
    AND shptbl.DATAAREAID = 'ha'
    AND shptbl.[PARTITION] = 5637144576
    AND shptbl.HAQUERYGIFTNOTE = 1 -- Gift note printed
    --AND shptbl.HASINGLESKU = 0 AND shptbl.HASINGLEL<PERSON><PERSON><PERSON><PERSON><PERSON>HIPMENT = 0
GROUP BY 
    CAST(shptbl.<PERSON><PERSON><PERSON>IED<PERSON><PERSON><PERSON>ME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS DATE)
ORDER BY 
    ShippedDate 
