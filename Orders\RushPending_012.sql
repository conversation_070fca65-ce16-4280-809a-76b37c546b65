
-- Find Pending Rush orders(not shipped)
-- 2/17/2025 - v1 - PowerBI

WITH rush AS
(
SELECT
    wst.ORDERNUM
  --, wst.ACCOUNTNUM
    , wst.SHIPMENTID
    , CASE WHEN wst.HALOCDIRECTFAIL = 1 THEN 'Yes' ELSE 'No' END AS LDF
    , cnttbl.CONTAINERID
    , wktbl.WORKID
    --, wktbl.WORKID
    , CASE WHEN wktbl.FROZEN = 1 THEN 'Yes' ELSE 'No' END   AS WkBlocked
    , CASE 
        WHEN wktbl.WORKSTATUS = 0 THEN 'Open' 
        WHEN wktbl.WORKSTATUS = 1 THEN 'In process'
        WHEN wktbl.WORKSTATUS = 4 THEN 'Closed'
        ELSE 'Unknown'
    END AS WkStatus
    , wkusr.USERNAME
    , CASE WHEN ISNULL(wkcl.CLUSTERID, '') = '' THEN 'N/A' ELSE wkcl.CLUSTERID END AS 'ClusterId'
    , CONVERT( DECIMAL( 10, 0), SUM( cntln.QTY ) )                                          AS Units
    , wst.MODECODE
    , wst.WAVEID
    --, wst.DELIVERYNAME                                                                      AS ShipTo
    --, hacs.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS ActualShippedTime
    --, *
FROM
    WHSCONTAINERTABLE cnttbl 
    JOIN WHSCONTAINERLINE cntln ON cntln.CONTAINERID = cnttbl.CONTAINERID AND cnttbl.[PARTITION] = cntln.[PARTITION] AND cnttbl.DATAAREAID = cntln.DATAAREAID
    JOIN WHSSHIPMENTTABLE wst ON cnttbl.SHIPMENTID = wst.SHIPMENTID AND cnttbl.[PARTITION] = wst.[PARTITION] AND cnttbl.DATAAREAID = wst.DATAAREAID 
    JOIN WHSWORKTABLE wktbl ON wktbl.CONTAINERID = cnttbl.CONTAINERID AND wktbl.DATAAREAID = cnttbl.DATAAREAID AND wktbl.[PARTITION] = cnttbl.[PARTITION]
    LEFT JOIN WHSWORKCLUSTERLINE wkcl ON wkcl.WORKID = wktbl.WORKID AND wkcl.[PARTITION] = wktbl.[PARTITION] AND wkcl.DATAAREAID = wktbl.DATAAREAID
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs ON hacs.CARTONID = cnttbl.CONTAINERID AND hacs.[PARTITION] = cnttbl.[PARTITION] AND cnttbl.DATAAREAID = hacs.DATAAREAID
    LEFT JOIN WHSWORKUSER wkusr ON wkusr.USERID = wktbl.LOCKEDUSER AND wkusr.DATAAREAID = wktbl.DATAAREAID AND wkusr.[PARTITION] = wktbl.[PARTITION]
    JOIN SALESTABLE st ON st.SALESID = wst.ORDERNUM AND st.DATAAREAID = 'ha' AND st.[PARTITION] = wst.[PARTITION]
WHERE
    --wll.CREATEDDATETIME > GETUTCDATE() - @DaysToCheck
    wst.SHIPMENTSTATUS < 5 -- Not Shipped
    AND st.SALESSTATUS = 1 -- Open order
    AND (cnttbl.SHIPCARRIERTRACKINGNUM = '' AND hacs.TRACKINGNUMBER IS NULL )-- Not shipped and without tracking
    AND wst.MODECODE IN ( '1D', '2D', '3D' )
    --AND hacs.CREATEDDATETIME IS NULL -- Not shipped yet
    --AND wst.SHIPCONFIRMUTCDATETIME > CONCAT( @DayToCheck, '07:30:00 PM' )
    --AND wst.ORDERNUM = '********'
GROUP BY
    cnttbl.CONTAINERID, wst.SHIPMENTID, wst.LOADID, wst.ORDERNUM, wst.MODECODE, wst.HALOCDIRECTFAIL --, st.DLVMODE, st.CREATEDDATETIME
    , hacs.CREATEDDATETIME,  wst.WAVEID, wktbl.FROZEN, wkcl.CLUSTERID, wktbl.WORKSTATUS, wkusr.USERNAME, wktbl.WORKID
)
SELECT 
    ORDERNUM        AS [OrderNum]
    --, ACCOUNTNUM 
    , SHIPMENTID    AS [ShipmentId]
    , LDF
    , CONTAINERID   AS [ContainerId]
    , WORKID        AS [WorkId]
    , WkBlocked
    , WkStatus
    , CASE WHEN ISNULL(Username, '') = '' THEN 'N/A' ELSE Username END AS LockedBy
    , CLUSTERID     As [ClusterId]
    , Units
    , MODECODE  AS ShipMethod
    --, ShipTo
    , WAVEID    As [WaveId]
    --, DayReleased
    --, ReleasedToWH
    --, ExpectedShipTime  AS MustShipBy
    --, ActualShippedTime
    --, KYShipConfirmDateTime
    /*, --CASE WHEN DATEPART(yy,ActualShippedTime) = 1899 THEn 'No' 
      CASE WHEN ActualShippedTime IS NULL  THEN 'No'   
       WHEN CAST(ActualShippedTime AS DATETIME2) > CAST(ExpectedShipTime AS DATETIME2) THEN 'No' ELSE 'Yes' END AS ShippedOnTime*/
FROM
    rush
--ORDER BY
    --CLUSTERID
    --ORDERNUM