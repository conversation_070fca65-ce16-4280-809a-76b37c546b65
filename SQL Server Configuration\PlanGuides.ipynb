{"metadata": {"kernelspec": {"name": "SQL", "display_name": "SQL", "language": "sql"}, "language_info": {"name": "sql", "version": ""}}, "nbformat_minor": 2, "nbformat": 4, "cells": [{"cell_type": "markdown", "source": ["A plan guide in SQL Server is a feature that allows you to influence the query optimizer to use a specific execution plan for a query, even if the optimizer would normally choose a different plan. Plan guides are particularly useful for addressing performance issues caused by suboptimal query plans.\n", "\n", "  \n", "\n", "Purpose of Plan Guides\n", "\n", "Consistency: Ensure that a specific execution plan is used consistently for a query, which can be crucial for queries that are sensitive to plan changes.\n", "\n", "Performance Tuning: Force the use of a known good plan when the query optimizer might choose a less efficient plan.\n", "\n", "Parameter Sniffing: Mitigate issues related to parameter sniffing by guiding the optimizer to use a plan that is more generally efficient.\n", "\n", "Types of Plan Guides\n", "\n", "SQL Plan Guide: Applies to a specific SQL query.\n", "\n", "OBJECT Plan Guide: Applies to a specific stored procedure, function, or trigger.\n", "\n", "TEMPLATE Plan Guide: Applies to a parameterized query pattern.\n", "\n", "Finding Plan Guides in SSMS\n", "\n", "To find plan guides in SQL Server Management Studio (SSMS):\n", "\n", "  \n", "\n", "Open SSMS and Connect to Your Database:\n", "\n", "  \n", "\n", "Launch SSMS and connect to the SQL Server instance where your database resides.\n", "\n", "Navigate to Plan Guides:\n", "\n", "  \n", "\n", "In the Object Explorer, expand the database where you want to find the plan guides.\n", "\n", "Expand the \"Programmability\" folder.\n", "\n", "Expand the \"Plan Guides\" folder.\n", "\n", "You will see a list of plan guides defined for the database.\n", "\n", "Setting Up a Plan Guide\n", "\n", "To set up a plan guide, you can use the sp\\_create\\_plan\\_guide stored procedure. Here’s an example of how to create a SQL plan guide:"], "metadata": {"azdata_cell_guid": "7990e3ae-bcb6-412c-b46d-a39cd7d6a00c"}, "attachments": {}}, {"cell_type": "code", "source": ["USE YourDatabaseName;\r\n", "GO\r\n", "\r\n", "-- Create a SQL plan guide\r\n", "EXEC sp_create_plan_guide\r\n", "    @name = N'MyPlanGuide',\r\n", "    @stmt = N'SELECT T1.ITEMID, T1.INVENTDIMID, T1.RECID\r\n", "              FROM INVENTSUM T1 WITH (UPDLOCK)\r\n", "              CROSS JOIN INVENTSUMDELTADIM T2\r\n", "              WHERE ((T1.PARTITION = @P1) AND (T1.DATAAREAID = @P2))\r\n", "              AND (((T2.PARTITION = @P3) AND (T2.DATAAREAID = @P4))\r\n", "              AND (((T2.ITEMID = T1.ITEMID) AND (T2.INVENTDIMID = T1.INVENTDIMID)) AND (T2.TTSID = @P5)))',\r\n", "    @type = N'SQL',\r\n", "    @module_or_batch = NULL,\r\n", "    @params = NULL,\r\n", "    @hints = N'OPTION (USE PLAN N''XML_Execution_Plan'')';\r\n", "GO\r\n", ""], "metadata": {"azdata_cell_guid": "c0d4dadb-d19a-4895-85f2-8073b91f4934", "language": "sql"}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["Modifying or Changing a Plan Guide\n", "\n", "To modify or change a plan guide, you can use the sp\\_control\\_plan\\_guide stored procedure. Here’s an example of how to enable, disable, or drop a plan guide:\n", "\n", "  \n", "\n", "Enable a Plan Guide:"], "metadata": {"azdata_cell_guid": "4cb62855-5f0b-4ce0-bbcb-201d0834a515"}, "attachments": {}}, {"cell_type": "code", "source": ["USE YourDatabaseName;\r\n", "GO\r\n", "\r\n", "EXEC sp_control_plan_guide\r\n", "    @operation = N'ENABLE',\r\n", "    @name = N'MyPlanGuide';\r\n", "GO\r\n", ""], "metadata": {"azdata_cell_guid": "04feda51-d97c-4dad-af9e-822f7f5413e2", "language": "sql"}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["Disable a Plan Guide:"], "metadata": {"azdata_cell_guid": "f98e639c-50b2-470d-a14b-a9020256c1b2"}, "attachments": {}}, {"cell_type": "code", "source": ["USE YourDatabaseName;\r\n", "GO\r\n", "\r\n", "EXEC sp_control_plan_guide\r\n", "    @operation = N'DISABLE',\r\n", "    @name = N'MyPlanGuide';\r\n", "GO\r\n", ""], "metadata": {"azdata_cell_guid": "9a0f31bb-fce8-41c0-a095-084aa16fa1e9", "language": "sql"}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["Drop a Plan Guide:"], "metadata": {"azdata_cell_guid": "1c2f3b46-86f2-47ac-acda-2da828c2cbab"}, "attachments": {}}, {"cell_type": "code", "source": ["USE YourDatabaseName;\r\n", "GO\r\n", "\r\n", "EXEC sp_control_plan_guide\r\n", "    @operation = N'DROP',\r\n", "    @name = N'MyPlanGuide';\r\n", "GO\r\n", ""], "metadata": {"azdata_cell_guid": "8e6b847c-2a96-4f9c-a1c1-6b8f7192f2ab", "language": "sql"}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["Example: Changing a Plan Guide\n", "\n", "If you need to change the hints or the query associated with a plan guide, you typically need to drop the existing plan guide and create a new one with the desired settings.\n", "\n", "  \n", "\n", "Drop the Existing Plan Guide:"], "metadata": {"azdata_cell_guid": "4e350999-4bcb-445e-b70c-a59efad02115"}, "attachments": {}}, {"cell_type": "code", "source": ["USE YourDatabaseName;\r\n", "GO\r\n", "\r\n", "EXEC sp_control_plan_guide\r\n", "    @operation = N'DROP',\r\n", "    @name = N'MyPlanGuide';\r\n", "GO\r\n", ""], "metadata": {"azdata_cell_guid": "489ee9bd-87db-4e5e-89f1-31a5d0785754", "language": "sql"}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["Create a New Plan Guide with Updated Settings:"], "metadata": {"azdata_cell_guid": "8cd2bcb2-b841-4b76-ae21-5ffe4f3c7933"}, "attachments": {}}, {"cell_type": "code", "source": ["USE YourDatabaseName;\r\n", "GO\r\n", "\r\n", "EXEC sp_create_plan_guide\r\n", "    @name = N'MyPlanGuide',\r\n", "    @stmt = N'SELECT T1.ITEMID, T1.INVENTDIMID, T1.RECID\r\n", "              FROM INVENTSUM T1 WITH (UPDLOCK)\r\n", "              CROSS JOIN INVENTSUMDELTADIM T2\r\n", "              WHERE ((T1.PARTITION = @P1) AND (T1.DATAAREAID = @P2))\r\n", "              AND (((T2.PARTITION = @P3) AND (T2.DATAAREAID = @P4))\r\n", "              AND (((T2.ITEMID = T1.ITEMID) AND (T2.INVENTDIMID = T1.INVENTDIMID)) AND (T2.TTSID = @P5)))',\r\n", "    @type = N'SQL',\r\n", "    @module_or_batch = NULL,\r\n", "    @params = NULL,\r\n", "    @hints = N'OPTION (USE PLAN N''New_XML_Execution_Plan'')';\r\n", "GO\r\n", ""], "metadata": {"azdata_cell_guid": "665f28f4-b84e-48d6-b3c6-7344ed33083c", "language": "sql"}, "outputs": [], "execution_count": null}, {"cell_type": "markdown", "source": ["Best Practices\n", "\n", "Test Thoroughly: Always test plan guides in a development or staging environment before applying them to production.\n", "\n", "Monitor Performance: Continuously monitor the performance of queries affected by plan guides to ensure they are having the desired effect.\n", "\n", "Document Changes: Keep documentation of the plan guides you create, including the reasons for their creation and any performance metrics."], "metadata": {"azdata_cell_guid": "721f90cb-3f39-451a-81af-44c10395d92b"}, "attachments": {}}]}