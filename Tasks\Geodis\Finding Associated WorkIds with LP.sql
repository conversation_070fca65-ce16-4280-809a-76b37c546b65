
-- Finding associated work ids's to a LP


SELECT it.ITEMID, id.INVENTCOLORID, id.INVENTSIZEID, ito.REFERENCEID, wktbl.TARGETLICENSEPLATEID, it.RECID
FROM INVENTDIM id 
    LEFT JOIN INVENTTRANS it ON it.INVENTDIMID = id.INVENTDIMID AND it.[PARTITION] = id.[PARTITION] AND it.DATAAREAID = id.DATAAREAID
    LEFT JOIN INVENTTRANSORIGIN ito ON it.INVENTTRANSORIGIN = ito.RECID AND it.[PARTITION] = ito.[PARTITION] AND it.DATAAREAID = ito.DATAAREAID
    --LEFT JOIN WHSWORKLINE wkln ON wkln.INVENTTRANSID = ito.INVENTTRANSID AND wkln.[PARTITION] = ito.[PARTITION] AND wkln.DATAAREAID = ito.DATAAREAID
    LEFT JOIN WHSWORKTABLE wktbl ON wktbl.WORKID = ito.REFERENCEID AND wktbl.[PARTITION] = ito.[PARTITION] AND wktbl.DATAAREAID = ito.DATAAREAID
WHERE
    id.LICENSEPLATEID = 'G400-0092' 
    AND it.STATUSISSUE = 0
    AND id.WMSLOCATIONID = 'Outbound'
