

SELECT
    rtgctrans.CA<PERSON><PERSON><PERSON><PERSON>
    , rtgctrans.MC<PERSON><PERSON><PERSON>ID
    , rtgctrans.MCRINVOICEACCOUNT
    , rtgctrans.TRANSDATE
    , rtgctrans.MCRINVENTTRANSID
    , rtgctrans.OPERATION
    , rtgctrans.CHANNEL
    , rtgctrans.TRANSAC<PERSON>ONID
FROM 
    RETAILGIFTCA<PERSON>TRANSACTIONS rtgctrans
WHERE
    rtgctrans.MCRSALESID IN ( '********')
    --rtgctrans.CARDNUMBER = '****************'
ORDER BY
    rtgctrans.CARDNUMBER, rtgctrans.MCRSALESID



/*
SELECT *
    FROM SALESTABLE st
WHERE
    st.SALESID IN ( '********', '********')



*/