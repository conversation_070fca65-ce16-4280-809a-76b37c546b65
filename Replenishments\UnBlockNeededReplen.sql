/*
UPDATE [DAX_PROD].[dbo].[<PERSON><PERSON>WOR<PERSON><PERSON>BL<PERSON>]
SET FROZEN = 0
*/
SELECT
 *
 FROM WHSWORKTABLE
WHERE 
    FROZEN = 1
    -- FROZEN = 0
    AND WORKID IN
(
    SELECT 
        DISTINCT REPLENWORKID 
    FROM 
        WHSREPLENWORKLINK wklnk
        LEFT JOIN WHSWORKTABLE dwktbl ON dwktbl.WORKID = wklnk.DEMANDWORKID AND wklnk.DATAAREAID = 'ha' AND dwktbl.[PARTITION] = wklnk.[PARTITION]
        LEFT JOIN WHSWORKTABLE rwktbl ON rwktbl.WORKID = wklnk.REP<PERSON>NWORKID AND wklnk.DATAAREAID = 'ha' AND rwktbl.[PARTITION] = wklnk.[PARTITION]
    WHERE
        dwktbl.WORKSTATUS = 0
        AND dwktbl.FROZEN = 1
        AND rwktbl.WORKSTATUS = 0

)