-- SQL Server Performance Tuning Fast Track Video Course
-- https://www.SQLMaestros.com

-- 2_Module_2_Wait_Statistics
-- 2_1_Working_With_Wait_Stats
-- 1_Wait_Stats_Analysis.sql

-- Instance level wait analysis
select * from sys.dm_os_wait_stats
	WHERE [wait_type] NOT IN (
		N'FT_IFTSHC_MUTEX',
N'BROKER_TRANSMITTER',
N'CXCONSUMER',
N'LAZYWRITER_SLEEP',
N'BROKER_RECEIVE_WAITFOR',
N'BROKER_TASK_STOP',
N'LOGMGR_QUEUE',
N'MEMORY_ALLOCATION_EXT',
N'PARALLEL_REDO_DRAIN_WORKER',
N'PARALLEL_REDO_LOG_CACHE',
N'KSOURCE_WAKEUP',
N'PARALLEL_REDO_TRAN_LIST',
N'PARALLEL_REDO_WORKER_SYNC',
N'XE_DISPATCHER_WAIT',
N'PREEMPTIVE_OS_FLUSHFILEBUFFERS',
N'WAIT_XTP_CKPT_CLOSE',
N'PREEMPTIVE_XE_GETTARGETSTATE',
N'DIRTY_PAGE_POLL',
N'PWAIT_ALL_COMPONENTS_INITIALIZED',
N'EXECSYNC',
N'PWAIT_DIRECTLOGCONSUMER_GETNEXT',
N'CLR_SEMAPHORE',
N'QDS_ASYNC_QUEUE',
N'QDS_CLEANUP_STALE_QUERIES_TASK_MAIN_LOOP_SLEEP',
N'QDS_SHUTDOWN_QUEUE',
N'DISPATCHER_QUEUE_SEMAPHORE',
N'XE_DISPATCHER_JOIN',
N'REDO_THREAD_PENDING_WORK',
N'ONDEMAND_TASK_QUEUE',
N'FT_IFTS_SCHEDULER_IDLE_WAIT',
N'CLR_MANUAL_EVENT',
N'REQUEST_FOR_DEADLOCK_SEARCH',
N'RESOURCE_QUEUE',
N'SERVER_IDLE_CHECK',
N'QDS_PERSIST_TASK_MAIN_LOOP_SLEEP',
N'SLEEP_BPOOL_FLUSH',
N'SLEEP_DBSTARTUP',
N'SLEEP_DCOMSTARTUP',
N'WAIT_XTP_OFFLINE_CKPT_NEW_LOG',
N'SLEEP_MASTERDBREADY',
N'CHECKPOINT_QUEUE',
N'SLEEP_MASTERMDREADY',
N'XE_TIMER_EVENT',
N'BROKER_EVENTHANDLER',
N'SLEEP_MASTERUPGRADED',
N'PARALLEL_REDO_WORKER_WAIT_WORK',
N'BROKER_TO_FLUSH',
N'SLEEP_MSDBSTARTUP',
N'FSAGENT',
N'SLEEP_SYSTEMTASK',
N'SLEEP_TASK',
N'SLEEP_TEMPDBSTARTUP',
N'SNI_HTTP_ACCEPT',
N'SOS_WORK_DISPATCHER',
N'CLR_AUTO_EVENT',
N'SP_SERVER_DIAGNOSTICS_SLEEP',
N'SQLTRACE_BUFFER_FLUSH',
N'SQLTRACE_INCREMENTAL_FLUSH_SLEEP',
N'SQLTRACE_WAIT_ENTRIES',
N'VDI_CLIENT_OTHER',
N'WAIT_FOR_RESULTS',
N'WAITFOR',
N'WAITFOR_TASKSHUTDOWN',
N'WAIT_XTP_RECOVERY',
N'CHKPT',
N'WAIT_XTP_HOST_WAIT')
order by wait_time_ms DESC


-- SQL Server Performance Tuning Fast Track Video Course
-- https://www.SQLMaestros.com
