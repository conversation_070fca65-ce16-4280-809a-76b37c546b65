
-- Finding locations with duplicated sortcodes

SELECT
  PickLoc.Location      AS PickLoc
  , BulkLoc.[Location]  AS BulkLoc
  , PickLoc.SORTCODE
FROM
(
SELECT  
  wmslocation.wmslocationid	AS Location
  , wmslocation.SORTCODE
  
  --, wmslocation.zoneid			AS ZoneId
	/*
  wmslocation.locprofileid	AS LocProfile, 
	OH.itemid					AS Item, 
	OH.inventcolorid			AS Color, 
	OH.inventsizeid				AS Size, 
	OH.physicalinvent			AS Qty, 
	OH.reservphysical			AS Reserved 
  */
FROM 
  wmslocation
  LEFT OUTER JOIN 
  ( 
    SELECT DISTINCT idim.wmslocationid, isum.itemid, idim.inventcolorid, idim.inventsizeid, isum.physicalinvent, isum.reservphysical
    FROM inventsum isum
    LEFT OUTER JOIN inventdim idim ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = 'ha' AND isum.PARTITION = '5637144576'
    WHERE 
      isum.physicalinvent > 0 AND idim.inventlocationid = '4010' AND idim.DATAAREAID = 'ha'-- KY DC only
  ) as OH -- Pulls location with inventory. Avoiding duplicate records.
  ON wmslocation.wmslocationID = OH.wmslocationid
WHERE 
  wmslocation.inventlocationid = '4010' AND wmslocation.locprofileid LIKE '%Picking%' 
	AND wmslocation.zoneid NOT IN ( 'Current Pick') -- Exclude GCs, 3333s
  AND WMSLOCATION.SORTCODE > 0
) AS PickLoc

INNER JOIN
(
  SELECT  
  wmslocation.wmslocationid	AS Location
  , wmslocation.SORTCODE
  
  --, wmslocation.zoneid			AS ZoneId
	/*
  wmslocation.locprofileid	AS LocProfile, 
	OH.itemid					AS Item, 
	OH.inventcolorid			AS Color, 
	OH.inventsizeid				AS Size, 
	OH.physicalinvent			AS Qty, 
	OH.reservphysical			AS Reserved 
  */
FROM 
  wmslocation
  LEFT OUTER JOIN 
  ( 
    SELECT DISTINCT idim.wmslocationid, isum.itemid, idim.inventcolorid, idim.inventsizeid, isum.physicalinvent, isum.reservphysical
    FROM inventsum isum
    LEFT OUTER JOIN inventdim idim ON isum.inventdimid = idim.inventdimid AND isum.DATAAREAID = 'ha' AND isum.PARTITION = '5637144576'
    WHERE 
      isum.physicalinvent > 0 AND idim.inventlocationid = '4010' AND idim.DATAAREAID = 'ha'-- KY DC only
  ) as OH -- Pulls location with inventory. Avoiding duplicate records.
  ON wmslocation.wmslocationID = OH.wmslocationid
WHERE 
  wmslocation.inventlocationid = '4010' AND wmslocation.locprofileid LIKE '%Bulk%' 
	AND wmslocation.zoneid NOT IN ( 'Current Pick') -- Exclude GCs, 3333s
  AND WMSLOCATION.SORTCODE > 0
) AS BulkLoc
ON PickLoc.SORTCODE = BulkLoc.SortCode

ORDER BY PickLoc.SORTCODE, PickLoc.[Location]