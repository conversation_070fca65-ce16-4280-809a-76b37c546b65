/*
sp_lock
sp_who2
*/

USE Logistyx_Shipping

SELECT 	
	Shipper_Reference		AS Container, 
	Consignee_Reference		AS 'Order', 
	MachineName, 
	Consignee_Company		AS Company, 
	/*Consignee_Contact		AS Contact, 
	Consignee_Code			AS CustomerAccount,
	Consignee_Phone			AS Phone, 
	Consignee_Residential	AS Residential, 
	Total, Base Discount, 
	ServicePlaintext		AS ShipMethod, */
	Consignee_Address1		AS [Address],
	Consignee_City			AS [City],
	Consignee_State			AS [State],
	Consignee_PostalCode	AS [PostalCode],
	Country					AS [Country],	
	DATEADD( hh, 3, ShipTime )	AS ShipTime, 
	--Shipper, 
	s.UserName				AS Shipper, 
	/*CASE WHEN ManualShipment = 1 THEN 'True' ELSE 'False' END AS ManuallyShipped, 
	HA_Service, 
	Tracking_Number											AS Tracking,*/
	ltrim( rtrim( Tracking_Number ) )	AS Tracking 
FROM Shipments s WITH (NOLOCK)
	INNER JOIN Packages p ON s.ShipmentGUID = p.ShipmentGUID
WHERE 
	1 = 1 AND
	--Tracking_Number = '************'
	
	--Shipper_Reference IN ( 'CN016989051','CN016989042','CN016988377','CN016988724','CN016988988','CN016988951','CN016988943' )
	--Shipper_Reference LIKE ( 'CN017507540%' )
	Shipper_Reference IN ( 'CN018474652' )
	--Consignee_City = 'London'
	--Country NOT IN ('United States of America', 'United States')
	--AND ShipTime > DATEADD(DAY, -30, GETDATE())
	--OR Consignee_Company LIKE 'Megan Lopez'
	--Consignee_Address1	LIKE '1720 Fox Trail Dr%'
	--RTRIM(LTrim(Consignee_Reference)) IN ( '45533540', '0' )
	--Tracking_Number = '************'
	--ISNULL( Tracking_Number, '' ) = '' AND ShipTime > '6/22/2023'
ORDER BY
	Shipper_Reference, ShipTime


--sp_columns Shipments
	 --Consignee_Company = 'himeka hagiwara'
/*	
	AND ShipTime > '8/1/2022 04:00:00 AM'
	AND ISNULL( Consignee_Code, '' ) <> ''
*/
--	ORDER BY ShipTime ASC
/*	
SELECT st.Company, st.TotalCnt
FROM
(
SELECT 
	Consignee_Company		AS Company, 
	COUNT( * )				As TotalCnt
FROM Shipments s 
	--INNER JOIN Packages p ON s.ShipmentGUID = p.ShipmentGUID
WHERE 
	Consignee_Company = 'WINDSOR REDICK'
	AND ShipTime > '8/1/2022 03:00:00 AM'
GROUP BY Consignee_Company
) AS st
WHERE
	st.TotalCnt > 20

SELECT 
	Top 100 *
FROM 
	Shipments
WHERE 
	Consignee_Address1 LIKE '895 west End Avenue%'
	AND Consignee_Address2 LIKE 'Apt 6A'
	--AND ISNULL(Consignee_Code, '') = ''
ORDER BY
	ShipTime ASC
	
SELECT Top 2 *
FROM Packages

SELECT TOP 20 *
FROM [DAX_PROD].[dbo].[HASHIPPEDCARTONSTAGING]
WHERE 
	CARTONID IN ( 'CN018129833' )


SELECT 
	TOP 2 *
FROM 
	[DAX_PROD].[dbo].[HASHIPPEDCARTONSTAGING]
WHERE 
	CARTONID = 'CN014820751'
	--TRACKINGNUMBER IN ( '************', '1ZEX05570227170984', '1ZEX05570233403792')


SELECT TOP 2 *
FROM [DAX_PROD].[dbo].[SALESTABLE]
*/