-- Author: <PERSON>

--Pending clusters, working version
-- Modified on 7/8/2022 to align it with the new clustering proces.
-- A cluster could be associated with more than one wave
-- 2/20/2025
-- Adding the SLA Date for better filtering on the PBI dashboard
-- Changing to one line the Daylight savings calculation
-- Changing query format(leading commas)
-- 3/7/2025
-- Adding literals for the partition and dataarea on the WHERE clause
-- 3/12/2025
-- Adding the VoicePickUser and VoicePutUser to the query
-- Adding the VoiceStatus to the query
-- 3/18/2025
-- Removing orders already shipped from the query
-- 5/6/2025
-- Using DeepSeek

-- 5/12/2025: This query shows only one cluster per row
-- It chooses the oldest SLA Date

DECLARE @ReportDays INT = 20;

WITH WorkDetails AS (
    SELECT
        CL.CLUSTERID,
        WT.WORKID,
        VoiceStatus = CASE 
            WHEN vput.[STATUS] IS NULL THEN 
                CASE WHEN voicepick.USERNAME IS NULL THEN 'N/A' ELSE 'In Progress' END
            ELSE 
                CASE vput.[STATUS]
                    WHEN 0 THEN 'Pending'
                    WHEN 2 THEN 'Completed'
                    WHEN 3 THEN 'Error'
                    WHEN 4 THEN 'Reset'
                    WHEN 5 THEN 'Manually Picked'
                    WHEN 6 THEN 'Canceled'
                    ELSE 'N/A'
                END
        END,
        VoiceUser = COALESCE(vputuser.USERNAME, voicepick.USERNAME, 'N/A'),
        ReleasedToWH = CAST(ll.CREATEDDATETIME AS DATE)
    FROM WHSWORKCLUSTERLINE CL
    INNER JOIN WHSWORKTABLE WT 
        ON CL.WORKID = WT.WORKID AND WT.DATAAREAID = 'ha' AND WT.[PARTITION] = 5637144576
        AND WT.WORKSTATUS < 2 -- 0 - Open, 1 - In process
        AND WT.WORKTRANSTYPE = 2 -- Sales orders
        AND WT.WORKTEMPLATECODE LIKE '4010%' 
        AND WT.WORKTEMPLATECODE NOT LIKE '4010 W%' -- Not Wholesale
        AND WT.CREATEDDATETIME > DATEADD(DAY, -@ReportDays, GETUTCDATE())
    INNER JOIN WHSCONTAINERTABLE CNTTbl 
        ON WT.CONTAINERID = CNTTbl.CONTAINERID
        AND CNTTbl.DATAAREAID = 'ha'
        AND (CNTTbl.SHIPCARRIERTRACKINGNUM = '' OR CNTTbl.SHIPCARRIERTRACKINGNUM IS NULL)
    -- Only one row per workid    
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput 
        ON WT.WORKID = vput.WORKID AND vput.DATAAREAID = 'ha' AND vput.[PARTITION] = WT.[PARTITION]
    -- Only one row per workid
    LEFT JOIN WHSWORKUSER vputuser 
        ON vput.WORKUSER = vputuser.USERID AND vputuser.DATAAREAID = 'ha' AND vputuser.[PARTITION] = WT.[PARTITION]
    -- This was DeepSeek trying to get the firtst row for each workid from HAVOICEINTEGRATIONQUEUEPICKS
    -- It was not working as expected, so I changed it to a LEFT JOIN with a ROW_NUMBER() filter
    -- 5/13/1974
    -- Removing the Top 1 and including the GROUP BY
    -- Trying to get different stauses for the same cluster, if available
    OUTER APPLY (
        SELECT 
            --TOP 1 
            wkuser.USERNAME
        FROM HAVOICEINTEGRATIONQUEUEPICKS vip
        INNER JOIN WHSWORKUSER wkuser WITH (NOLOCK) 
            ON wkuser.USERID = vip.WORKUSER 
            AND wkuser.DATAAREAID = 'ha'
        WHERE vip.WORKID = WT.WORKID
        AND vip.STATUS < 7 AND vip.DATAAREAID = 'ha' AND vip.[PARTITION] = WT.[PARTITION]
        GROUP BY
            /*vip.WORKID, */vip.[STATUS], wkuser.USERNAME
        --ORDER BY vip.[STATUS] DESC
    ) voicepick
    /*
    -- The goal is to get the first user from HAVOICEINTEGRATIONQUEUEPICKS
    -- It was modified to use ROW_NUMBER() instead of FIRST_VALUE() for accuracy: it was duplicating units in some cases
    LEFT JOIN (
    SELECT 
        vip.WORKID,
        vip.[PARTITION],
        wkuser.USERNAME,
        ROW_NUMBER() OVER (PARTITION BY vip.WORKID ORDER BY vip.[STATUS]) AS rn
    FROM HAVOICEINTEGRATIONQUEUEPICKS vip
    INNER JOIN WHSWORKUSER wkuser WITH (NOLOCK) ON wkuser.USERID = vip.WORKUSER
            AND wkuser.DATAAREAID = 'ha'
    WHERE vip.STATUS < 5
    ) voicepick ON voicepick.WORKID = wt.WORKID 
        AND voicepick.[PARTITION] = wt.[PARTITION]
        AND voicepick.rn = 1 -- Get the first one
    */
    OUTER APPLY (
        SELECT MIN(CREATEDDATETIME) AS CREATEDDATETIME
        FROM WHSLOADLINE 
        WHERE SHIPMENTID = WT.SHIPMENTID
        AND ORDERNUM = WT.ORDERNUM
        AND LOADID = WT.LOADID
        AND DATAAREAID = 'ha'
    ) ll
),
ClusterSummary AS (
    SELECT
        CT.CLUSTERID,
        CT.CLUSTERPROFILEID,
        CT.CREATEDBY,
        CT.CREATEDDATETIME,
        WD.VoiceStatus,
        WD.VoiceUser,
        WkPending = COUNT(WD.WORKID),
        ReleasedToWH = MIN(WD.ReleasedToWH)
    FROM WHSWORKCLUSTERTABLE CT
    INNER JOIN WHSWORKCLUSTERLINE CL 
        ON CT.CLUSTERID = CL.CLUSTERID
        AND CT.DATAAREAID = 'ha'
        AND CT.[PARTITION] = 5637144576
    INNER JOIN WorkDetails WD 
        ON CL.WORKID = WD.WORKID
    GROUP BY 
        CT.CLUSTERID,
        CT.CLUSTERPROFILEID,
        CT.CREATEDBY,
        CT.CREATEDDATETIME,
        WD.VoiceStatus,
        WD.VoiceUser
),
TotalWorks AS (
    SELECT 
        CL.CLUSTERID,
        WkCount = COUNT_BIG(CL.WORKID)
    FROM WHSWORKCLUSTERLINE CL
    INNER JOIN WHSWORKTABLE WT 
        ON CL.WORKID = WT.WORKID
        AND WT.WORKTRANSTYPE = 2
        AND WT.WORKTEMPLATECODE LIKE '4010%'
        AND WT.CREATEDDATETIME > DATEADD(DAY, -@ReportDays, GETUTCDATE())
    GROUP BY CL.CLUSTERID
)
SELECT
    CS.CLUSTERID    AS [ClusterId],
    CS.CREATEDBY    AS [CreatedBy],
    FORMAT(CS.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time', 
          'MMM dd, yyyy hh:mmtt') AS [CreatedOn],
    FORMAT(CS.ReleasedToWH, 'MMM dd, yyyy') AS [SLA_Date],
    CS.VoiceStatus,
    CS.VoiceUser,
    CS.WkPending,
    TW.WkCount AS [ClusterTotal],
    CS.CLUSTERPROFILEID AS [ClusterProfile]
FROM ClusterSummary CS
INNER JOIN TotalWorks TW 
    ON CS.CLUSTERID = TW.CLUSTERID
ORDER BY 
    CS.CLUSTERID;  -- Removed SLA_Date per request
    