
-- Containers

DECLARE @ReportDays AS INTEGER = 200

SELECT
    YEAR( cnttbl.MODIFIEDDATETIME )                 AS 'Year'
    , DATENAME( month, cnttbl.MODIFIEDDATETIME )    AS 'Month'
    , cnttype.DESCRIPTION
    , COUNT( cnttbl.ContainerID )                   AS TotalCNTs
FROM
    WHSCONTAINERTABLE cnttbl
    INNER JOIN WHSCONTAINERTYPE cnttype
    ON cnttbl.CONTAINERTYPECODE = cnttype.CONTAINERTYPECODE AND cnttbl.DATAAREAID = cnttype.DATAAREAID AND cnttbl.[PARTITION] = cnttype.[PARTITION]
WHERE
    /*cnttbl.CONTAINERTYPECODE IN ('AB', 'CG', 'SM', 'MD', 'LG') -- We are using these containers types( lately )
    AND */
    cnttbl.MODIFIEDDATETIME > GETUTCDATE() - @ReportDays  -- this year
GROUP BY
    DATENAME( month, cnttbl.MODIFIEDDATETIME )
    , YEA<PERSON>( cnttbl.MODIFIEDDATETIME )
    , <PERSON><PERSON><PERSON>( cnttbl.MODIFIEDDATETIME )
    , cnttype.DESCRIPTION
ORDER BY
    YEAR( cnttbl.MODIFIEDDATETIME )
    , MONTH( cnttbl.MODIFIEDDATETIME )
    , cnttype.DESCRIPTION
