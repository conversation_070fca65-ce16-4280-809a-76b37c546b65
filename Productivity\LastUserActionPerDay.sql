
DECLARE @ReportDays INT = 7

SELECT
    StkData.USERNAME
    , CASE WHEN ISNULL( StkData.WkClassDescription, '' ) = '' THEN 'Inventory Movement' ELSE StkData.WkClassDescription END AS WorkClassDescription
    --, CONVERT( varchar(20), MAX( StkData.WorkClosedTime ), 0 ) AS LastStockerAction
     , CONVERT(varchar, MAX( StkData.WorkClosedTime), 22) AS LastStockerAction
    -- SQL 2012 and above
    --FORMAT( MAX( StkData.WorkClosedTime ), 'MM/dd/yyyy hh:mm:ss tt' ) AS LastStockerAction
FROM
(
SELECT
    wkln.WORKID
    --, wkln.USERID
    , wkuser.USERNAME
    --, wkln.WORKCLASSID
    , wkclasstbl.[DESCRIPTION] AS WkClassDescription
    ,
    CASE  WHEN DATEPART( mm, wkln.WORKCLOSEDUTCDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wkln.WORKCLOSEDUTCDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wkln.WORKCLOSEDUTCDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME ) -- No DST
        WHEN DATEPART( mm, wkln.WORKCLOSEDUTCDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) < 8 THEN DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME ) -- No DST
                    WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) > 14 THEN  DATEADD( hh, - 4, wkln.WORKCLOSEDUTCDATETIME ) -- DST
                    WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wkln.WORKCLOSEDUTCDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wkln.WORKCLOSEDUTCDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wkln.WORKCLOSEDUTCDATETIME )
             END
        WHEN DATEPART( dd, wkln.WORKCLOSEDUTCDATETIME ) - DATEPART( w, wkln.WORKCLOSEDUTCDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wkln.WORKCLOSEDUTCDATETIME )
        ELSE DATEADD( hh, - 4, wkln.WORKCLOSEDUTCDATETIME )
    END AS WorkClosedTime
FROM
    WHSWORKLINE wkln
    LEFT JOIN WHSWORKUSER wkuser ON wkln.USERID = wkuser.USERID AND wkln.[PARTITION] = wkuser.[PARTITION] AND wkln.DATAAREAID = wkuser.[DATAAREAID]
    LEFT JOIN WHSWORKCLASSTABLE wkclasstbl ON wkln.WORKCLASSID = wkclasstbl.WORKCLASSID AND wkln.[PARTITION] = wkclasstbl.[PARTITION] AND wkln.DATAAREAID = wkclasstbl.DATAAREAID
WHERE
    wkln.WORKTYPE = 2
    AND ( wkln.WORKCLASSID IN ('Bulk Pull', 'Fwd Throw', 'Movement', 'Returns', 'RecPutaway', 'BulkPut', 'CycleCount' )  OR ISNULL( wkln.WorkClassID, '' ) = '' )
    AND wkln.WORKSTATUS = 4
    AND wkln.MODIFIEDDATETIME > GETUTCDATE() - @ReportDays
    AND wkln.MODIFIEDBY = 'wfexc'
    --AND wkln.Userid = 'besl'
    --AND CAST( wkln.WORKCLOSEDUTCDATETIME AS DATE ) = '01/03/2023'
) AS StkData
GROUP BY
    CAST( WorkClosedTime AS DATE ), USERNAME, WkClassDescription
ORDER BY
    CAST( WorkClosedTime AS DATE )
    , USERNAME
    , WkClassDescription
     

