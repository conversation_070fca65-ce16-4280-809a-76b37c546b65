

SELECT 
    --TOP 20 
    * 
FROM 
    WHSWORKER ww
LEFT JOIN HCMWORKER hcmw ON ww.WORKER = hcmw.RECID
/*

SELECT TOP 20 * FROM USERINFO WHERE id = 'labreu'
SELECT TOP 20 * FROM DIRPARTYTABLE WHERE FILENUMBER_SA IS NOT NULL

SELECT TOP 20 * FROM DIRPERSONNAME WHERE RECID = 5639268558
SELECT
    TOP 20 *
FROM    
    WHSWORKUSERWAREHOUSE


*/

SELECT 
    TOP 20 * 
FROM 
    WHSWORKUSER wwu
    LEFT JOIN WHSWORKER ww ON wwu.WORKER = ww.RECID
    LEFT JOIN HCMWORKER hcmw ON ww.WORKER = hcmw.RECID
WHERE
    wwu.USERID = 'dgz'
