--Pending AB orders
SELECT wt.OR<PERSON>RNUM AS OrderNum, wt.CONTAINERID AS Container, wt.WOR<PERSON><PERSON>, 
	CASE	WHEN wt.WORKSTATUS = 0 THEN 'Open' 
			WHEN wt.WORKSTATUS = 1 THEN 'In process'
			WHEN wt.WORKSTATUS = 2 THEN 'Combined?'
			WHEN wt.WORKSTATUS = 4 THEN 'Closed'
			ELSE 'Unknown'
			END AS Status, 
			wt.LOADID, wt.SHIPMENTID, wt.<PERSON>VE<PERSON>, wt.WORKTEMPLATECODE, 
CAST( 
CASE    WHEN DATEPART( mm, wt.CREATEDDATETIME ) IN ( 4, 5, 6, 7, 8, 9, 10 )  THEN DATEADD( hh, - 4, wt.CREATEDDATETIME ) -- Daylight Savings Months 
        WHEN DATEPART( mm, wt.CREATEDDATETIME ) IN ( 1, 2, 12 )              THEN DATEADD( hh, - 5, wt.CREATEDDATETIME ) -- No DST
        WHEN DATEPART( mm, wt.CREATEDDATETIME ) = 3 
        THEN CASE   WHEN DATEPART( dd, wt.CREATEDDATETIME ) < 8 OR DATEPART( dd, wt.CREATEDDATETIME ) > 14 THEN  DATEADD( hh, - 5, wt.CREATEDDATETIME ) -- No DST
                    WHEN DATEPART( dd, wt.CREATEDDATETIME ) - DATEPART( w, wt.CREATEDDATETIME ) + 1  >= 8 THEN  DATEADD(hh, - 4, wt.CREATEDDATETIME ) -- Last Sunday after March 8
                    ELSE DATEADD(hh, - 5, wt.CREATEDDATETIME )
             END
        WHEN DATEPART( dd, wt.CREATEDDATETIME ) - DATEPART( w, wt.CREATEDDATETIME ) + 1  >= 1 --  After first Sunday, November
        THEN DATEADD( hh, - 5, wt.CREATEDDATETIME )
        ELSE DATEADD( hh, - 4, wt.CREATEDDATETIME )
END   AS DATETIME )  AS WorkCreatedOn
FROM WHSWORKTABLE wt
WHERE	wt.WORKTRANSTYPE = 2 AND wt.WORKSTATUS < 4 AND wt.WORKTEMPLATECODE IN ('4010 AutoBagger Sing', '4010 Bander' ) AND  
		wt.CREATEDDATETIME BETWEEN DATEADD( dd, -4, GETUTCDATE() ) AND DATEADD( minute, -50, GETUTCDATE() )
ORDER BY wt.WAVEID