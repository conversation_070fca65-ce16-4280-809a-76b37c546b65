-- CycleCount Worklines
-- The query below is used to retrieve cycle count worklines from the WHSWORKLINE table.
-- It joins the WHSWORKTABLE and WHSWOR<PERSON>LINECYCLECOUNT tables to get additional information about the worklines, such as their status and item details.
-- The query filters the results based on the modified date and work type, and it also includes specific work IDs for testing purposes.
-- The results are ordered by the work ID.

-- The finding was that if the location for the cycle count is empty, the corresponding WHSWORKLINECYCLECOUNT record won't be populated.

SELECT 
    wkln.WORKID                                         AS 'WorkId'
    , wktbl.WORKPOOLID                                  AS 'WorkPool' 
    , wkln.WORKCLASSID                                  AS 'WorkClassId'
    , CASE 
        WHEN wkln.WORKSTATUS = 0 THEN 'Open'
        WHEN wkln.WORKSTATUS = 1 THEN 'In Process'
        WHEN wkln.WORKSTATUS = 2 THEN 'Pending Review'
        WHEN wkln.WORKSTATUS = 3 THEN 'Combined?'
        WHEN wkln.WORKSTATUS = 4 THEN 'Closed'
        WHEN wkln.WORKSTATUS = 5 THEN 'Cancelled'
        ELSE 'Unknown'
    END                                                 AS 'WorkLineStatus'
    ,  CASE 
        WHEN wktbl.WORKSTATUS = 0 THEN 'Open'
        WHEN wktbl.WORKSTATUS = 1 THEN 'In Process'
        WHEN wktbl.WORKSTATUS = 2 THEN 'Pending Review'
        WHEN wktbl.WORKSTATUS = 3 THEN 'Combined?'
        WHEN wktbl.WORKSTATUS = 4 THEN 'Closed'
        WHEN wktbl.WORKSTATUS = 5 THEN 'Cancelled'
        ELSE 'Unknown'
    END                                                 AS 'WorkTableStatus'
    , wkln.WMSLOCATIONID                                AS 'Location'
    , wkcc.ITEMID                                       AS 'ItemId'
    , idim.INVENTCOLORID                                AS 'Color'
    , idim.INVENTSIZEID                                 AS 'Size'
    , CAST(wkcc.QTYCOUNTED AS DECIMAL(20,0))            AS 'QtyCounted'
    , CAST(wkcc.QTYEXPECTED AS DECIMAL(20,0))           AS 'QtyExpected'

    , IIF(wkcc.CYCLECOUNTRECONCILE IS NULL, 'N/A', IIF(wkcc.CYCLECOUNTRECONCILE = 0, 'No', 'Yes'))     AS 'CycleCountReconciled'
    , IIF(wkcc.CYCLECOUNTCOUNTED IS NULL, 'N/A', IIF(wkcc.CYCLECOUNTCOUNTED = 0, 'No', 'Yes'))      AS 'CycleCountCounted'
    , IIF(wkcc.ACCEPTREJECT IS NULL, 'N/A', IIF(wkcc.ACCEPTREJECT = 0, 'No', 'Yes'))                AS 'Pending'
    , wkln.USERID                                       AS 'UserId'
    , wkln.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS 'WorkLnModifiedDate'
    , wkln.MODIFIEDBY   AS 'ModifiedBy'
    --, *
FROM 
    WHSWORKLINE wkln
    INNER JOIN  WHSWORKTABLE wktbl          ON wktbl.WORKID     = wkln.WORKID       AND wktbl.DATAAREAID = 'ha' AND wktbl.[PARTITION]   = 5637144576
    LEFT JOIN   WHSWORKLINECYCLECOUNT wkcc  ON wkcc.WORKID      = wkln.WORKID       AND wkln.DATAAREAID = 'ha'  AND wkln.[PARTITION]    = 5637144576
    LEFT JOIN   inventdim idim              ON wkcc.INVENTDIMID = idim.INVENTDIMID  AND wkcc.DATAAREAID = 'ha'  AND wkcc.[PARTITION]    = 5637144576

WHERE
    wkln.MODIFIEDDATETIME >= GETUTCDATE() - 5
    AND wkln.WORKTYPE = 3 --Cycle Count
    --AND wkcc.ACCEPTREJECT IS NULL
    --AND wkcc.ACCEPTREJECT = 0
    AND wkln.WORKSTATUS < 5 -- Not cancelled
    /*AND wkln.WORKID IN ( 'WK0034788009', 'WK0034788010', 'WK0034788224', 'WK0034788234', 'WK0034788238',  -- On the CC table
        'WK0034788148', 'WK0034788149', 'WK0034788150', 'WK0034788151', 'WK0034788152', -- Not in the CC table
        'WK0034932487', 'WK0034932488', 'WK0034932489', 'WK0034932490', 'WK0034932491', -- I completed these(0 in location)
        'WK0034929849', 'WK0034929850', 'WK0034929851', 'WK0034929852', 'WK0034929853') -- Bulk60
        */
    ORDER BY
      wkln.WORKID 
    
    --AND wkcc.WORKID IS NULL
    --AND wkln.WORKID IS NOT NULL