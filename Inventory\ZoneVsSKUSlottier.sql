
-- EcoResColor, EcoResSize, HAPLMColor, HAPLMFLOORSETSKU, EcoResProductTranslation, EcoResProduct
-- Replenishment forecast vs inventory accuracy

USE DAX_PROD

SELECT
    ONHI.SKU
    , hafc.SlotTier     AS ForecastSlotTier
    , ONHI.LOCATION
    , ONHI.ZoneId
    , ONHI.LocProfileId
    , ONHI.Qty
    , ONHI.Reserved
   -- , CASE WHEN ONHI.ZoneId = hafc.SlotTier THEN 'Yes' ELSE 'No' END AS ZoneAndSlotMatch
     , CASE WHEN LEFT( ONHI.ZoneId, 3 ) = LEFT( hafc.SlotTier, 3 ) THEN 'Yes' ELSE 'No' END AS PickCategoryMatch
    , CASE WHEN SUBSTRING( ONHI.ZoneId, 4, 1 ) = SUBSTRING( hafc.SlotTier, 4, 1 ) THEN 'Yes' ELSE 'No' END AS SizeMatch
    , CASE WHEN LEN( ONHI.ZoneId ) = 5 THEN 
        CASE WHEN LEN( hafc.SlotTier ) = 5 THEN -- Same length
            CASE WHEN RIGHT( ONHI.ZoneId, 1) = RIGHT( hafc.SlotTier, 1 ) THEN 'Yes' ELSE 'No' END 
         ELSE
            'No' -- Different length
        END
      ELSE -- 6
         CASE WHEN LEN( hafc.SlotTier ) = 6 THEN -- Same length
            CASE WHEN RIGHT( ONHI.ZoneId, 2) = RIGHT( hafc.SlotTier, 2 ) THEN 'Yes' ELSE 'No' END 
         ELSE
            'No' -- Different length
        END
      END AS VelocityMatch
    
FROM    
(
SELECT  wmslocation.zoneid			AS ZoneId, 
/*
		wmslocation.locprofileid	AS LocProfile, 
		OH.itemid					AS Item, 
		OH.inventcolorid			AS Color, 
		OH.inventsizeid				AS Size, 
*/        
		wmslocation.wmslocationid	AS 'Location', 
        wmslocation.LocProfileID,
		CONVERT( DECIMAL( 20, 0 ), OH.physicalinvent )			AS Qty, 
		CONVERT( DECIMAL( 20, 0 ), OH.reservphysical )			AS Reserved,
        OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid AS SKU
  --      CASE WHEN CAST( hagpft.DATETIMESTAMP AS DATE ) = CAST( GETDATE() AS DATE ) THEN 'Yes' ELSE 'No' END AS Online
FROM wmslocation
LEFT OUTER JOIN 
( SELECT inventsum.itemid, inventsum.physicalinvent, inventsum.reservphysical, inventdim.inventcolorid, inventdim.inventsizeid, inventdim.wmslocationid
  FROM inventsum
  LEFT OUTER JOIN inventdim
  ON inventsum.inventdimid = inventdim.inventdimid
  WHERE inventsum.physicalinvent > 0 AND inventdim.inventlocationid = '4010'
) as OH -- Pulls location with inventory. Avoiding duplicate records.
ON wmslocation.wmslocationID = OH.wmslocationid
--LEFT OUTER JOIN hagoogleproductfeedtable hagpft ON hagpft.MPN = OH.itemid + '-' + OH.inventcolorid + '-' + OH.inventsizeid
WHERE 
    wmslocation.inventlocationid = '4010' 
    AND wmslocation.locprofileid LIKE '%Picking%'
    AND wmslocation.zoneid NOT IN ( 'Current Pick', 'Zone D')
    AND OH.physicalinvent > 0 -- Only locations with inventory to be considered as candidates
--ORDER BY Item, Color, Size
) AS ONHI
LEFT JOIN 
(
    SELECT 
        Item + '-' + Color +'-' + Size_     AS SKU
        , SlotTierValue                     AS SlotTier
        , HAForecastStartDate
    FROM    
        HAFORECASTREPLENISHMENTTABLE hafc 
) AS hafc
ON ONHI.SKU = hafc.SKU
WHERE
    1 = 1 
    AND hafc.SlotTier IS NOT NULL   -- Included in the forecast only because I don't know where to find this data if the item is not forecasted
    --AND ONHI.[Location] LIKE '25-124%'
ORDER BY
    ONHI.SKU, ONHI.Location

