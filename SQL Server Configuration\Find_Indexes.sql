USE DAX_PROD

-- Replace 'INVENTSUM' with the actual table name
<PERSON><PERSON><PERSON><PERSON> @TableName NVARCHAR(128) = 'INVENTTRANS';

-- Get information about indexes for the specified table
SELECT 
    idx.name AS IndexName,
    idx.index_id AS IndexID,
    col.name AS ColumnName,
    ic.is_included_column AS IsIncludedColumn
FROM sys.indexes idx
JOIN sys.index_columns ic ON idx.object_id = ic.object_id AND idx.index_id = ic.index_id
JOIN sys.columns col ON ic.object_id = col.object_id AND ic.column_id = col.column_id
WHERE 
    idx.object_id = OBJECT_ID(@TableName)
    --AND idx.type_desc = 'NONCLUSTERED';



SELECT 
    OBJECT_NAME(i.[object_id]) AS TableName,
    i.name AS IndexName,
    c.name AS ColumnName
FROM 
    sys.indexes i
INNER JOIN 
    sys.index_columns ic ON i.[object_id] = ic.[object_id] AND i.index_id = ic.index_id
INNER JOIN 
    sys.columns c ON ic.[object_id] = c.[object_id] AND ic.column_id = c.column_id
WHERE 
    OBJECT_NAME(i.[object_id]) = @TableName;
