
SELECT
    sl.SALESID
    , sl.CUSTACCOUNT
    , sl.DELIVERYNAME
    , sl.NAME
    /*, sl.ITEMID
    , id.INVENTCOLORID      AS [Color]
    , id.INVENTSIZEID       AS [Size]*/
    , sl.ITEMID + '-' + id.INVENTCOLORID + '-' + id.INVENTSIZEID AS [SKU]
    --, CAST( sl.COSTPRICE AS DECIMAL(10, 2)) AS [CostPrice]
    , CAST(sl.QTYORDERED AS INT)   AS [Qty]
    , CAST(sl.SALESPRICE AS DECIMAL(10, 2)) AS [SalesPrice]

    --, sl.SALESCATEGORY
    , CASE 
        WHEN sl.SALESSTATUS = 1 THEN 'Open'
        WHEN sl.SALESSTATUS = 2 THEN 'Delivered'
        WHEN sl.SALESSTATUS = 3 THEN 'Invoiced'
        ELSE 'Unknown'
      END   AS [LineStatus]
    , CASE 
        WHEN sl.SALESTYPE = 3 THEN 'Sales Order'
        WHEN sl.SALESTYPE = 4 THEn 'Returned Order'
        ELSE 'Unknown'
      END AS  [OrderType]
    --, st.HAISGIFT
    , CASE WHEN sl.HAGIFTGROUP = 1 THEN 'Yes' ELSE 'No'  END   AS [GiftOrder]
    --, sl.HAGIFTBUYERNAME
    --, sl.HAGIFTRECIPIENTNAME
    --, sl.HAGIFTNOTES
    --, sl.HACSLNOTES
    , sl.MODIFIEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'  AS [KY_ModifiedDateTime]
FROM
    salesline sl
JOIN inventdim id ON sl.inventdimid = id.inventdimid AND sl.DATAAREAID = id.DATAAREAID AND sl.[PARTITION] = id.[PARTITION]
JOIN SALESTABLE st ON st.SALESID = sl.SALESID AND st.DATAAREAID = sl.DATAAREAID AND st.[PARTITION] = sl.[PARTITION]
WHERE
    sl.ITEMID IN ('81969', '81970')
    --sl.ITEMID IN ('80939')
    AND sl.MODIFIEDDATETIME > '2/12/2024'
    --AND id.INVENTCOLORID IN ('1FZ', '1RR', 'VZ2')
    --AND id.INVENTCOLORID IN ('XC8')
    AND sl.SALESTYPE =  3  -- Sales Orders
ORDER BY
    SKU, sl.SALESID

--sp_columns salesline