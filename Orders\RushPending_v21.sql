
-- Find Pending Rush orders(not shipped)
-- Used in the PBI report: Rush Orders Pending
-- Added Voice user on the query - 3/5/2025
-- Added some of the <PERSON>'s suggestions - 3/5/2025

WITH rush AS
(
SELECT
    wst.ORDERNUM
  --, wst.ACCOUNTNUM
    , wst.SHIPMENTID
    , IIF(wst.HALOCDIRECTFAIL = 1, 'Yes', 'No')     AS [LDF]
     , COALESCE(cnttbl.CONTAINERID, 'N/A')          AS [ContainerId]
     , COALESCE(wktbl.WORKID, 'N/A')                AS [WORKID]
    --, wktbl.WORKID
    , CASE 
            WHEN wktbl.FROZEN = 1 THEN 'Yes' 
            WHEN wktbl.FROZEN = 0 THEN 'No' 
            ELSE 'N/A' 
        END                                         AS [WkBlocked]
    , CASE 
        WHEN wktbl.WORKSTATUS = 0 THEN 'Open' 
        WHEN wktbl.WORKSTATUS = 1 THEN 'In process'
        WHEN wktbl.WORKSTATUS = 4 THEN 'Closed'
        ELSE 'N/A'
    END AS [WkStatus]
    , wkusr.USERNAME                                AS [LockedUser]
    , (SELECT TOP 1 -- TOP 1 because there could be more than one line
        wkuser.USERNAME
        FROM HAVOICEINTEGRATIONQUEUEPICKS vip 
        LEFT JOIN WHSWORKUSER wkuser WITH (NOLOCK) ON wkuser.USERID = vip.WORKUSER 
        WHERE 
            vip.WORKID = wktbl.WORKID AND vip.STATUS < 7 -- 0-Pending, 2-Completed, 4-Resets, 5 - Manually Picked, 6 - Canceled
            AND vip.[PARTITION]     = ********** AND vip.DATAAREAID     = 'ha'
            AND wkuser.[PARTITION]  = ********** AND wkuser.DATAAREAID  = 'ha'
        ORDER BY vip.[STATUS]    -- 0 - Pending first
        ) AS [VoicePickUser]
    , vput.[STATUS]             AS [PutStatus]
    , wkusr1.USERNAME           AS [VoicePutUser]
    , IIF(ISNULL(wkcl.CLUSTERID, '') = '','N/A', wkcl.CLUSTERID)            AS 'ClusterId'
    , IIF(ISNULL(wkct.CLUSTERID, '') = '', 'N/A', wkct.CLUSTERPROFILEID)    AS 'ClusterProfile'
    , IIF(ISNULL(cnttbl.CONTAINERID, '') = '', 0, CONVERT( DECIMAL( 10, 0), SUM( cntln.QTY ) ) )                                         AS CntUnits
    , wst.MODECODE
    --, st.DLVMODE
    , wst.WAVEID
    , wvt.WAVETEMPLATENAME
    , (SELECT CAST(SUM(ll.QTY) AS INT) FROM WHSLOADLINE ll WHERE ll.SHIPMENTID = wst.SHIPMENTID AND ll.ORDERNUM = st.SALESID AND ll.LOADID = wst.LOADID AND ll.DATAAREAID = 'ha') AS LdUnits
    , (SELECT MIN(ll.CREATEDDATETIME) FROM WHSLOADLINE ll WHERE ll.SHIPMENTID = wst.SHIPMENTID AND ll.ORDERNUM = st.SALESID AND ll.LOADID = wst.LOADID  AND ll.DATAAREAID = 'ha') AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time' AS [ReleasedToWH]

    --, wst.DELIVERYNAME                                                                      AS ShipTo
    --, hacs.CREATEDDATETIME AT TIME ZONE 'UTC' AT TIME ZONE 'Eastern Standard Time'          AS ActualShippedTime
    , IIF(wktbl.WORKID IS NULL, 'N/A', IIF(EXISTS (SELECT '1' FROM WHSREPLENWORKLINK WHERE DEMANDWORKID= wktbl.WORKID AND DATAAREAID= 'ha'), 'Yes', 'No')) AS [NeedsReplen]
    --, hacs.TRACKINGNUMBER
FROM
    SALESTABLE st
    -- Taking into account orders with shipments created
    JOIN WHSSHIPMENTTABLE wst                   WITH (NOLOCK) ON st.SALESID = wst.ORDERNUM                  
    JOIN WHSWAVETABLE wvt                       WITH (NOLOCK) ON wvt.WAVEID = wst.WAVEID     
    -- Using literals for the partition and dataareaid               
    LEFT JOIN WHSCONTAINERTABLE cnttbl          WITH (NOLOCK) ON wst.SHIPMENTID     = cnttbl.SHIPMENTID     AND cnttbl.[PARTITION]  = ********** AND cnttbl.DATAAREAID  = 'ha'        
    LEFT JOIN WHSCONTAINERLINE cntln            WITH (NOLOCK) ON cntln.CONTAINERID  = cnttbl.CONTAINERID    AND cntln.[PARTITION]   = ********** AND cntln.DATAAREAID   = 'ha'    
    LEFT JOIN WHSWORKTABLE wktbl                WITH (NOLOCK) ON wktbl.CONTAINERID  = cnttbl.CONTAINERID    AND wktbl.[PARTITION]   = ********** AND wktbl.DATAAREAID   = 'ha'   
    LEFT JOIN WHSWORKCLUSTERLINE wkcl           WITH (NOLOCK) ON wkcl.WORKID        = wktbl.WORKID          AND wkcl.[PARTITION]    = ********** AND wkcl.DATAAREAID    = 'ha'                
    LEFT JOIN WHSWORKCLUSTERTABLE wkct          WITH (NOLOCK) ON wkcl.CLUSTERID     = wkct.CLUSTERID        AND wkct.[PARTITION]    = ********** AND wkct.DATAAREAID    = 'ha'          
    LEFT JOIN HASHIPPEDCARTONSTAGING hacs       WITH (NOLOCK) ON hacs.CARTONID      = cnttbl.CONTAINERID    AND hacs.[PARTITION]    = ********** AND hacs.DATAAREAID    = 'ha'       
    LEFT JOIN WHSWORKUSER wkusr                 WITH (NOLOCK) ON wkusr.USERID       = wktbl.LOCKEDUSER      AND wkusr.[PARTITION]   = ********** AND wkusr.DATAAREAID   = 'ha'           
    LEFT JOIN HAVOICEINTEGRATIONQUEUEPUTS vput  WITH (NOLOCK) ON wktbl.WORKID       = vput.WORKID           AND vput.[PARTITION]    = ********** AND vput.DATAAREAID    = 'ha'                
    LEFT JOIN WHSWORKUSER wkusr1                WITH (NOLOCK) ON wkusr1.USERID      = vput.WORKUSER         AND wkusr1.[PARTITION]  = ********** AND wkusr1.DATAAREAID  = 'ha'           
        --AND wktbl.LOCKEDUSER <> ''
WHERE
    --wll.CREATEDDATETIME > GETUTCDATE() - @DaysToCheck
    wst.SHIPMENTSTATUS < 5 -- Not Shipped
    AND st.SALESSTATUS = 1 -- Open order
    AND (cnttbl.SHIPCARRIERTRACKINGNUM = '' OR cnttbl.SHIPCARRIERTRACKINGNUM IS NULL) -- Not shipped(including the tracking number added to the table) or tracking number not available
    AND hacs.TRACKINGNUMBER IS NULL -- Not shipped yet
    AND wst.MODECODE IN ( '1D', '2D', '3D', 'SA', 'IE' ) -- 1st Day, 2nd Day, 3rd Day, Saturday, International Economy
    -- Using literals for the partition and dataareaid
    AND st.[PARTITION]      = ********** AND st.DATAAREAID     = 'ha'
    AND wst.[PARTITION]     = ********** AND wst.DATAAREAID     = 'ha'
    AND wvt.[PARTITION]     = ********** AND wvt.DATAAREAID     = 'ha'
    
    --AND wst.SHIPCONFIRMUTCDATETIME > CONCAT( @DayToCheck, '07:30:00 PM' )
    --AND wst.ORDERNUM = '44426351'
GROUP BY
    cnttbl.CONTAINERID, wst.SHIPMENTID, wst.LOADID, wst.ORDERNUM, wst.MODECODE, wst.HALOCDIRECTFAIL --, st.DLVMODE, st.CREATEDDATETIME
    , hacs.CREATEDDATETIME,  wst.WAVEID, wktbl.FROZEN, wkcl.CLUSTERID, wktbl.WORKSTATUS, wkusr.USERNAME, wktbl.WORKID
    , wkct.CLUSTERID, wkct.CLUSTERPROFILEID, wvt.WAVETEMPLATENAME, st.SALESID, hacs.TRACKINGNUMBER, wktbl.[PARTITION], vput.[STATUS], wkusr1.USERNAME
)
SELECT 
    ORDERNUM                                                AS [OrderNum]
    , MODECODE                                              AS [ShipMethod]
    , FORMAT(ReleasedToWH, 'MMM dd yyyy hh:mmtt', 'en-US')  AS [ReleasedToWH]
    , CASE WHEN CntUnits > 0 THEN CntUnits ELSE LdUnits END AS [Units] -- Use the container units if available
    , SHIPMENTID                                            AS [ShipmentId]
    , LDF                                                   AS [LDF]
    , CONTAINERID                                           AS [ContainerId]
    , WORKID                                                AS [WorkId]
    , NeedsReplen                                           AS [NeedsReplen]
    , WkBlocked                                             AS [WorkBlocked]
    , WkStatus                                              AS [WorkStatus]
    , COALESCE(LockedUser,'N/A')                            AS [LockedBy]
    , COALESCE(VoicePutUser, VoicePickUser,'N/A')           AS [VoiceUser]
    , CASE 
        WHEN PutStatus IS NULL THEN CASE WHEN VoicePickUser IS NULL THEN 'N/A' ELSE 'In Progress' END --
        ELSE 
            CASE 
                WHEN PutStatus = 0 THEN 'Pending'
                WHEN PutStatus = 2 THEN 'Completed'
                WHEN PutStatus = 3 THEN 'Error' -- Not sure if this is the correct status
                WHEN PutStatus = 4 THEN 'Reset'
                WHEN PutStatus = 5 THEN 'Manually Picked'
                WHEN PutStatus = 6 THEN 'Canceled'
                ELSE 'N/A'
            END END AS [VoiceStatus]
    , CLUSTERID                                             AS [ClusterId]
    , ClusterProfile                                        AS [ClusterProfile]
    , WAVEID                                                AS [WaveId]
    , [WAVETEMPLATENAME]                                    AS [WaveTemplate]
FROM
    rush
ORDER BY
    ReleasedToWH, OrderNum

/*
SELECT TOP 1 * 
FROM HAVOICEINTEGRATIONQUEUEPICKS
WHERE STATUS = 0
ORDER BY CREATEDDATETIME DESC

SELECT TOP 1 * 
FROM HAVOICEINTEGRATIONQUEUEPUTS
--WHERE STATUS = 3
ORDER BY CREATEDDATETIME DESC
*/